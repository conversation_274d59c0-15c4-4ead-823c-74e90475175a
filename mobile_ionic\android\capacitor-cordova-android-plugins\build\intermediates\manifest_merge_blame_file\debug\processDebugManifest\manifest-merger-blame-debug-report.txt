1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:amazon="http://schemas.amazon.com/apk/res/android"
3    xmlns:android="http://schemas.android.com/apk/res/android"
4    package="capacitor.cordova.android.plugins" >
5
6    <uses-sdk android:minSdkVersion="23" />
7
8    <uses-permission android:name="android.permission.WAKE_LOCK" />
8-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:7:1-63
8-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:7:18-61
9
10    <application android:usesCleartextTraffic="true" >
10-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:4:1-6:15
10-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:4:15-50
11        <service android:name="de.appplant.cordova.plugin.background.ForegroundService" />
11-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:5:1-82
11-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:5:10-80
12    </application>
13
14</manifest>
