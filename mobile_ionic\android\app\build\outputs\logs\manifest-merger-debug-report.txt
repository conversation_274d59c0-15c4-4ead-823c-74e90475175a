-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:67:9-75:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:71:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:69:13-64
	android:exported
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:70:13-37
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:68:13-62
manifest
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:2:1-101:12
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:2:1-101:12
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:2:1-101:12
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:2:1-101:12
MERGED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:capacitor-app] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-geolocation] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-haptics] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:capacitor-keyboard] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\keyboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-26:12
MERGED from [:capacitor-network] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:capacitor-screen-orientation] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\screen-orientation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-status-bar] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\status-bar\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-android] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67a90a1a8c55670038dd931e3f3b25cb\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9084271993d381c8ac309caaf3a77e64\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b92e52c2632eac400835f629f0ee20f\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb45cce1f49d878f63bd8e8d88d9e60f\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\204db47e03493cf73776a3dfb705c834\transformed\jetified-firebase-analytics-21.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\024b6659e57055e6c4017e4c21ea75d4\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1a2e191223766f999bff6844a913a29\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f93b0ea020f03c70d0995ea03b814315\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [io.ionic.libs:iongeolocation-android:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09bbb197d3d6dee77f16eab66690678a\transformed\jetified-iongeolocation-android-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b9815794b248d93fce2c2be5a9ef34e\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f27da281d74b85eb78118b239a6eaaa\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d2ce448169c2e727be55efdf141c77b\transformed\jetified-play-services-measurement-sdk-21.5.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c5b18b55e93d5fa03408e4694250a2\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2359dc44ab01a450954c2d301df2b94c\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4c7db6702f7e50208ba73221e92642c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38c830fa5b6d1367df43faf0ceeec523\transformed\jetified-play-services-measurement-impl-21.5.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07120d64e33fd21102f9712dbd2064ba\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a266d20f6893b6e62cb5fdd736cdcc6c\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15892da900adba5b3d60250866325826\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3de85a618098aeb537cbedb4ee1d9e43\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4ce560774597ea94c85ef3c8e0e49a3\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\598151f0108175f2b1d1fd1995bc77a7\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b18dcb18abcb7c17455c46b05fba0568\transformed\webkit-1.12.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec2443e183070b451e91dc697f3e7a64\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2f21988eafdf1e02464404b3b4930ef\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30bcb85b335202e31f341a42ad87bba5\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a06704aa632e443d4858ae0ab155a592\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdbd558caa2532388ee1b3ef8049c5ea\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29371066396b67b010755bb17c43607b\transformed\jetified-core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bbdee37d4d39bf317b309154d2155a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c40da0f8874caab0886fc54097aa1009\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3671358a07cc6b9e8d49a1597b3af137\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fc077deaa6ae4b2a278ed65a6f1ce6d\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c510344c901dc03566d036990f9399e6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6350fb641fc02552f6031c8abeec6f1b\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea44852a4ef297c84b0de9976e6fb61\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf764f1fd33c94e3d1c93ffd95a4d2dc\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\096abac89f215017f2382b0bcdab8a0f\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b162b93295d0891258bb3cc283aeeb\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38034b5e8c35207c27c3e42bb8196d8f\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753860e07b345dc127b1d7f5d0eab371\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e75c3be96a6914f8eee68075b65eb3e\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57aaa07f867634f9177b8ea23a549d09\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bba617d5a95720ed153c08cb02f05ee8\transformed\jetified-play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d96b47300f755aa2bd705ef5858f1094\transformed\jetified-play-services-measurement-base-21.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff98a927589a446211f91dda472d317\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2aad402e0f517a6b906de88fe705bff\transformed\fragment-1.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60395a9384cbe2441ac8351f633baf7b\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6898a8bc3991f857522617dcb8b6325\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65b64062a12d422a60d99b420cc70239\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed5728efd22666df31a52b43d801d6f7\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1128d0b3b00e66ed828507978cbc9c50\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5f1461f08ccb786b3adfcebb6ed17f0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5585e02535d144316eb66fe0a80e378\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d11b9b8bfc076f7fb54e27f40ef4e98\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a042cb221ebd2f728e612b04b6445ca4\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\182af7a6e31fda7c7833cf898704169d\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d665cbdc1e629deef4b1c479ecb549c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0e07e3d4657341b543adb88e1f147ba\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34bb48d74cc5d2d7fcc9b7022ac700d6\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e8e706d29dc4ad342a9c853c78116ef\transformed\jetified-framework-10.1.1\AndroidManifest.xml:20:1-27:12
	package
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:4:5-76:19
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:4:5-76:19
MERGED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-17:19
MERGED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-17:19
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-24:19
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-24:19
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-12:19
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-12:19
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\204db47e03493cf73776a3dfb705c834\transformed\jetified-firebase-analytics-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\204db47e03493cf73776a3dfb705c834\transformed\jetified-firebase-analytics-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\024b6659e57055e6c4017e4c21ea75d4\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\024b6659e57055e6c4017e4c21ea75d4\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f93b0ea020f03c70d0995ea03b814315\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f93b0ea020f03c70d0995ea03b814315\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b9815794b248d93fce2c2be5a9ef34e\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b9815794b248d93fce2c2be5a9ef34e\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f27da281d74b85eb78118b239a6eaaa\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f27da281d74b85eb78118b239a6eaaa\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d2ce448169c2e727be55efdf141c77b\transformed\jetified-play-services-measurement-sdk-21.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d2ce448169c2e727be55efdf141c77b\transformed\jetified-play-services-measurement-sdk-21.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c5b18b55e93d5fa03408e4694250a2\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c5b18b55e93d5fa03408e4694250a2\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2359dc44ab01a450954c2d301df2b94c\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2359dc44ab01a450954c2d301df2b94c\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38c830fa5b6d1367df43faf0ceeec523\transformed\jetified-play-services-measurement-impl-21.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38c830fa5b6d1367df43faf0ceeec523\transformed\jetified-play-services-measurement-impl-21.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07120d64e33fd21102f9712dbd2064ba\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07120d64e33fd21102f9712dbd2064ba\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdbd558caa2532388ee1b3ef8049c5ea\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdbd558caa2532388ee1b3ef8049c5ea\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea44852a4ef297c84b0de9976e6fb61\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea44852a4ef297c84b0de9976e6fb61\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e75c3be96a6914f8eee68075b65eb3e\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e75c3be96a6914f8eee68075b65eb3e\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57aaa07f867634f9177b8ea23a549d09\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57aaa07f867634f9177b8ea23a549d09\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d96b47300f755aa2bd705ef5858f1094\transformed\jetified-play-services-measurement-base-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d96b47300f755aa2bd705ef5858f1094\transformed\jetified-play-services-measurement-base-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff98a927589a446211f91dda472d317\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff98a927589a446211f91dda472d317\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65b64062a12d422a60d99b420cc70239\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65b64062a12d422a60d99b420cc70239\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5f1461f08ccb786b3adfcebb6ed17f0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5f1461f08ccb786b3adfcebb6ed17f0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:9:9-35
	android:label
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:7:9-41
	android:roundIcon
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:8:9-54
	android:icon
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:6:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:5:9-35
	android:theme
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:10:9-40
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:12:9-69
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:11:9-44
activity#io.ionic.starter.MainActivity
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:14:9-33:20
	android:label
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:17:13-56
	android:launchMode
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:19:13-44
	android:exported
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:20:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:15:13-140
	android:theme
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:18:13-62
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:16:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:22:13-25:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:23:17-69
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:23:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:24:17-77
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:24:27-74
intent-filter#action:name:FLUTTER_NOTIFICATION_CLICK+category:name:android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:28:13-31:29
action#FLUTTER_NOTIFICATION_CLICK
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:29:17-69
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:29:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:30:17-76
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:30:27-73
activity#io.ionic.starter.EmergencyAlertActivity
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:36:9-43:43
	android:turnScreenOn
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:43:13-40
	android:excludeFromRecents
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:40:13-46
	android:showWhenLocked
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:42:13-42
	android:launchMode
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:39:13-43
	android:exported
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:41:13-37
	android:theme
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:38:13-71
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:37:13-51
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:46:9-48:60
	android:resource
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:48:13-57
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:47:13-83
meta-data#com.google.firebase.messaging.default_notification_color
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:50:9-52:53
	android:resource
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:52:13-50
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:51:13-84
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:54:9-56:53
	android:value
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:56:13-50
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:55:13-89
service#io.ionic.starter.EmergencyFCMService
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:59:9-65:19
	android:exported
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:61:13-37
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:60:13-48
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:62:13-64:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:63:17-78
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:63:25-75
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:80:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4c7db6702f7e50208ba73221e92642c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4c7db6702f7e50208ba73221e92642c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38c830fa5b6d1367df43faf0ceeec523\transformed\jetified-play-services-measurement-impl-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38c830fa5b6d1367df43faf0ceeec523\transformed\jetified-play-services-measurement-impl-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bba617d5a95720ed153c08cb02f05ee8\transformed\jetified-play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bba617d5a95720ed153c08cb02f05ee8\transformed\jetified-play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:23:5-67
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:80:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:81:5-79
MERGED from [:capacitor-network] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:capacitor-network] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4c7db6702f7e50208ba73221e92642c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4c7db6702f7e50208ba73221e92642c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38c830fa5b6d1367df43faf0ceeec523\transformed\jetified-play-services-measurement-impl-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38c830fa5b6d1367df43faf0ceeec523\transformed\jetified-play-services-measurement-impl-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bba617d5a95720ed153c08cb02f05ee8\transformed\jetified-play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bba617d5a95720ed153c08cb02f05ee8\transformed\jetified-play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:81:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:82:5-81
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:82:22-78
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:83:5-79
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:83:22-76
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:84:5-66
MERGED from [:capacitor-haptics] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:capacitor-haptics] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:84:22-63
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:87:5-68
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4c7db6702f7e50208ba73221e92642c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4c7db6702f7e50208ba73221e92642c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38c830fa5b6d1367df43faf0ceeec523\transformed\jetified-play-services-measurement-impl-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38c830fa5b6d1367df43faf0ceeec523\transformed\jetified-play-services-measurement-impl-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bba617d5a95720ed153c08cb02f05ee8\transformed\jetified-play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bba617d5a95720ed153c08cb02f05ee8\transformed\jetified-play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:87:22-65
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:88:5-77
MERGED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-77
MERGED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-77
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-77
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:23:5-77
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:88:22-74
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:91:5-78
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:91:22-75
uses-permission#android.permission.USE_FULL_SCREEN_INTENT
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:92:5-81
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:92:22-78
uses-permission#android.permission.TURN_SCREEN_ON
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:93:5-73
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:93:22-70
uses-permission#android.permission.SHOW_WHEN_LOCKED
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:94:5-75
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:94:22-72
uses-permission#android.permission.DISABLE_KEYGUARD
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:95:5-75
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:95:22-72
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:98:5-77
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:98:22-74
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:99:5-95
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:99:22-92
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:100:5-81
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:100:22-78
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:72:13-74:64
	android:resource
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:74:17-51
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:73:17-67
uses-sdk
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
MERGED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-app] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-app] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-geolocation] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-geolocation] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-haptics] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-haptics] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-keyboard] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\keyboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-keyboard] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\keyboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-network] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-network] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\network\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-screen-orientation] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\screen-orientation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-screen-orientation] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\screen-orientation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-status-bar] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\status-bar\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-status-bar] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\status-bar\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-android] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-android] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67a90a1a8c55670038dd931e3f3b25cb\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67a90a1a8c55670038dd931e3f3b25cb\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9084271993d381c8ac309caaf3a77e64\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9084271993d381c8ac309caaf3a77e64\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b92e52c2632eac400835f629f0ee20f\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b92e52c2632eac400835f629f0ee20f\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb45cce1f49d878f63bd8e8d88d9e60f\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fb45cce1f49d878f63bd8e8d88d9e60f\transformed\jetified-core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\204db47e03493cf73776a3dfb705c834\transformed\jetified-firebase-analytics-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\204db47e03493cf73776a3dfb705c834\transformed\jetified-firebase-analytics-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\024b6659e57055e6c4017e4c21ea75d4\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\024b6659e57055e6c4017e4c21ea75d4\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1a2e191223766f999bff6844a913a29\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1a2e191223766f999bff6844a913a29\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f93b0ea020f03c70d0995ea03b814315\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f93b0ea020f03c70d0995ea03b814315\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [io.ionic.libs:iongeolocation-android:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09bbb197d3d6dee77f16eab66690678a\transformed\jetified-iongeolocation-android-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.ionic.libs:iongeolocation-android:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09bbb197d3d6dee77f16eab66690678a\transformed\jetified-iongeolocation-android-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b9815794b248d93fce2c2be5a9ef34e\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b9815794b248d93fce2c2be5a9ef34e\transformed\jetified-play-services-location-21.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f27da281d74b85eb78118b239a6eaaa\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f27da281d74b85eb78118b239a6eaaa\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d2ce448169c2e727be55efdf141c77b\transformed\jetified-play-services-measurement-sdk-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d2ce448169c2e727be55efdf141c77b\transformed\jetified-play-services-measurement-sdk-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c5b18b55e93d5fa03408e4694250a2\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c5b18b55e93d5fa03408e4694250a2\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2359dc44ab01a450954c2d301df2b94c\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2359dc44ab01a450954c2d301df2b94c\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4c7db6702f7e50208ba73221e92642c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4c7db6702f7e50208ba73221e92642c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38c830fa5b6d1367df43faf0ceeec523\transformed\jetified-play-services-measurement-impl-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38c830fa5b6d1367df43faf0ceeec523\transformed\jetified-play-services-measurement-impl-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07120d64e33fd21102f9712dbd2064ba\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\07120d64e33fd21102f9712dbd2064ba\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a266d20f6893b6e62cb5fdd736cdcc6c\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a266d20f6893b6e62cb5fdd736cdcc6c\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15892da900adba5b3d60250866325826\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15892da900adba5b3d60250866325826\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3de85a618098aeb537cbedb4ee1d9e43\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3de85a618098aeb537cbedb4ee1d9e43\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4ce560774597ea94c85ef3c8e0e49a3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4ce560774597ea94c85ef3c8e0e49a3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\598151f0108175f2b1d1fd1995bc77a7\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\598151f0108175f2b1d1fd1995bc77a7\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b18dcb18abcb7c17455c46b05fba0568\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b18dcb18abcb7c17455c46b05fba0568\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec2443e183070b451e91dc697f3e7a64\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec2443e183070b451e91dc697f3e7a64\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2f21988eafdf1e02464404b3b4930ef\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2f21988eafdf1e02464404b3b4930ef\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30bcb85b335202e31f341a42ad87bba5\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30bcb85b335202e31f341a42ad87bba5\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a06704aa632e443d4858ae0ab155a592\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a06704aa632e443d4858ae0ab155a592\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdbd558caa2532388ee1b3ef8049c5ea\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdbd558caa2532388ee1b3ef8049c5ea\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29371066396b67b010755bb17c43607b\transformed\jetified-core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29371066396b67b010755bb17c43607b\transformed\jetified-core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bbdee37d4d39bf317b309154d2155a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57bbdee37d4d39bf317b309154d2155a\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c40da0f8874caab0886fc54097aa1009\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c40da0f8874caab0886fc54097aa1009\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3671358a07cc6b9e8d49a1597b3af137\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3671358a07cc6b9e8d49a1597b3af137\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fc077deaa6ae4b2a278ed65a6f1ce6d\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5fc077deaa6ae4b2a278ed65a6f1ce6d\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c510344c901dc03566d036990f9399e6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c510344c901dc03566d036990f9399e6\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6350fb641fc02552f6031c8abeec6f1b\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6350fb641fc02552f6031c8abeec6f1b\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea44852a4ef297c84b0de9976e6fb61\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea44852a4ef297c84b0de9976e6fb61\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf764f1fd33c94e3d1c93ffd95a4d2dc\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf764f1fd33c94e3d1c93ffd95a4d2dc\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\096abac89f215017f2382b0bcdab8a0f\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\096abac89f215017f2382b0bcdab8a0f\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b162b93295d0891258bb3cc283aeeb\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02b162b93295d0891258bb3cc283aeeb\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38034b5e8c35207c27c3e42bb8196d8f\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38034b5e8c35207c27c3e42bb8196d8f\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753860e07b345dc127b1d7f5d0eab371\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\753860e07b345dc127b1d7f5d0eab371\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e75c3be96a6914f8eee68075b65eb3e\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e75c3be96a6914f8eee68075b65eb3e\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57aaa07f867634f9177b8ea23a549d09\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57aaa07f867634f9177b8ea23a549d09\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bba617d5a95720ed153c08cb02f05ee8\transformed\jetified-play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bba617d5a95720ed153c08cb02f05ee8\transformed\jetified-play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d96b47300f755aa2bd705ef5858f1094\transformed\jetified-play-services-measurement-base-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d96b47300f755aa2bd705ef5858f1094\transformed\jetified-play-services-measurement-base-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff98a927589a446211f91dda472d317\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff98a927589a446211f91dda472d317\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2aad402e0f517a6b906de88fe705bff\transformed\fragment-1.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f2aad402e0f517a6b906de88fe705bff\transformed\fragment-1.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60395a9384cbe2441ac8351f633baf7b\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60395a9384cbe2441ac8351f633baf7b\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6898a8bc3991f857522617dcb8b6325\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e6898a8bc3991f857522617dcb8b6325\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65b64062a12d422a60d99b420cc70239\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65b64062a12d422a60d99b420cc70239\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed5728efd22666df31a52b43d801d6f7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed5728efd22666df31a52b43d801d6f7\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1128d0b3b00e66ed828507978cbc9c50\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1128d0b3b00e66ed828507978cbc9c50\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5f1461f08ccb786b3adfcebb6ed17f0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5f1461f08ccb786b3adfcebb6ed17f0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5585e02535d144316eb66fe0a80e378\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5585e02535d144316eb66fe0a80e378\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d11b9b8bfc076f7fb54e27f40ef4e98\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d11b9b8bfc076f7fb54e27f40ef4e98\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a042cb221ebd2f728e612b04b6445ca4\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a042cb221ebd2f728e612b04b6445ca4\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\182af7a6e31fda7c7833cf898704169d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\182af7a6e31fda7c7833cf898704169d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d665cbdc1e629deef4b1c479ecb549c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d665cbdc1e629deef4b1c479ecb549c\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0e07e3d4657341b543adb88e1f147ba\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0e07e3d4657341b543adb88e1f147ba\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34bb48d74cc5d2d7fcc9b7022ac700d6\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34bb48d74cc5d2d7fcc9b7022ac700d6\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e8e706d29dc4ad342a9c853c78116ef\transformed\jetified-framework-10.1.1\AndroidManifest.xml:25:5-44
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e8e706d29dc4ad342a9c853c78116ef\transformed\jetified-framework-10.1.1\AndroidManifest.xml:25:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml
service#io.capawesome.capacitorjs.plugins.firebase.messaging.MessagingService
ADDED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-16:19
	android:exported
		ADDED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-97
receiver#com.capacitorjs.plugins.localnotifications.TimedNotificationPublisher
ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-106
	android:name
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:19-103
receiver#com.capacitorjs.plugins.localnotifications.NotificationDismissReceiver
ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-107
	android:name
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:19-104
receiver#com.capacitorjs.plugins.localnotifications.LocalNotificationRestoreReceiver
ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-23:20
	android:exported
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
	android:directBootAware
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-43
	android:name
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-103
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.LOCKED_BOOT_COMPLETED+action:name:android.intent.action.QUICKBOOT_POWERON
ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-22:29
action#android.intent.action.LOCKED_BOOT_COMPLETED
ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-86
	android:name
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-83
action#android.intent.action.BOOT_COMPLETED
ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-79
	android:name
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:17-82
	android:name
		ADDED from [:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:25-79
service#de.appplant.cordova.plugin.background.ForegroundService
ADDED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-91
	android:name
		ADDED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:18-88
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4c7db6702f7e50208ba73221e92642c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e4c7db6702f7e50208ba73221e92642c\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\024b6659e57055e6c4017e4c21ea75d4\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\024b6659e57055e6c4017e4c21ea75d4\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f93b0ea020f03c70d0995ea03b814315\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f93b0ea020f03c70d0995ea03b814315\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\024b6659e57055e6c4017e4c21ea75d4\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\024b6659e57055e6c4017e4c21ea75d4\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\024b6659e57055e6c4017e4c21ea75d4\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38c830fa5b6d1367df43faf0ceeec523\transformed\jetified-play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38c830fa5b6d1367df43faf0ceeec523\transformed\jetified-play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57aaa07f867634f9177b8ea23a549d09\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57aaa07f867634f9177b8ea23a549d09\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bba617d5a95720ed153c08cb02f05ee8\transformed\jetified-play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bba617d5a95720ed153c08cb02f05ee8\transformed\jetified-play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bba617d5a95720ed153c08cb02f05ee8\transformed\jetified-play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bba617d5a95720ed153c08cb02f05ee8\transformed\jetified-play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bba617d5a95720ed153c08cb02f05ee8\transformed\jetified-play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bba617d5a95720ed153c08cb02f05ee8\transformed\jetified-play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f93b0ea020f03c70d0995ea03b814315\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f93b0ea020f03c70d0995ea03b814315\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f93b0ea020f03c70d0995ea03b814315\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f27da281d74b85eb78118b239a6eaaa\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f27da281d74b85eb78118b239a6eaaa\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f27da281d74b85eb78118b239a6eaaa\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f27da281d74b85eb78118b239a6eaaa\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38c830fa5b6d1367df43faf0ceeec523\transformed\jetified-play-services-measurement-impl-21.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38c830fa5b6d1367df43faf0ceeec523\transformed\jetified-play-services-measurement-impl-21.5.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea44852a4ef297c84b0de9976e6fb61\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea44852a4ef297c84b0de9976e6fb61\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65b64062a12d422a60d99b420cc70239\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65b64062a12d422a60d99b420cc70239\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdbd558caa2532388ee1b3ef8049c5ea\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdbd558caa2532388ee1b3ef8049c5ea\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdbd558caa2532388ee1b3ef8049c5ea\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea44852a4ef297c84b0de9976e6fb61\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea44852a4ef297c84b0de9976e6fb61\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea44852a4ef297c84b0de9976e6fb61\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff98a927589a446211f91dda472d317\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff98a927589a446211f91dda472d317\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff98a927589a446211f91dda472d317\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
