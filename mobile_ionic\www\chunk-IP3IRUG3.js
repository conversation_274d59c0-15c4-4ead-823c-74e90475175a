import{f as g}from"./chunk-BAKMWPBW.js";var w=e=>{try{if(e instanceof f)return e.value;if(!h()||typeof e!="string"||e==="")return e;if(e.includes("onload="))return"";let n=document.createDocumentFragment(),t=document.createElement("div");n.appendChild(t),t.innerHTML=e,b.forEach(i=>{let m=n.querySelectorAll(i);for(let a=m.length-1;a>=0;a--){let s=m[a];s.parentNode?s.parentNode.removeChild(s):n.removeChild(s);let v=u(s);for(let l=0;l<v.length;l++)d(v[l])}});let r=u(n);for(let i=0;i<r.length;i++)d(r[i]);let o=document.createElement("div");o.appendChild(n);let c=o.querySelector("div");return c!==null?c.innerHTML:o.innerHTML}catch(n){return g("sanitizeDOMString",n),""}},d=e=>{if(e.nodeType&&e.nodeType!==1)return;if(typeof NamedNodeMap<"u"&&!(e.attributes instanceof NamedNodeMap)){e.remove();return}for(let t=e.attributes.length-1;t>=0;t--){let r=e.attributes.item(t),o=r.name;if(!p.includes(o.toLowerCase())){e.removeAttribute(o);continue}let c=r.value,i=e[o];(c!=null&&c.toLowerCase().includes("javascript:")||i!=null&&i.toLowerCase().includes("javascript:"))&&e.removeAttribute(o)}let n=u(e);for(let t=0;t<n.length;t++)d(n[t])},u=e=>e.children!=null?e.children:e.childNodes,h=()=>{var e;let n=window,t=(e=n==null?void 0:n.Ionic)===null||e===void 0?void 0:e.config;return t?t.get?t.get("sanitizerEnabled",!0):t.sanitizerEnabled===!0||t.sanitizerEnabled===void 0:!0},p=["class","id","href","src","name","slot"],b=["script","style","iframe","meta","link","object","embed"],f=class{constructor(n){this.value=n}},I=e=>{let n=window,t=n.Ionic;if(!(t&&t.config&&t.config.constructor.name!=="Object"))return n.Ionic=n.Ionic||{},n.Ionic.config=Object.assign(Object.assign({},n.Ionic.config),e),n.Ionic.config};var C=!1;export{w as a,I as b,C as c};
