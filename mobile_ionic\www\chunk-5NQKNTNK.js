import{a as A}from"./chunk-LWIBFRM4.js";import"./chunk-25KYI4JX.js";import{b as R}from"./chunk-3KLEUTE3.js";import"./chunk-FULEFYAM.js";import"./chunk-NETZAO6G.js";import{$a as y,A as p,Ab as N,Db as h,F as n,G as o,H as m,J as d,M as a,Na as g,Oa as b,Pa as v,Qa as S,Ra as k,Sa as w,Ta as x,Wa as _,X as u,ab as f,cb as M,db as E,ea as C,eb as O,fb as T,gb as P,ub as F,vb as j,z as c,zb as D}from"./chunk-YFIZFQXH.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{h as I}from"./chunk-LNJ3S2LQ.js";var B=(()=>{let i=class i{constructor(t,e,r,l){this.fcmService=t,this.authService=e,this.alertController=r,this.loadingController=l,this.userId=null}ngOnInit(){let t=localStorage.getItem("token");if(t)try{let e=this.parseJwt(t);e&&e.sub&&(this.userId=e.sub)}catch(e){console.error("Error parsing JWT token:",e)}}parseJwt(t){try{let r=t.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),l=decodeURIComponent(atob(r).split("").map(function(J){return"%"+("00"+J.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(l)}catch(e){return console.error("Error parsing JWT token:",e),null}}refreshFCMToken(){return I(this,null,function*(){let t=yield this.loadingController.create({message:"Refreshing notification settings...",spinner:"circles"});yield t.present();try{let e=yield this.fcmService.refreshFCMToken(this.userId||void 0);yield t.dismiss(),e?yield(yield this.alertController.create({header:"Success",message:"Notification settings refreshed successfully. You should now be able to receive notifications.",buttons:["OK"]})).present():yield(yield this.alertController.create({header:"Error",message:"Failed to refresh notification settings. Please try again later.",buttons:["OK"]})).present()}catch{yield t.dismiss(),yield(yield this.alertController.create({header:"Error",message:"An error occurred while refreshing notification settings. Please try again later.",buttons:["OK"]})).present()}})}};i.\u0275fac=function(e){return new(e||i)(c(R),c(A),c(D),c(N))},i.\u0275cmp=p({type:i,selectors:[["app-fcm-refresh"]],decls:14,vars:0,consts:[["expand","block",3,"click"],["name","refresh-outline","slot","start"]],template:function(e,r){e&1&&(n(0,"ion-card")(1,"ion-card-header")(2,"ion-card-title"),a(3,"Notification Settings"),o(),n(4,"ion-card-subtitle"),a(5,"Not receiving notifications?"),o()(),n(6,"ion-card-content")(7,"p"),a(8,"If you're not receiving notifications, you can try refreshing your notification settings."),o(),n(9,"p"),a(10,"This will generate a new notification token and register it with our servers."),o(),n(11,"ion-button",0),d("click",function(){return r.refreshFCMToken()}),m(12,"ion-icon",1),a(13," Refresh Notification Settings "),o()()())},dependencies:[h,g,v,S,k,w,x,f,u],styles:["ion-card[_ngcontent-%COMP%]{margin:16px;border-radius:12px;box-shadow:0 4px 12px #0000001a}ion-card-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:600}ion-card-subtitle[_ngcontent-%COMP%]{font-size:.9rem;color:var(--ion-color-medium)}ion-card-content[_ngcontent-%COMP%]{padding:16px}p[_ngcontent-%COMP%]{margin-bottom:12px;font-size:.9rem;line-height:1.4;color:var(--ion-color-dark)}ion-button[_ngcontent-%COMP%]{margin-top:16px}"]});let s=i;return s})();var Z=(()=>{let i=class i{constructor(t){this.router=t}ngOnInit(){}goBack(){this.router.navigate(["/tabs/home"])}logout(){localStorage.removeItem("token"),this.router.navigate(["/login"])}};i.\u0275fac=function(e){return new(e||i)(c(C))},i.\u0275cmp=p({type:i,selectors:[["app-settings"]],decls:22,vars:0,consts:[["slot","start"],[3,"click"],["slot","icon-only","name","arrow-back"],["button","",3,"click"],["name","log-out-outline","slot","start","color","danger"]],template:function(e,r){e&1&&(n(0,"ion-header")(1,"ion-toolbar")(2,"ion-buttons",0)(3,"ion-button",1),d("click",function(){return r.goBack()}),m(4,"ion-icon",2),o()(),n(5,"ion-title"),a(6,"Settings"),o()()(),n(7,"ion-content")(8,"ion-list")(9,"ion-item-group")(10,"ion-item-divider")(11,"ion-label"),a(12,"Notifications"),o()(),m(13,"app-fcm-refresh"),o(),n(14,"ion-item-group")(15,"ion-item-divider")(16,"ion-label"),a(17,"Account"),o()(),n(18,"ion-item",3),d("click",function(){return r.logout()}),m(19,"ion-icon",4),n(20,"ion-label"),a(21,"Logout"),o()()()()())},dependencies:[h,g,b,_,y,f,M,E,O,T,P,F,j,u,B],styles:["ion-item-divider[_ngcontent-%COMP%]{--background: var(--ion-color-light);--color: var(--ion-color-medium);font-size:.9rem;font-weight:600;letter-spacing:.5px;text-transform:uppercase;padding-top:16px}ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--inner-padding-end: 16px}"]});let s=i;return s})();export{Z as SettingsPage};
