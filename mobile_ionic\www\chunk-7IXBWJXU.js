import{a as y}from"./chunk-7VSVCZ7D.js";import{a as g,b as N}from"./chunk-VBU7NMPV.js";import{b as w}from"./chunk-L5T6STQ3.js";import{b as S,d as L,f as d,g as C,j as I,k as f,l as A}from"./chunk-3EJRMEWO.js";import{d as V,f as k,g as x,i as z}from"./chunk-GNOVVPTF.js";import{e as E,f as p}from"./chunk-BAKMWPBW.js";import{h as v}from"./chunk-LNJ3S2LQ.js";var T=":host{--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:grid;grid-auto-columns:1fr;position:relative;-ms-flex-align:stretch;align-items:stretch;-ms-flex-pack:center;justify-content:center;width:100%;background:var(--background);font-family:var(--ion-font-family, inherit);text-align:center;contain:paint;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.segment-scrollable){-ms-flex-pack:start;justify-content:start;width:auto;overflow-x:auto;grid-auto-columns:minmax(-webkit-min-content, 1fr);grid-auto-columns:minmax(min-content, 1fr)}:host(.segment-scrollable::-webkit-scrollbar){display:none}:host{--background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.065);border-radius:8px;overflow:hidden;z-index:0}:host(.ion-color){background:rgba(var(--ion-color-base-rgb), 0.065)}:host(.in-toolbar){-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;width:auto}:host(.in-toolbar:not(.ion-color)){background:var(--ion-toolbar-segment-background, var(--background))}:host(.in-toolbar-color:not(.ion-color)){background:rgba(var(--ion-color-contrast-rgb), 0.11)}",j=T,D=":host{--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:grid;grid-auto-columns:1fr;position:relative;-ms-flex-align:stretch;align-items:stretch;-ms-flex-pack:center;justify-content:center;width:100%;background:var(--background);font-family:var(--ion-font-family, inherit);text-align:center;contain:paint;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.segment-scrollable){-ms-flex-pack:start;justify-content:start;width:auto;overflow-x:auto;grid-auto-columns:minmax(-webkit-min-content, 1fr);grid-auto-columns:minmax(min-content, 1fr)}:host(.segment-scrollable::-webkit-scrollbar){display:none}:host{--background:transparent;grid-auto-columns:minmax(auto, 360px)}:host(.in-toolbar){min-height:var(--min-height)}:host(.segment-scrollable) ::slotted(ion-segment-button){min-width:auto}",P=D,Z=(()=>{let b=class{constructor(t){S(this,t),this.ionChange=f(this,"ionChange",7),this.ionSelect=f(this,"ionSelect",7),this.ionStyle=f(this,"ionStyle",7),this.segmentViewEl=null,this.onClick=o=>{let n=o.target,e=this.checked;n.tagName!=="ION-SEGMENT"&&(this.value=n.value,n!==e&&this.emitValueChange(),this.segmentViewEl?(this.updateSegmentView(),this.scrollable&&e&&this.checkButton(e,n)):(this.scrollable||!this.swipeGesture)&&(e?this.checkButton(e,n):this.setCheckedClasses()))},this.onSlottedItemsChange=()=>{this.valueChanged(this.value)},this.getSegmentButton=o=>{var n,e;let i=this.getButtons().filter(r=>!r.disabled),a=i.findIndex(r=>r===document.activeElement);switch(o){case"first":return i[0];case"last":return i[i.length-1];case"next":return(n=i[a+1])!==null&&n!==void 0?n:i[0];case"previous":return(e=i[a-1])!==null&&e!==void 0?e:i[i.length-1];default:return null}},this.activated=!1,this.color=void 0,this.disabled=!1,this.scrollable=!1,this.swipeGesture=!0,this.value=void 0,this.selectOnFocus=!1}colorChanged(t,o){(o===void 0&&t!==void 0||o!==void 0&&t===void 0)&&this.emitStyle()}swipeGestureChanged(){this.gestureChanged()}valueChanged(t,o){if(this.segmentViewEl&&t===void 0){this.value=this.getButtons()[0].value;return}if(o!==void 0&&t!==void 0){let n=this.getButtons(),e=n.find(a=>a.value===o),i=n.find(a=>a.value===t);e&&i&&(this.segmentViewEl?this.triggerScrollOnValueChange!==!1&&this.updateSegmentView():this.checkButton(e,i))}else t!==void 0&&o===void 0&&this.segmentViewEl&&this.updateSegmentView();this.ionSelect.emit({value:t}),this.segmentViewEl||this.scrollActiveButtonIntoView(),this.triggerScrollOnValueChange=void 0}disabledChanged(){if(this.gestureChanged(),this.segmentViewEl)this.segmentViewEl.disabled=this.disabled;else{let t=this.getButtons();for(let o of t)o.disabled=this.disabled}}gestureChanged(){this.gesture&&this.gesture.enable(!this.scrollable&&!this.disabled&&this.swipeGesture)}connectedCallback(){this.emitStyle(),this.segmentViewEl=this.getSegmentView()}disconnectedCallback(){this.segmentViewEl=null}componentWillLoad(){this.emitStyle()}componentDidLoad(){return v(this,null,function*(){this.segmentViewEl=this.getSegmentView(),this.setCheckedClasses(),z(()=>{this.scrollActiveButtonIntoView(!1)}),this.gesture=(yield import("./chunk-TRVJA27Y.js")).createGesture({el:this.el,gestureName:"segment",gesturePriority:100,threshold:0,passive:!1,onStart:t=>this.onStart(t),onMove:t=>this.onMove(t),onEnd:t=>this.onEnd(t)}),this.gestureChanged(),this.disabled&&this.disabledChanged(),this.updateSegmentView(!1)})}onStart(t){this.valueBeforeGesture=this.value,this.activate(t)}onMove(t){this.setNextIndex(t)}onEnd(t){this.setActivated(!1),this.setNextIndex(t,!0),t.event.stopImmediatePropagation();let o=this.value;o!==void 0&&this.valueBeforeGesture!==o&&(this.emitValueChange(),this.updateSegmentView()),this.valueBeforeGesture=void 0}emitValueChange(){let{value:t}=this;this.ionChange.emit({value:t})}getButtons(){return Array.from(this.el.querySelectorAll("ion-segment-button"))}get checked(){return this.getButtons().find(t=>t.value===this.value)}setActivated(t){this.getButtons().forEach(n=>{n.classList.toggle("segment-button-activated",t)}),this.activated=t}activate(t){let o=t.event.target,e=this.getButtons().find(i=>i.value===this.value);o.tagName==="ION-SEGMENT-BUTTON"&&(e||(this.value=o.value,this.setCheckedClasses()),this.value===o.value&&this.setActivated(!0))}getIndicator(t){return(t.shadowRoot||t).querySelector(".segment-button-indicator")}checkButton(t,o){let n=this.getIndicator(t),e=this.getIndicator(o);if(n===null||e===null)return;let i=n.getBoundingClientRect(),a=e.getBoundingClientRect(),r=i.width/a.width,c=`translate3d(${i.left-a.left}px, 0, 0) scaleX(${r})`;L(()=>{e.classList.remove("segment-button-indicator-animated"),e.style.setProperty("transform",c),e.getBoundingClientRect(),e.classList.add("segment-button-indicator-animated"),e.style.setProperty("transform",""),this.scrollActiveButtonIntoView(!0)}),this.value=o.value,this.setCheckedClasses()}setCheckedClasses(){let t=this.getButtons(),n=t.findIndex(e=>e.value===this.value)+1;for(let e of t)e.classList.remove("segment-button-after-checked");n<t.length&&t[n].classList.add("segment-button-after-checked")}getSegmentView(){let o=this.getButtons().find(e=>e.contentId),n=document.querySelector(`ion-segment-content[id="${o==null?void 0:o.contentId}"]`);return n==null?void 0:n.closest("ion-segment-view")}handleSegmentViewScroll(t){let{scrollRatio:o,isManualScroll:n}=t.detail;if(!n)return;let e=t.target,i=this.segmentViewEl,a=this.el;if(t.composedPath().includes(i)||e!=null&&e.contains(a)){let r=this.getButtons();if(!r.length)return;let s=r.findIndex(h=>h.value===this.value),c=r[s],l=Math.round(o*(r.length-1));(this.lastNextIndex===void 0||this.lastNextIndex!==l)&&(this.lastNextIndex=l,this.triggerScrollOnValueChange=!1,this.checkButton(c,r[l]),this.emitValueChange())}}updateSegmentView(t=!0){let n=this.getButtons().find(i=>i.value===this.value);if(!(n!=null&&n.contentId))return;let e=this.segmentViewEl;e&&e.setContent(n.contentId,t)}scrollActiveButtonIntoView(t=!0){let{scrollable:o,value:n,el:e}=this;if(o){let a=this.getButtons().find(r=>r.value===n);if(a!==void 0){let r=e.getBoundingClientRect(),s=a.getBoundingClientRect(),l=s.x-r.x-r.width/2+s.width/2,h=e.scrollLeft+l;e.scrollTo({top:0,left:h,behavior:t?"smooth":"instant"})}}}setNextIndex(t,o=!1){let n=y(this.el),e=this.activated,i=this.getButtons(),a=i.findIndex(u=>u.value===this.value),r=i[a],s,c;if(a===-1)return;let l=r.getBoundingClientRect(),h=l.left,B=l.width,m=t.currentX,G=l.top+l.height/2,M=this.el.getRootNode().elementFromPoint(m,G),O=n?m>h+B:m<h,R=n?m<h:m>h+B;if(e&&!o){if(O){let u=a-1;u>=0&&(c=u)}else if(R&&e&&!o){let u=a+1;u<i.length&&(c=u)}c!==void 0&&!i[c].disabled&&(s=i[c])}if(!e&&o&&(s=M),s!=null){if(s.tagName==="ION-SEGMENT")return!1;r!==s&&this.checkButton(r,s)}return!0}emitStyle(){this.ionStyle.emit({segment:!0})}onKeyDown(t){let o=y(this.el),n=this.selectOnFocus,e;switch(t.key){case"ArrowRight":t.preventDefault(),e=o?this.getSegmentButton("previous"):this.getSegmentButton("next");break;case"ArrowLeft":t.preventDefault(),e=o?this.getSegmentButton("next"):this.getSegmentButton("previous");break;case"Home":t.preventDefault(),e=this.getSegmentButton("first");break;case"End":t.preventDefault(),e=this.getSegmentButton("last");break;case" ":case"Enter":t.preventDefault(),e=document.activeElement,n=!0}if(e){if(n){let i=this.checked;this.checkButton(i||e,e),e!==i&&this.emitValueChange()}e.setFocus()}}render(){let t=w(this);return d(C,{key:"a64e39352050b516f7dc82ce95a4bcff8431d1d0",role:"tablist",onClick:this.onClick,class:N(this.color,{[t]:!0,"in-toolbar":g("ion-toolbar",this.el),"in-toolbar-color":g("ion-toolbar[color]",this.el),"segment-activated":this.activated,"segment-disabled":this.disabled,"segment-scrollable":this.scrollable})},d("slot",{key:"bb3f3ec30e59e0461fa620d8961ab730cc802a4e",onSlotchange:this.onSlottedItemsChange}))}get el(){return I(this)}static get watchers(){return{color:["colorChanged"],swipeGesture:["swipeGestureChanged"],value:["valueChanged"],disabled:["disabledChanged"]}}};return b.style={ios:j,md:P},b})(),F=':host{--color:initial;--color-hover:var(--color);--color-checked:var(--color);--color-disabled:var(--color);--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:column;flex-direction:column;height:auto;background:var(--background);color:var(--color);text-decoration:none;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;grid-row:1;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;min-width:inherit;max-width:inherit;height:auto;min-height:inherit;max-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:none;outline:none;background:transparent;contain:content;pointer-events:none;overflow:hidden;z-index:2}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}:host(.segment-button-checked){background:var(--background-checked);color:var(--color-checked)}:host(.segment-button-disabled){cursor:default;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(:focus){outline:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.segment-button-checked:hover) .button-native{color:var(--color-checked)}}::slotted(ion-icon){-ms-flex-negative:0;flex-shrink:0;-ms-flex-order:-1;order:-1;pointer-events:none}::slotted(ion-label){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;line-height:22px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.segment-button-layout-icon-top) .button-native{-ms-flex-direction:column;flex-direction:column}:host(.segment-button-layout-icon-start) .button-native{-ms-flex-direction:row;flex-direction:row}:host(.segment-button-layout-icon-end) .button-native{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.segment-button-layout-icon-bottom) .button-native{-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.segment-button-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.segment-button-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color, var(--color-checked))}.segment-button-indicator{-webkit-transform-origin:left;transform-origin:left;position:absolute;opacity:0;-webkit-box-sizing:border-box;box-sizing:border-box;will-change:transform, opacity;pointer-events:none}.segment-button-indicator-background{width:100%;height:var(--indicator-height);-webkit-transform:var(--indicator-transform);transform:var(--indicator-transform);-webkit-box-shadow:var(--indicator-box-shadow);box-shadow:var(--indicator-box-shadow);pointer-events:none}.segment-button-indicator-animated{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked) .segment-button-indicator{opacity:1}@media (prefers-reduced-motion: reduce){.segment-button-indicator-background{-webkit-transform:none;transform:none}.segment-button-indicator-animated{-webkit-transition:none;transition:none}}:host{--background:none;--background-checked:none;--background-hover:none;--background-hover-opacity:0;--background-focused:none;--background-focused-opacity:0;--border-radius:7px;--border-width:1px;--border-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.12);--border-style:solid;--indicator-box-shadow:0 0 5px rgba(0, 0, 0, 0.16);--indicator-color:var(--ion-color-step-350, var(--ion-background-color-step-350, var(--ion-background-color, #fff)));--indicator-height:100%;--indicator-transition:transform 260ms cubic-bezier(0.4, 0, 0.2, 1);--indicator-transform:none;--transition:100ms all linear;--padding-top:0;--padding-end:13px;--padding-bottom:0;--padding-start:13px;margin-top:2px;margin-bottom:2px;position:relative;-ms-flex-direction:row;flex-direction:row;min-width:70px;min-height:28px;-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);font-size:13px;font-weight:450;line-height:37px}:host::before{margin-left:0;margin-right:0;margin-top:5px;margin-bottom:5px;-webkit-transition:160ms opacity ease-in-out;transition:160ms opacity ease-in-out;-webkit-transition-delay:100ms;transition-delay:100ms;border-left:var(--border-width) var(--border-style) var(--border-color);content:"";opacity:1;will-change:opacity}:host(:first-of-type)::before{border-left-color:transparent}:host(.segment-button-disabled){opacity:0.3}::slotted(ion-icon){font-size:24px}:host(.segment-button-layout-icon-start) ::slotted(ion-label){-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:0;margin-inline-end:0}:host(.segment-button-layout-icon-end) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:2px;margin-inline-end:2px}.segment-button-indicator{-webkit-padding-start:2px;padding-inline-start:2px;-webkit-padding-end:2px;padding-inline-end:2px;left:0;right:0;top:0;bottom:0}.segment-button-indicator-background{border-radius:var(--border-radius);background:var(--indicator-color)}.segment-button-indicator-background{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked)::before,:host(.segment-button-after-checked)::before{opacity:0}:host(.segment-button-checked){z-index:-1}:host(.segment-button-activated){--indicator-transform:scale(0.95)}:host(.ion-focused) .button-native{opacity:0.7}@media (any-hover: hover){:host(:hover) .button-native{opacity:0.5}:host(.segment-button-checked:hover) .button-native{opacity:1}}:host(.in-segment-color){background:none;color:var(--ion-text-color, #000)}:host(.in-segment-color) .segment-button-indicator-background{background:var(--ion-color-step-350, var(--ion-background-color-step-350, var(--ion-background-color, #fff)))}@media (any-hover: hover){:host(.in-segment-color:hover) .button-native,:host(.in-segment-color.segment-button-checked:hover) .button-native{color:var(--ion-text-color, #000)}}:host(.in-toolbar:not(.in-segment-color)){--background-checked:var(--ion-toolbar-segment-background-checked, none);--color:var(--ion-toolbar-segment-color, var(--ion-toolbar-color), initial);--color-checked:var(--ion-toolbar-segment-color-checked, var(--ion-toolbar-color), initial);--indicator-color:var(--ion-toolbar-segment-indicator-color, var(--ion-color-step-350, var(--ion-background-color-step-350, var(--ion-background-color, #fff))))}:host(.in-toolbar-color) .segment-button-indicator-background{background:var(--ion-color-contrast)}:host(.in-toolbar-color:not(.in-segment-color)) .button-native{color:var(--ion-color-contrast)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color)) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.in-toolbar-color:not(.in-segment-color):hover) .button-native{color:var(--ion-color-contrast)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color):hover) .button-native{color:var(--ion-color-base)}}',$=F,q=':host{--color:initial;--color-hover:var(--color);--color-checked:var(--color);--color-disabled:var(--color);--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:column;flex-direction:column;height:auto;background:var(--background);color:var(--color);text-decoration:none;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;grid-row:1;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;min-width:inherit;max-width:inherit;height:auto;min-height:inherit;max-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:none;outline:none;background:transparent;contain:content;pointer-events:none;overflow:hidden;z-index:2}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}:host(.segment-button-checked){background:var(--background-checked);color:var(--color-checked)}:host(.segment-button-disabled){cursor:default;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(:focus){outline:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.segment-button-checked:hover) .button-native{color:var(--color-checked)}}::slotted(ion-icon){-ms-flex-negative:0;flex-shrink:0;-ms-flex-order:-1;order:-1;pointer-events:none}::slotted(ion-label){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;line-height:22px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.segment-button-layout-icon-top) .button-native{-ms-flex-direction:column;flex-direction:column}:host(.segment-button-layout-icon-start) .button-native{-ms-flex-direction:row;flex-direction:row}:host(.segment-button-layout-icon-end) .button-native{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.segment-button-layout-icon-bottom) .button-native{-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.segment-button-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.segment-button-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color, var(--color-checked))}.segment-button-indicator{-webkit-transform-origin:left;transform-origin:left;position:absolute;opacity:0;-webkit-box-sizing:border-box;box-sizing:border-box;will-change:transform, opacity;pointer-events:none}.segment-button-indicator-background{width:100%;height:var(--indicator-height);-webkit-transform:var(--indicator-transform);transform:var(--indicator-transform);-webkit-box-shadow:var(--indicator-box-shadow);box-shadow:var(--indicator-box-shadow);pointer-events:none}.segment-button-indicator-animated{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked) .segment-button-indicator{opacity:1}@media (prefers-reduced-motion: reduce){.segment-button-indicator-background{-webkit-transform:none;transform:none}.segment-button-indicator-animated{-webkit-transition:none;transition:none}}:host{--background:none;--background-checked:none;--background-hover:var(--color-checked);--background-focused:var(--color-checked);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04;--color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--color-checked:var(--ion-color-primary, #0054e9);--indicator-box-shadow:none;--indicator-color:var(--color-checked);--indicator-height:2px;--indicator-transition:transform 250ms cubic-bezier(0.4, 0, 0.2, 1);--indicator-transform:none;--padding-top:0;--padding-end:16px;--padding-bottom:0;--padding-start:16px;--transition:color 0.15s linear 0s, opacity 0.15s linear 0s;min-width:90px;min-height:48px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);font-size:14px;font-weight:500;letter-spacing:0.06em;line-height:40px;text-transform:uppercase}:host(.segment-button-disabled){opacity:0.3}:host(.in-segment-color){background:none;color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6)}:host(.in-segment-color) ion-ripple-effect{color:var(--ion-color-base)}:host(.in-segment-color) .segment-button-indicator-background{background:var(--ion-color-base)}:host(.in-segment-color.segment-button-checked) .button-native{color:var(--ion-color-base)}:host(.in-segment-color.ion-focused) .button-native::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.in-segment-color:hover) .button-native{color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6)}:host(.in-segment-color:hover) .button-native::after{background:var(--ion-color-base)}:host(.in-segment-color.segment-button-checked:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-segment-color)){--background:var(--ion-toolbar-segment-background, none);--background-checked:var(--ion-toolbar-segment-background-checked, none);--color:var(--ion-toolbar-segment-color, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6));--color-checked:var(--ion-toolbar-segment-color-checked, var(--ion-color-primary, #0054e9));--indicator-color:var(--ion-toolbar-segment-color-checked, var(--color-checked))}:host(.in-toolbar-color:not(.in-segment-color)) .button-native{color:rgba(var(--ion-color-contrast-rgb), 0.6)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color)) .button-native{color:var(--ion-color-contrast)}@media (any-hover: hover){:host(.in-toolbar-color:not(.in-segment-color)) .button-native::after{background:var(--ion-color-contrast)}}::slotted(ion-icon){margin-top:12px;margin-bottom:12px;font-size:24px}::slotted(ion-label){margin-top:12px;margin-bottom:12px}:host(.segment-button-layout-icon-top) ::slotted(ion-label),:host(.segment-button-layout-icon-bottom) ::slotted(ion-icon){margin-top:0}:host(.segment-button-layout-icon-top) ::slotted(ion-icon),:host(.segment-button-layout-icon-bottom) ::slotted(ion-label){margin-bottom:0}:host(.segment-button-layout-icon-start) ::slotted(ion-label){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:0;margin-inline-end:0}:host(.segment-button-layout-icon-end) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px}:host(.segment-button-has-icon-only) ::slotted(ion-icon){margin-top:12px;margin-bottom:12px}:host(.segment-button-has-label-only) ::slotted(ion-label){margin-top:12px;margin-bottom:12px}.segment-button-indicator{left:0;right:0;bottom:0}.segment-button-indicator-background{background:var(--indicator-color)}:host(.in-toolbar:not(.in-segment-color)) .segment-button-indicator-background{background:var(--ion-toolbar-segment-indicator-color, var(--indicator-color))}:host(.in-toolbar-color:not(.in-segment-color)) .segment-button-indicator-background{background:var(--ion-color-contrast)}',_=q,X=0,tt=(()=>{let b=class{constructor(t){S(this,t),this.segmentEl=null,this.inheritedAttributes={},this.updateStyle=()=>{A(this)},this.updateState=()=>{let{segmentEl:o}=this;o&&(this.checked=o.value===this.value,o.disabled&&(this.disabled=!0))},this.checked=!1,this.contentId=void 0,this.disabled=!1,this.layout="icon-top",this.type="button",this.value="ion-sb-"+X++}valueChanged(){this.updateState()}connectedCallback(){let t=this.segmentEl=this.el.closest("ion-segment");t&&(this.updateState(),k(t,"ionSelect",this.updateState),k(t,"ionStyle",this.updateStyle)),this.contentId&&this.disabled&&(E("[ion-segment-button] - Segment buttons cannot be disabled when associated with an <ion-segment-content>."),this.disabled=!1)}disconnectedCallback(){let t=this.segmentEl;t&&(x(t,"ionSelect",this.updateState),x(t,"ionStyle",this.updateStyle),this.segmentEl=null)}componentWillLoad(){if(this.inheritedAttributes=Object.assign({},V(this.el,["aria-label"])),!this.contentId)return;let t=document.getElementById(this.contentId);if(!t){p(`[ion-segment-button] - Unable to find Segment Content with id="${this.contentId}".`);return}if(t.tagName!=="ION-SEGMENT-CONTENT"){p(`[ion-segment-button] - Element with id="${this.contentId}" is not an <ion-segment-content> element.`);return}}get hasLabel(){return!!this.el.querySelector("ion-label")}get hasIcon(){return!!this.el.querySelector("ion-icon")}setFocus(){return v(this,null,function*(){let{nativeEl:t}=this;t!==void 0&&t.focus()})}render(){let{checked:t,type:o,disabled:n,hasIcon:e,hasLabel:i,layout:a,segmentEl:r}=this,s=w(this),c=()=>(r==null?void 0:r.color)!==void 0;return d(C,{key:"d79dad80db69123510c6d52bbf4424558600c14a",class:{[s]:!0,"in-toolbar":g("ion-toolbar",this.el),"in-toolbar-color":g("ion-toolbar[color]",this.el),"in-segment":g("ion-segment",this.el),"in-segment-color":c(),"segment-button-has-label":i,"segment-button-has-icon":e,"segment-button-has-label-only":i&&!e,"segment-button-has-icon-only":e&&!i,"segment-button-disabled":n,"segment-button-checked":t,[`segment-button-layout-${a}`]:!0,"ion-activatable":!0,"ion-activatable-instant":!0,"ion-focusable":!0}},d("button",Object.assign({key:"781fd7fef443a2fbd404b8a399d1203794759a69","aria-selected":t?"true":"false",role:"tab",ref:l=>this.nativeEl=l,type:o,class:"button-native",part:"native",disabled:n},this.inheritedAttributes),d("span",{key:"64df08f60ea17481183f8ad284884b9979eaeb24",class:"button-inner"},d("slot",{key:"2b7ef328023ab5ccc0adc6f865e5e7121467eeb8"})),s==="md"&&d("ion-ripple-effect",{key:"1fd5e6179227925a03b6c3b5258bbf4ea99bfef1"})),d("div",{key:"dbcedad6988b0eadeebe02bdafdedb8eb37af19c",part:"indicator",class:"segment-button-indicator segment-button-indicator-animated"},d("div",{key:"f4f1789f66bdfa2de5445bc0d292116a93bad7cc",part:"indicator-background",class:"segment-button-indicator-background"})))}get el(){return I(this)}static get watchers(){return{value:["valueChanged"]}}};return b.style={ios:$,md:_},b})();export{Z as ion_segment,tt as ion_segment_button};
