import{A as l,Db as g,F as t,G as i,H as n,M as o,X as r,fb as p,ga as s,rb as b,sb as c,xb as d}from"./chunk-YFIZFQXH.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import"./chunk-LNJ3S2LQ.js";var w=(()=>{let e=class e{constructor(){}};e.\u0275fac=function(m){return new(m||e)},e.\u0275cmp=l({type:e,selectors:[["app-tabs"]],decls:18,vars:0,consts:[["slot","bottom"],["tab","home"],["src","assets/homePage.png",2,"width","24px","height","24px","display","block","margin","auto"],["tab","search"],["src","assets/searchPlace.png",2,"width","24px","height","24px","display","block","margin","auto"],["tab","map"],["src","assets/map.png",2,"width","24px","height","24px","display","block","margin","auto"],["tab","profile"],["src","assets/setting.png",2,"width","24px","height","24px","display","block","margin","auto"]],template:function(m,h){m&1&&(t(0,"ion-tabs")(1,"ion-tab-bar",0)(2,"ion-tab-button",1),n(3,"img",2),t(4,"ion-label"),o(5,"Home"),i()(),t(6,"ion-tab-button",3),n(7,"img",4),t(8,"ion-label"),o(9,"Search"),i()(),t(10,"ion-tab-button",5),n(11,"img",6),t(12,"ion-label"),o(13,"Map"),i()(),t(14,"ion-tab-button",7),n(15,"img",8),t(16,"ion-label"),o(17,"Settings"),i()()()())},dependencies:[g,p,b,c,d,r,s],encapsulation:2});let a=e;return a})();export{w as TabsPage};
