import{a as g}from"./chunk-DGJ3H4GJ.js";import{a as C,c as A}from"./chunk-IP3IRUG3.js";import{i as f,j as u,l as b,m as y,n as p,q as k,t as x,u as v}from"./chunk-UKIOCGZG.js";import{a as o}from"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import{c as w}from"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import{b as c}from"./chunk-L5T6STQ3.js";import{b as L,f as a,g as D,j as T,k as s}from"./chunk-3EJRMEWO.js";import{i as h}from"./chunk-GNOVVPTF.js";import{a as d}from"./chunk-BAKMWPBW.js";import"./chunk-OBXDPQ3V.js";import{h as l}from"./chunk-LNJ3S2LQ.js";var E=n=>{let i=o(),e=o(),t=o();return e.addElement(n.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),t.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.01,transform:"scale(1.1)"},{offset:1,opacity:1,transform:"scale(1)"}]),i.addElement(n).easing("ease-in-out").duration(200).addAnimation([e,t])},I=n=>{let i=o(),e=o(),t=o();return e.addElement(n.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),t.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.99,transform:"scale(1)"},{offset:1,opacity:0,transform:"scale(0.9)"}]),i.addElement(n).easing("ease-in-out").duration(200).addAnimation([e,t])},M=n=>{let i=o(),e=o(),t=o();return e.addElement(n.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),t.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.01,transform:"scale(1.1)"},{offset:1,opacity:1,transform:"scale(1)"}]),i.addElement(n).easing("ease-in-out").duration(200).addAnimation([e,t])},O=n=>{let i=o(),e=o(),t=o();return e.addElement(n.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),t.addElement(n.querySelector(".loading-wrapper")).keyframes([{offset:0,opacity:.99,transform:"scale(1)"},{offset:1,opacity:0,transform:"scale(0.9)"}]),i.addElement(n).easing("ease-in-out").duration(200).addAnimation([e,t])},z=".sc-ion-loading-ios-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-ios-h{display:none}.loading-wrapper.sc-ion-loading-ios{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-ios{color:var(--spinner-color)}.sc-ion-loading-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, var(--ion-background-color-step-100, #f9f9f9)));--max-width:270px;--max-height:90%;--spinner-color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));--backdrop-opacity:var(--ion-backdrop-opacity, 0.3);color:var(--ion-text-color, #000);font-size:0.875rem}.loading-wrapper.sc-ion-loading-ios{border-radius:8px;-webkit-padding-start:34px;padding-inline-start:34px;-webkit-padding-end:34px;padding-inline-end:34px;padding-top:24px;padding-bottom:24px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.loading-translucent.sc-ion-loading-ios-h .loading-wrapper.sc-ion-loading-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.loading-content.sc-ion-loading-ios{font-weight:bold}.loading-spinner.sc-ion-loading-ios+.loading-content.sc-ion-loading-ios{-webkit-margin-start:16px;margin-inline-start:16px}",B=z,P=".sc-ion-loading-md-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-md-h{display:none}.loading-wrapper.sc-ion-loading-md{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-md{color:var(--spinner-color)}.sc-ion-loading-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--max-width:280px;--max-height:90%;--spinner-color:var(--ion-color-primary, #0054e9);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));font-size:0.875rem}.loading-wrapper.sc-ion-loading-md{border-radius:2px;-webkit-padding-start:24px;padding-inline-start:24px;-webkit-padding-end:24px;padding-inline-end:24px;padding-top:24px;padding-bottom:24px;-webkit-box-shadow:0 16px 20px rgba(0, 0, 0, 0.4);box-shadow:0 16px 20px rgba(0, 0, 0, 0.4)}.loading-spinner.sc-ion-loading-md+.loading-content.sc-ion-loading-md{-webkit-margin-start:16px;margin-inline-start:16px}",j=P,X=(()=>{let n=class{constructor(i){L(this,i),this.didPresent=s(this,"ionLoadingDidPresent",7),this.willPresent=s(this,"ionLoadingWillPresent",7),this.willDismiss=s(this,"ionLoadingWillDismiss",7),this.didDismiss=s(this,"ionLoadingDidDismiss",7),this.didPresentShorthand=s(this,"didPresent",7),this.willPresentShorthand=s(this,"willPresent",7),this.willDismissShorthand=s(this,"willDismiss",7),this.didDismissShorthand=s(this,"didDismiss",7),this.delegateController=x(this),this.lockController=g(),this.triggerController=v(),this.customHTMLEnabled=d.get("innerHTMLTemplatesEnabled",A),this.presented=!1,this.onBackdropTap=()=>{this.dismiss(void 0,k)},this.overlayIndex=void 0,this.delegate=void 0,this.hasController=!1,this.keyboardClose=!0,this.enterAnimation=void 0,this.leaveAnimation=void 0,this.message=void 0,this.cssClass=void 0,this.duration=0,this.backdropDismiss=!1,this.showBackdrop=!0,this.spinner=void 0,this.translucent=!1,this.animated=!0,this.htmlAttributes=void 0,this.isOpen=!1,this.trigger=void 0}onIsOpenChange(i,e){i===!0&&e===!1?this.present():i===!1&&e===!0&&this.dismiss()}triggerChanged(){let{trigger:i,el:e,triggerController:t}=this;i&&t.addClickListener(e,i)}connectedCallback(){f(this.el),this.triggerChanged()}componentWillLoad(){var i;if(this.spinner===void 0){let e=c(this);this.spinner=d.get("loadingSpinner",d.get("spinner",e==="ios"?"lines":"crescent"))}!((i=this.htmlAttributes)===null||i===void 0)&&i.id||u(this.el)}componentDidLoad(){this.isOpen===!0&&h(()=>this.present()),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}present(){return l(this,null,function*(){let i=yield this.lockController.lock();yield this.delegateController.attachViewToDom(),yield b(this,"loadingEnter",E,M),this.duration>0&&(this.durationTimeout=setTimeout(()=>this.dismiss(),this.duration+10)),i()})}dismiss(i,e){return l(this,null,function*(){let t=yield this.lockController.lock();this.durationTimeout&&clearTimeout(this.durationTimeout);let r=yield y(this,i,e,"loadingLeave",I,O);return r&&this.delegateController.removeViewFromDom(),t(),r})}onDidDismiss(){return p(this.el,"ionLoadingDidDismiss")}onWillDismiss(){return p(this.el,"ionLoadingWillDismiss")}renderLoadingMessage(i){let{customHTMLEnabled:e,message:t}=this;return e?a("div",{class:"loading-content",id:i,innerHTML:C(t)}):a("div",{class:"loading-content",id:i},t)}render(){let{message:i,spinner:e,htmlAttributes:t,overlayIndex:r}=this,S=c(this),m=`loading-${r}-msg`;return a(D,Object.assign({key:"d6066c8b56b1fe4b597a243a7dab191ef0d21286",role:"dialog","aria-modal":"true","aria-labelledby":i!==void 0?m:null,tabindex:"-1"},t,{style:{zIndex:`${4e4+this.overlayIndex}`},onIonBackdropTap:this.onBackdropTap,class:Object.assign(Object.assign({},w(this.cssClass)),{[S]:!0,"overlay-hidden":!0,"loading-translucent":this.translucent})}),a("ion-backdrop",{key:"2431eda00a2a3f510f5dfc39b7c7d47c056dfa3d",visible:this.showBackdrop,tappable:this.backdropDismiss}),a("div",{key:"cf210aaf5e754e4eccdb49cf7ead4647b3f9b2d1",tabindex:"0","aria-hidden":"true"}),a("div",{key:"fa9375143d391656d70e181d25b952c77c2fc6ec",class:"loading-wrapper ion-overlay-wrapper"},e&&a("div",{key:"8e4a4ed994f7f62df86b03696ac95162df41f52d",class:"loading-spinner"},a("ion-spinner",{key:"e5b323c272d365853ba92bd211e390b4fd4751d2",name:e,"aria-hidden":"true"})),i!==void 0&&this.renderLoadingMessage(m)),a("div",{key:"cae35ec8c34800477bff3ebcec8010e632158233",tabindex:"0","aria-hidden":"true"}))}get el(){return T(this)}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}};return n.style={ios:B,md:j},n})();export{X as ion_loading};
