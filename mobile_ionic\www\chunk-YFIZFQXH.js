import{b as Kc}from"./chunk-IP3IRUG3.js";import{c as Wc,e as qc,f as Zc,g as Yc,h as Qc}from"./chunk-UKIOCGZG.js";import{b as Vc,c as Bc,d as Uc,e as Hc,f as $c}from"./chunk-RXV5ZBOB.js";import{f as fh}from"./chunk-ODNE7IRY.js";import{a as vn,b as hh,c as ph,d as gh,f as Lc}from"./chunk-F4H6ZFEG.js";import{a as zc}from"./chunk-JWIEPCRG.js";import{a as mh,d as vh}from"./chunk-WTKF7BA6.js";import{b as qt}from"./chunk-SV7S5NYR.js";import{a as Xn,b as jc,c as Oi}from"./chunk-WTCPO44B.js";import{c as Gc}from"./chunk-L5T6STQ3.js";import{m as yh}from"./chunk-3EJRMEWO.js";import{a as b,b as L,d as Pc,g as u,h as ve}from"./chunk-LNJ3S2LQ.js";function Zt(t){let r=t(n=>{Error.call(n),n.stack=new Error().stack});return r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,r}var rt=Zt(t=>function(){t(this),this.name="EmptyError",this.message="no elements in sequence"});function P(t){return typeof t=="function"}var ki=Zt(t=>function(r){t(this),this.message=r?`${r.length} errors occurred during unsubscription:
${r.map((n,o)=>`${o+1}) ${n.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=r});function yn(t,e){if(t){let r=t.indexOf(e);0<=r&&t.splice(r,1)}}var ue=class t{constructor(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let e;if(!this.closed){this.closed=!0;let{_parentage:r}=this;if(r)if(this._parentage=null,Array.isArray(r))for(let i of r)i.remove(this);else r.remove(this);let{initialTeardown:n}=this;if(P(n))try{n()}catch(i){e=i instanceof ki?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Dh(i)}catch(s){e=e!=null?e:[],s instanceof ki?e=[...e,...s.errors]:e.push(s)}}if(e)throw new ki(e)}}add(e){var r;if(e&&e!==this)if(this.closed)Dh(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=(r=this._finalizers)!==null&&r!==void 0?r:[]).push(e)}}_hasParent(e){let{_parentage:r}=this;return r===e||Array.isArray(r)&&r.includes(e)}_addParent(e){let{_parentage:r}=this;this._parentage=Array.isArray(r)?(r.push(e),r):r?[r,e]:e}_removeParent(e){let{_parentage:r}=this;r===e?this._parentage=null:Array.isArray(r)&&yn(r,e)}remove(e){let{_finalizers:r}=this;r&&yn(r,e),e instanceof t&&e._removeParent(this)}};ue.EMPTY=(()=>{let t=new ue;return t.closed=!0,t})();var Xc=ue.EMPTY;function Fi(t){return t instanceof ue||t&&"closed"in t&&P(t.remove)&&P(t.add)&&P(t.unsubscribe)}function Dh(t){P(t)?t():t.unsubscribe()}var ot={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Jn={setTimeout(t,e,...r){let{delegate:n}=Jn;return n!=null&&n.setTimeout?n.setTimeout(t,e,...r):setTimeout(t,e,...r)},clearTimeout(t){let{delegate:e}=Jn;return((e==null?void 0:e.clearTimeout)||clearTimeout)(t)},delegate:void 0};function Pi(t){Jn.setTimeout(()=>{let{onUnhandledError:e}=ot;if(e)e(t);else throw t})}function io(){}var Ih=Jc("C",void 0,void 0);function Ch(t){return Jc("E",void 0,t)}function bh(t){return Jc("N",t,void 0)}function Jc(t,e,r){return{kind:t,value:e,error:r}}var Dn=null;function er(t){if(ot.useDeprecatedSynchronousErrorHandling){let e=!Dn;if(e&&(Dn={errorThrown:!1,error:null}),t(),e){let{errorThrown:r,error:n}=Dn;if(Dn=null,r)throw n}}else t()}function Eh(t){ot.useDeprecatedSynchronousErrorHandling&&Dn&&(Dn.errorThrown=!0,Dn.error=t)}var In=class extends ue{constructor(e){super(),this.isStopped=!1,e?(this.destination=e,Fi(e)&&e.add(this)):this.destination=MI}static create(e,r,n){return new Yt(e,r,n)}next(e){this.isStopped?tl(bh(e),this):this._next(e)}error(e){this.isStopped?tl(Ch(e),this):(this.isStopped=!0,this._error(e))}complete(){this.isStopped?tl(Ih,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(e){this.destination.next(e)}_error(e){try{this.destination.error(e)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},EI=Function.prototype.bind;function el(t,e){return EI.call(t,e)}var nl=class{constructor(e){this.partialObserver=e}next(e){let{partialObserver:r}=this;if(r.next)try{r.next(e)}catch(n){ji(n)}}error(e){let{partialObserver:r}=this;if(r.error)try{r.error(e)}catch(n){ji(n)}else ji(e)}complete(){let{partialObserver:e}=this;if(e.complete)try{e.complete()}catch(r){ji(r)}}},Yt=class extends In{constructor(e,r,n){super();let o;if(P(e)||!e)o={next:e!=null?e:void 0,error:r!=null?r:void 0,complete:n!=null?n:void 0};else{let i;this&&ot.useDeprecatedNextContext?(i=Object.create(e),i.unsubscribe=()=>this.unsubscribe(),o={next:e.next&&el(e.next,i),error:e.error&&el(e.error,i),complete:e.complete&&el(e.complete,i)}):o=e}this.destination=new nl(o)}};function ji(t){ot.useDeprecatedSynchronousErrorHandling?Eh(t):Pi(t)}function wI(t){throw t}function tl(t,e){let{onStoppedNotification:r}=ot;r&&Jn.setTimeout(()=>r(t,e))}var MI={closed:!0,next:io,error:wI,complete:io};function SI(t,e){let r=typeof e=="object";return new Promise((n,o)=>{let i=new Yt({next:s=>{n(s),i.unsubscribe()},error:o,complete:()=>{r?n(e.defaultValue):o(new rt)}});t.subscribe(i)})}var Li=class extends ue{constructor(e,r){super()}schedule(e,r=0){return this}};var so={setInterval(t,e,...r){let{delegate:n}=so;return n!=null&&n.setInterval?n.setInterval(t,e,...r):setInterval(t,e,...r)},clearInterval(t){let{delegate:e}=so;return((e==null?void 0:e.clearInterval)||clearInterval)(t)},delegate:void 0};var Vi=class extends Li{constructor(e,r){super(e,r),this.scheduler=e,this.work=r,this.pending=!1}schedule(e,r=0){var n;if(this.closed)return this;this.state=e;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,r)),this.pending=!0,this.delay=r,this.id=(n=this.id)!==null&&n!==void 0?n:this.requestAsyncId(i,this.id,r),this}requestAsyncId(e,r,n=0){return so.setInterval(e.flush.bind(e,this),n)}recycleAsyncId(e,r,n=0){if(n!=null&&this.delay===n&&this.pending===!1)return r;r!=null&&so.clearInterval(r)}execute(e,r){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let n=this._execute(e,r);if(n)return n;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(e,r){let n=!1,o;try{this.work(e)}catch(i){n=!0,o=i||new Error("Scheduled action threw falsy error")}if(n)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:e,scheduler:r}=this,{actions:n}=r;this.work=this.state=this.scheduler=null,this.pending=!1,yn(n,this),e!=null&&(this.id=this.recycleAsyncId(r,e,null)),this.delay=null,super.unsubscribe()}}};var rl={now(){return(rl.delegate||Date).now()},delegate:void 0};var tr=class t{constructor(e,r=t.now){this.schedulerActionCtor=e,this.now=r}schedule(e,r=0,n){return new this.schedulerActionCtor(this,e).schedule(n,r)}};tr.now=rl.now;var Bi=class extends tr{constructor(e,r=tr.now){super(e,r),this.actions=[],this._active=!1}flush(e){let{actions:r}=this;if(this._active){r.push(e);return}let n;this._active=!0;do if(n=e.execute(e.state,e.delay))break;while(e=r.shift());if(this._active=!1,n){for(;e=r.shift();)e.unsubscribe();throw n}}};var ao=new Bi(Vi),wh=ao;var nr=typeof Symbol=="function"&&Symbol.observable||"@@observable";function be(t){return t}function ol(...t){return il(t)}function il(t){return t.length===0?be:t.length===1?t[0]:function(r){return t.reduce((n,o)=>o(n),r)}}var q=(()=>{class t{constructor(r){r&&(this._subscribe=r)}lift(r){let n=new t;return n.source=this,n.operator=r,n}subscribe(r,n,o){let i=_I(r)?r:new Yt(r,n,o);return er(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(r){try{return this._subscribe(r)}catch(n){r.error(n)}}forEach(r,n){return n=Mh(n),new n((o,i)=>{let s=new Yt({next:a=>{try{r(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(r){var n;return(n=this.source)===null||n===void 0?void 0:n.subscribe(r)}[nr](){return this}pipe(...r){return il(r)(this)}toPromise(r){return r=Mh(r),new r((n,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>n(i))})}}return t.create=e=>new t(e),t})();function Mh(t){var e;return(e=t!=null?t:ot.Promise)!==null&&e!==void 0?e:Promise}function TI(t){return t&&P(t.next)&&P(t.error)&&P(t.complete)}function _I(t){return t&&t instanceof In||TI(t)&&Fi(t)}function Ui(t){return t&&P(t.schedule)}function Hi(t){return t instanceof Date&&!isNaN(t)}function $i(t=0,e,r=wh){let n=-1;return e!=null&&(Ui(e)?r=e:n=e),new q(o=>{let i=Hi(t)?+t-r.now():t;i<0&&(i=0);let s=0;return r.schedule(function(){o.closed||(o.next(s++),0<=n?this.schedule(void 0,n):o.complete())},i)})}function xI(t=0,e=ao){return t<0&&(t=0),$i(t,t,e)}function sl(t){return P(t==null?void 0:t.lift)}function H(t){return e=>{if(sl(e))return e.lift(function(r){try{return t(r,this)}catch(n){this.error(n)}});throw new TypeError("Unable to lift unknown Observable type")}}function B(t,e,r,n,o){return new al(t,e,r,n,o)}var al=class extends In{constructor(e,r,n,o,i,s){super(e),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=r?function(a){try{r(a)}catch(c){e.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){e.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=n?function(){try{n()}catch(a){e.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var e;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:r}=this;super.unsubscribe(),!r&&((e=this.onFinalize)===null||e===void 0||e.call(this))}}};function rr(){return H((t,e)=>{let r=null;t._refCount++;let n=B(e,void 0,void 0,void 0,()=>{if(!t||t._refCount<=0||0<--t._refCount){r=null;return}let o=t._connection,i=r;r=null,o&&(!i||o===i)&&o.unsubscribe(),e.unsubscribe()});t.subscribe(n),n.closed||(r=t.connect())})}var or=class extends q{constructor(e,r){super(),this.source=e,this.subjectFactory=r,this._subject=null,this._refCount=0,this._connection=null,sl(e)&&(this.lift=e.lift)}_subscribe(e){return this.getSubject().subscribe(e)}getSubject(){let e=this._subject;return(!e||e.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:e}=this;this._subject=this._connection=null,e==null||e.unsubscribe()}connect(){let e=this._connection;if(!e){e=this._connection=new ue;let r=this.getSubject();e.add(this.source.subscribe(B(r,void 0,()=>{this._teardown(),r.complete()},n=>{this._teardown(),r.error(n)},()=>this._teardown()))),e.closed&&(this._connection=null,e=ue.EMPTY)}return e}refCount(){return rr()(this)}};var Sh=Zt(t=>function(){t(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var ee=(()=>{class t extends q{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(r){let n=new zi(this,this);return n.operator=r,n}_throwIfClosed(){if(this.closed)throw new Sh}next(r){er(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let n of this.currentObservers)n.next(r)}})}error(r){er(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=r;let{observers:n}=this;for(;n.length;)n.shift().error(r)}})}complete(){er(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:r}=this;for(;r.length;)r.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var r;return((r=this.observers)===null||r===void 0?void 0:r.length)>0}_trySubscribe(r){return this._throwIfClosed(),super._trySubscribe(r)}_subscribe(r){return this._throwIfClosed(),this._checkFinalizedStatuses(r),this._innerSubscribe(r)}_innerSubscribe(r){let{hasError:n,isStopped:o,observers:i}=this;return n||o?Xc:(this.currentObservers=null,i.push(r),new ue(()=>{this.currentObservers=null,yn(i,r)}))}_checkFinalizedStatuses(r){let{hasError:n,thrownError:o,isStopped:i}=this;n?r.error(o):i&&r.complete()}asObservable(){let r=new q;return r.source=this,r}}return t.create=(e,r)=>new zi(e,r),t})(),zi=class extends ee{constructor(e,r){super(),this.destination=e,this.source=r}next(e){var r,n;(n=(r=this.destination)===null||r===void 0?void 0:r.next)===null||n===void 0||n.call(r,e)}error(e){var r,n;(n=(r=this.destination)===null||r===void 0?void 0:r.error)===null||n===void 0||n.call(r,e)}complete(){var e,r;(r=(e=this.destination)===null||e===void 0?void 0:e.complete)===null||r===void 0||r.call(e)}_subscribe(e){var r,n;return(n=(r=this.source)===null||r===void 0?void 0:r.subscribe(e))!==null&&n!==void 0?n:Xc}};var pe=class extends ee{constructor(e){super(),this._value=e}get value(){return this.getValue()}_subscribe(e){let r=super._subscribe(e);return!r.closed&&e.next(this._value),r}getValue(){let{hasError:e,thrownError:r,_value:n}=this;if(e)throw r;return this._throwIfClosed(),n}next(e){super.next(this._value=e)}};var Oe=new q(t=>t.complete());function Th(t){return t[t.length-1]}function Gi(t){return P(Th(t))?t.pop():void 0}function Qt(t){return Ui(Th(t))?t.pop():void 0}var cl=function(t,e){return cl=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(r[o]=n[o])},cl(t,e)};function s1(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");cl(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}function M(t,e,r,n){var o=arguments.length,i=o<3?e:n===null?n=Object.getOwnPropertyDescriptor(e,r):n,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(i=(o<3?s(i):o>3?s(e,r,i):s(e,r))||i);return o>3&&i&&Object.defineProperty(e,r,i),i}function xh(t,e,r,n){function o(i){return i instanceof r?i:new r(function(s){s(i)})}return new(r||(r=Promise))(function(i,s){function a(d){try{l(n.next(d))}catch(h){s(h)}}function c(d){try{l(n.throw(d))}catch(h){s(h)}}function l(d){d.done?i(d.value):o(d.value).then(a,c)}l((n=n.apply(t,e||[])).next())})}function _h(t){var e=typeof Symbol=="function"&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function Cn(t){return this instanceof Cn?(this.v=t,this):new Cn(t)}function Ah(t,e,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=r.apply(t,e||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(p){return function(v){return Promise.resolve(v).then(p,h)}}function a(p,v){n[p]&&(o[p]=function(y){return new Promise(function(x,O){i.push([p,y,x,O])>1||c(p,y)})},v&&(o[p]=v(o[p])))}function c(p,v){try{l(n[p](v))}catch(y){g(i[0][3],y)}}function l(p){p.value instanceof Cn?Promise.resolve(p.value.v).then(d,h):g(i[0][2],p)}function d(p){c("next",p)}function h(p){c("throw",p)}function g(p,v){p(v),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Rh(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],r;return e?e.call(t):(t=typeof _h=="function"?_h(t):t[Symbol.iterator](),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(i){r[i]=t[i]&&function(s){return new Promise(function(a,c){s=t[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var ir=t=>t&&typeof t.length=="number"&&typeof t!="function";function Wi(t){return P(t==null?void 0:t.then)}function qi(t){return P(t[nr])}function Zi(t){return Symbol.asyncIterator&&P(t==null?void 0:t[Symbol.asyncIterator])}function Yi(t){return new TypeError(`You provided ${t!==null&&typeof t=="object"?"an invalid object":`'${t}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function AI(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Qi=AI();function Ki(t){return P(t==null?void 0:t[Qi])}function Xi(t){return Ah(this,arguments,function*(){let r=t.getReader();try{for(;;){let{value:n,done:o}=yield Cn(r.read());if(o)return yield Cn(void 0);yield yield Cn(n)}}finally{r.releaseLock()}})}function Ji(t){return P(t==null?void 0:t.getReader)}function re(t){if(t instanceof q)return t;if(t!=null){if(qi(t))return RI(t);if(ir(t))return NI(t);if(Wi(t))return OI(t);if(Zi(t))return Nh(t);if(Ki(t))return kI(t);if(Ji(t))return FI(t)}throw Yi(t)}function RI(t){return new q(e=>{let r=t[nr]();if(P(r.subscribe))return r.subscribe(e);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function NI(t){return new q(e=>{for(let r=0;r<t.length&&!e.closed;r++)e.next(t[r]);e.complete()})}function OI(t){return new q(e=>{t.then(r=>{e.closed||(e.next(r),e.complete())},r=>e.error(r)).then(null,Pi)})}function kI(t){return new q(e=>{for(let r of t)if(e.next(r),e.closed)return;e.complete()})}function Nh(t){return new q(e=>{PI(t,e).catch(r=>e.error(r))})}function FI(t){return Nh(Xi(t))}function PI(t,e){var r,n,o,i;return xh(this,void 0,void 0,function*(){try{for(r=Rh(t);n=yield r.next(),!n.done;){let s=n.value;if(e.next(s),e.closed)return}}catch(s){o={error:s}}finally{try{n&&!n.done&&(i=r.return)&&(yield i.call(r))}finally{if(o)throw o.error}}e.complete()})}function Me(t,e,r,n=0,o=!1){let i=e.schedule(function(){r(),o?t.add(this.schedule(null,n)):this.unsubscribe()},n);if(t.add(i),!o)return i}function es(t,e=0){return H((r,n)=>{r.subscribe(B(n,o=>Me(n,t,()=>n.next(o),e),()=>Me(n,t,()=>n.complete(),e),o=>Me(n,t,()=>n.error(o),e)))})}function ts(t,e=0){return H((r,n)=>{n.add(t.schedule(()=>r.subscribe(n),e))})}function Oh(t,e){return re(t).pipe(ts(e),es(e))}function kh(t,e){return re(t).pipe(ts(e),es(e))}function Fh(t,e){return new q(r=>{let n=0;return e.schedule(function(){n===t.length?r.complete():(r.next(t[n++]),r.closed||this.schedule())})})}function Ph(t,e){return new q(r=>{let n;return Me(r,e,()=>{n=t[Qi](),Me(r,e,()=>{let o,i;try{({value:o,done:i}=n.next())}catch(s){r.error(s);return}i?r.complete():r.next(o)},0,!0)}),()=>P(n==null?void 0:n.return)&&n.return()})}function ns(t,e){if(!t)throw new Error("Iterable cannot be null");return new q(r=>{Me(r,e,()=>{let n=t[Symbol.asyncIterator]();Me(r,e,()=>{n.next().then(o=>{o.done?r.complete():r.next(o.value)})},0,!0)})})}function jh(t,e){return ns(Xi(t),e)}function Lh(t,e){if(t!=null){if(qi(t))return Oh(t,e);if(ir(t))return Fh(t,e);if(Wi(t))return kh(t,e);if(Zi(t))return ns(t,e);if(Ki(t))return Ph(t,e);if(Ji(t))return jh(t,e)}throw Yi(t)}function oe(t,e){return e?Lh(t,e):re(t)}function F(...t){let e=Qt(t);return oe(t,e)}function sr(t,e){let r=P(t)?t:()=>t,n=o=>o.error(r());return new q(e?o=>e.schedule(n,0,o):n)}function ll(t){return!!t&&(t instanceof q||P(t.lift)&&P(t.subscribe))}var jI=Zt(t=>function(r=null){t(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=r});function LI(t,e){let{first:r,each:n,with:o=VI,scheduler:i=e!=null?e:ao,meta:s=null}=Hi(t)?{first:t}:typeof t=="number"?{each:t}:t;if(r==null&&n==null)throw new TypeError("No timeout provided.");return H((a,c)=>{let l,d,h=null,g=0,p=v=>{d=Me(c,i,()=>{try{l.unsubscribe(),re(o({meta:s,lastValue:h,seen:g})).subscribe(c)}catch(y){c.error(y)}},v)};l=a.subscribe(B(c,v=>{d==null||d.unsubscribe(),g++,c.next(h=v),n>0&&p(n)},void 0,void 0,()=>{d!=null&&d.closed||d==null||d.unsubscribe(),h=null})),!g&&p(r!=null?typeof r=="number"?r:+r-i.now():n)})}function VI(t){throw new jI(t)}function $(t,e){return H((r,n)=>{let o=0;r.subscribe(B(n,i=>{n.next(t.call(e,i,o++))}))})}var{isArray:BI}=Array;function UI(t,e){return BI(e)?t(...e):t(e)}function ar(t){return $(e=>UI(t,e))}var{isArray:HI}=Array,{getPrototypeOf:$I,prototype:zI,keys:GI}=Object;function rs(t){if(t.length===1){let e=t[0];if(HI(e))return{args:e,keys:null};if(WI(e)){let r=GI(e);return{args:r.map(n=>e[n]),keys:r}}}return{args:t,keys:null}}function WI(t){return t&&typeof t=="object"&&$I(t)===zI}function os(t,e){return t.reduce((r,n,o)=>(r[n]=e[o],r),{})}function bn(...t){let e=Qt(t),r=Gi(t),{args:n,keys:o}=rs(t);if(n.length===0)return oe([],e);let i=new q(qI(n,e,o?s=>os(o,s):be));return r?i.pipe(ar(r)):i}function qI(t,e,r=be){return n=>{Vh(e,()=>{let{length:o}=t,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Vh(e,()=>{let l=oe(t[c],e),d=!1;l.subscribe(B(n,h=>{i[c]=h,d||(d=!0,a--),a||n.next(r(i.slice()))},()=>{--s||n.complete()}))},n)},n)}}function Vh(t,e,r){t?Me(r,t,e):e()}function Bh(t,e,r,n,o,i,s,a){let c=[],l=0,d=0,h=!1,g=()=>{h&&!c.length&&!l&&e.complete()},p=y=>l<n?v(y):c.push(y),v=y=>{i&&e.next(y),l++;let x=!1;re(r(y,d++)).subscribe(B(e,O=>{o==null||o(O),i?p(O):e.next(O)},()=>{x=!0},void 0,()=>{if(x)try{for(l--;c.length&&l<n;){let O=c.shift();s?Me(e,s,()=>v(O)):v(O)}g()}catch(O){e.error(O)}}))};return t.subscribe(B(e,p,()=>{h=!0,g()})),()=>{a==null||a()}}function le(t,e,r=1/0){return P(e)?le((n,o)=>$((i,s)=>e(n,i,o,s))(re(t(n,o))),r):(typeof e=="number"&&(r=e),H((n,o)=>Bh(n,o,t,r)))}function cr(t=1/0){return le(be,t)}function Uh(){return cr(1)}function lr(...t){return Uh()(oe(t,Qt(t)))}function is(t){return new q(e=>{re(t()).subscribe(e)})}function ul(...t){let e=Gi(t),{args:r,keys:n}=rs(t),o=new q(i=>{let{length:s}=r;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let d=0;d<s;d++){let h=!1;re(r[d]).subscribe(B(i,g=>{h||(h=!0,l--),a[d]=g},()=>c--,void 0,()=>{(!c||!h)&&(l||i.next(n?os(n,a):a),i.complete())}))}});return e?o.pipe(ar(e)):o}var ZI=["addListener","removeListener"],YI=["addEventListener","removeEventListener"],QI=["on","off"];function En(t,e,r,n){if(P(r)&&(n=r,r=void 0),n)return En(t,e,r).pipe(ar(n));let[o,i]=JI(t)?YI.map(s=>a=>t[s](e,a,r)):KI(t)?ZI.map(Hh(t,e)):XI(t)?QI.map(Hh(t,e)):[];if(!o&&ir(t))return le(s=>En(s,e,r))(re(t));if(!o)throw new TypeError("Invalid event target");return new q(s=>{let a=(...c)=>s.next(1<c.length?c:c[0]);return o(a),()=>i(a)})}function Hh(t,e){return r=>n=>t[r](e,n)}function KI(t){return P(t.addListener)&&P(t.removeListener)}function XI(t){return P(t.on)&&P(t.off)}function JI(t){return P(t.addEventListener)&&P(t.removeEventListener)}function ye(t,e){return H((r,n)=>{let o=0;r.subscribe(B(n,i=>t.call(e,i,o++)&&n.next(i)))})}function pt(t){return H((e,r)=>{let n=null,o=!1,i;n=e.subscribe(B(r,void 0,void 0,s=>{i=re(t(s,pt(t)(e))),n?(n.unsubscribe(),n=null,i.subscribe(r)):o=!0})),o&&(n.unsubscribe(),n=null,i.subscribe(r))})}function $h(t,e,r,n,o){return(i,s)=>{let a=r,c=e,l=0;i.subscribe(B(s,d=>{let h=l++;c=a?t(c,d,h):(a=!0,d),n&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function gt(t,e){return P(e)?le(t,e,1):le(t,1)}function Kt(t){return H((e,r)=>{let n=!1;e.subscribe(B(r,o=>{n=!0,r.next(o)},()=>{n||r.next(t),r.complete()}))})}function kt(t){return t<=0?()=>Oe:H((e,r)=>{let n=0;e.subscribe(B(r,o=>{++n<=t&&(r.next(o),t<=n&&r.complete())}))})}function dl(t,e=be){return t=t!=null?t:eC,H((r,n)=>{let o,i=!0;r.subscribe(B(n,s=>{let a=e(s);(i||!t(o,a))&&(i=!1,o=a,n.next(s))}))})}function eC(t,e){return t===e}function ss(t=tC){return H((e,r)=>{let n=!1;e.subscribe(B(r,o=>{n=!0,r.next(o)},()=>n?r.complete():r.error(t())))})}function tC(){return new rt}function Xt(t){return H((e,r)=>{try{e.subscribe(r)}finally{r.add(t)}})}function Ft(t,e){let r=arguments.length>=2;return n=>n.pipe(t?ye((o,i)=>t(o,i,n)):be,kt(1),r?Kt(e):ss(()=>new rt))}function ur(t){return t<=0?()=>Oe:H((e,r)=>{let n=[];e.subscribe(B(r,o=>{n.push(o),t<n.length&&n.shift()},()=>{for(let o of n)r.next(o);r.complete()},void 0,()=>{n=null}))})}function fl(t,e){let r=arguments.length>=2;return n=>n.pipe(t?ye((o,i)=>t(o,i,n)):be,ur(1),r?Kt(e):ss(()=>new rt))}function nC(t=1/0){let e;t&&typeof t=="object"?e=t:e={count:t};let{count:r=1/0,delay:n,resetOnSuccess:o=!1}=e;return r<=0?be:H((i,s)=>{let a=0,c,l=()=>{let d=!1;c=i.subscribe(B(s,h=>{o&&(a=0),s.next(h)},void 0,h=>{if(a++<r){let g=()=>{c?(c.unsubscribe(),c=null,l()):d=!0};if(n!=null){let p=typeof n=="number"?$i(n):re(n(h,a)),v=B(s,()=>{v.unsubscribe(),g()},()=>{s.complete()});p.subscribe(v)}else g()}else s.error(h)})),d&&(c.unsubscribe(),c=null,l())};l()})}function hl(t,e){return H($h(t,e,arguments.length>=2,!0))}function pl(...t){let e=Qt(t);return H((r,n)=>{(e?lr(t,r,e):lr(t,r)).subscribe(n)})}function De(t,e){return H((r,n)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&n.complete();r.subscribe(B(n,c=>{o==null||o.unsubscribe();let l=0,d=i++;re(t(c,d)).subscribe(o=B(n,h=>n.next(e?e(c,h,d,l++):h),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function gl(t){return H((e,r)=>{re(t).subscribe(B(r,()=>r.complete(),io)),!r.closed&&e.subscribe(r)})}function Ee(t,e,r){let n=P(t)||e||r?{next:t,error:e,complete:r}:t;return n?H((o,i)=>{var s;(s=n.subscribe)===null||s===void 0||s.call(n);let a=!0;o.subscribe(B(i,c=>{var l;(l=n.next)===null||l===void 0||l.call(n,c),i.next(c)},()=>{var c;a=!1,(c=n.complete)===null||c===void 0||c.call(n),i.complete()},c=>{var l;a=!1,(l=n.error)===null||l===void 0||l.call(n,c),i.error(c)},()=>{var c,l;a&&((c=n.unsubscribe)===null||c===void 0||c.call(n)),(l=n.finalize)===null||l===void 0||l.call(n)}))}):be}function Il(t,e){return Object.is(t,e)}var fe=null,as=!1,Cl=1,Ye=Symbol("SIGNAL");function Z(t){let e=fe;return fe=t,e}function bl(){return fe}var lo={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function uo(t){if(as)throw new Error("");if(fe===null)return;fe.consumerOnSignalRead(t);let e=fe.nextProducerIndex++;if(ds(fe),e<fe.producerNode.length&&fe.producerNode[e]!==t&&co(fe)){let r=fe.producerNode[e];us(r,fe.producerIndexOfThis[e])}fe.producerNode[e]!==t&&(fe.producerNode[e]=t,fe.producerIndexOfThis[e]=co(fe)?Gh(t,fe,e):0),fe.producerLastReadVersion[e]=t.version}function zh(){Cl++}function El(t){if(!(co(t)&&!t.dirty)&&!(!t.dirty&&t.lastCleanEpoch===Cl)){if(!t.producerMustRecompute(t)&&!Tl(t)){Dl(t);return}t.producerRecomputeValue(t),Dl(t)}}function wl(t){if(t.liveConsumerNode===void 0)return;let e=as;as=!0;try{for(let r of t.liveConsumerNode)r.dirty||rC(r)}finally{as=e}}function Ml(){return(fe==null?void 0:fe.consumerAllowSignalWrites)!==!1}function rC(t){var e;t.dirty=!0,wl(t),(e=t.consumerMarkedDirty)==null||e.call(t,t)}function Dl(t){t.dirty=!1,t.lastCleanEpoch=Cl}function ls(t){return t&&(t.nextProducerIndex=0),Z(t)}function Sl(t,e){if(Z(e),!(!t||t.producerNode===void 0||t.producerIndexOfThis===void 0||t.producerLastReadVersion===void 0)){if(co(t))for(let r=t.nextProducerIndex;r<t.producerNode.length;r++)us(t.producerNode[r],t.producerIndexOfThis[r]);for(;t.producerNode.length>t.nextProducerIndex;)t.producerNode.pop(),t.producerLastReadVersion.pop(),t.producerIndexOfThis.pop()}}function Tl(t){ds(t);for(let e=0;e<t.producerNode.length;e++){let r=t.producerNode[e],n=t.producerLastReadVersion[e];if(n!==r.version||(El(r),n!==r.version))return!0}return!1}function _l(t){if(ds(t),co(t))for(let e=0;e<t.producerNode.length;e++)us(t.producerNode[e],t.producerIndexOfThis[e]);t.producerNode.length=t.producerLastReadVersion.length=t.producerIndexOfThis.length=0,t.liveConsumerNode&&(t.liveConsumerNode.length=t.liveConsumerIndexOfThis.length=0)}function Gh(t,e,r){if(Wh(t),t.liveConsumerNode.length===0&&qh(t))for(let n=0;n<t.producerNode.length;n++)t.producerIndexOfThis[n]=Gh(t.producerNode[n],t,n);return t.liveConsumerIndexOfThis.push(r),t.liveConsumerNode.push(e)-1}function us(t,e){if(Wh(t),t.liveConsumerNode.length===1&&qh(t))for(let n=0;n<t.producerNode.length;n++)us(t.producerNode[n],t.producerIndexOfThis[n]);let r=t.liveConsumerNode.length-1;if(t.liveConsumerNode[e]=t.liveConsumerNode[r],t.liveConsumerIndexOfThis[e]=t.liveConsumerIndexOfThis[r],t.liveConsumerNode.length--,t.liveConsumerIndexOfThis.length--,e<t.liveConsumerNode.length){let n=t.liveConsumerIndexOfThis[e],o=t.liveConsumerNode[e];ds(o),o.producerIndexOfThis[n]=e}}function co(t){var e,r;return t.consumerIsAlwaysLive||((r=(e=t==null?void 0:t.liveConsumerNode)==null?void 0:e.length)!=null?r:0)>0}function ds(t){var e,r,n;(e=t.producerNode)!=null||(t.producerNode=[]),(r=t.producerIndexOfThis)!=null||(t.producerIndexOfThis=[]),(n=t.producerLastReadVersion)!=null||(t.producerLastReadVersion=[])}function Wh(t){var e,r;(e=t.liveConsumerNode)!=null||(t.liveConsumerNode=[]),(r=t.liveConsumerIndexOfThis)!=null||(t.liveConsumerIndexOfThis=[])}function qh(t){return t.producerNode!==void 0}function xl(t,e){let r=Object.create(oC);r.computation=t,e!==void 0&&(r.equal=e);let n=()=>{if(El(r),uo(r),r.value===cs)throw r.error;return r.value};return n[Ye]=r,n}var ml=Symbol("UNSET"),vl=Symbol("COMPUTING"),cs=Symbol("ERRORED"),oC=L(b({},lo),{value:ml,dirty:!0,error:null,equal:Il,kind:"computed",producerMustRecompute(t){return t.value===ml||t.value===vl},producerRecomputeValue(t){if(t.value===vl)throw new Error("Detected cycle in computations.");let e=t.value;t.value=vl;let r=ls(t),n,o=!1;try{n=t.computation(),Z(null),o=e!==ml&&e!==cs&&n!==cs&&t.equal(e,n)}catch(i){n=cs,t.error=i}finally{Sl(t,r)}if(o){t.value=e;return}t.value=n,t.version++}});function iC(){throw new Error}var Zh=iC;function Yh(t){Zh(t)}function Al(t){Zh=t}var yl=null;function Rl(t,e){let r=Object.create(fs);r.value=t,e!==void 0&&(r.equal=e);let n=()=>(uo(r),r.value);return n[Ye]=r,n}function fo(t,e){Ml()||Yh(t),t.equal(t.value,e)||(t.value=e,sC(t))}function Nl(t,e){Ml()||Yh(t),fo(t,e(t.value))}var fs=L(b({},lo),{equal:Il,value:void 0,kind:"signal"});function sC(t){t.version++,zh(),wl(t),yl==null||yl()}function Ol(t){let e=Z(null);try{return t()}finally{Z(e)}}var kl;function ho(){return kl}function Pt(t){let e=kl;return kl=t,e}var hs=Symbol("NotFound");var qp="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",R=class extends Error{code;constructor(e,r){super(Zp(e,r)),this.code=e}};function uC(t){return`NG0${Math.abs(t)}`}function Zp(t,e){return`${uC(t)}${e?": "+e:""}`}var Yp=Symbol("InputSignalNode#UNSET"),dC=L(b({},fs),{transformFn:void 0,applyValueToInputSignal(t,e){fo(t,e)}});function Qp(t,e){let r=Object.create(dC);r.value=t,r.transformFn=e==null?void 0:e.transform;function n(){if(uo(r),r.value===Yp){let i=null;throw new R(-950,i)}return r.value}return n[Ye]=r,n}function Ar(t){return{toString:t}.toString()}var dr="__annotations__",fr="__parameters__",Qh="__prop__metadata__";function fC(t,e,r,n,o){return Ar(()=>{let i=Kp(e);function s(...a){if(this instanceof s)return i.call(this,...a),this;let c=new s(...a);return function(d){return o&&o(d,...a),(d.hasOwnProperty(dr)?d[dr]:Object.defineProperty(d,dr,{value:[]})[dr]).push(c),d}}return r&&(s.prototype=Object.create(r.prototype)),s.prototype.ngMetadataName=t,s.annotationCls=s,s})}function Kp(t){return function(...r){if(t){let n=t(...r);for(let o in n)this[o]=n[o]}}}function Rr(t,e,r){return Ar(()=>{let n=Kp(e);function o(...i){if(this instanceof o)return n.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,d){let h=c.hasOwnProperty(fr)?c[fr]:Object.defineProperty(c,fr,{value:[]})[fr];for(;h.length<=d;)h.push(null);return(h[d]=h[d]||[]).push(s),c}}return o.prototype.ngMetadataName=t,o.annotationCls=o,o})}var Fe=globalThis;function J(t){for(let e in t)if(t[e]===J)return e;throw Error("Could not find renamed property on target object.")}function hC(t,e){for(let r in e)e.hasOwnProperty(r)&&!t.hasOwnProperty(r)&&(t[r]=e[r])}function ke(t){if(typeof t=="string")return t;if(Array.isArray(t))return`[${t.map(ke).join(", ")}]`;if(t==null)return""+t;let e=t.overriddenName||t.name;if(e)return`${e}`;let r=t.toString();if(r==null)return""+r;let n=r.indexOf(`
`);return n>=0?r.slice(0,n):r}function Kh(t,e){return t?e?`${t} ${e}`:t:e||""}var pC=J({__forward_ref__:J});function $e(t){return t.__forward_ref__=$e,t.toString=function(){return ke(this())},t}function Se(t){return Xp(t)?t():t}function Xp(t){return typeof t=="function"&&t.hasOwnProperty(pC)&&t.__forward_ref__===$e}function A(t){return{token:t.token,providedIn:t.providedIn||null,factory:t.factory,value:void 0}}function _e(t){return{providers:t.providers||[],imports:t.imports||[]}}function Xs(t){return Xh(t,Ss)||Xh(t,eg)}function Jp(t){return Xs(t)!==null}function Xh(t,e){return t.hasOwnProperty(e)?t[e]:null}function gC(t){let e=t&&(t[Ss]||t[eg]);return e||null}function Jh(t){return t&&(t.hasOwnProperty(ep)||t.hasOwnProperty(mC))?t[ep]:null}var Ss=J({\u0275prov:J}),ep=J({\u0275inj:J}),eg=J({ngInjectableDef:J}),mC=J({ngInjectorDef:J}),N=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(e,r){this._desc=e,this.\u0275prov=void 0,typeof r=="number"?this.__NG_ELEMENT_ID__=r:r!==void 0&&(this.\u0275prov=A({token:this,providedIn:r.providedIn||"root",factory:r.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function tg(t){return t&&!!t.\u0275providers}var vC=J({\u0275cmp:J}),yC=J({\u0275dir:J}),DC=J({\u0275pipe:J}),IC=J({\u0275mod:J}),Dr=J({\u0275fac:J}),yo=J({__NG_ELEMENT_ID__:J}),tp=J({__NG_ENV_ID__:J});function _n(t){return typeof t=="string"?t:t==null?"":String(t)}function CC(t){return typeof t=="function"?t.name||t.toString():typeof t=="object"&&t!=null&&typeof t.type=="function"?t.type.name||t.type.toString():_n(t)}function ng(t,e){throw new R(-200,t)}function Zu(t,e){throw new R(-201,!1)}var z=function(t){return t[t.Default=0]="Default",t[t.Host=1]="Host",t[t.Self=2]="Self",t[t.SkipSelf=4]="SkipSelf",t[t.Optional=8]="Optional",t}(z||{}),Ql;function rg(){return Ql}function Qe(t){let e=Ql;return Ql=t,e}function og(t,e,r){let n=Xs(t);if(n&&n.providedIn=="root")return n.value===void 0?n.value=n.factory():n.value;if(r&z.Optional)return null;if(e!==void 0)return e;Zu(t,"Injector")}var bC={},Mn=bC,Kl="__NG_DI_FLAG__",Ts=class{injector;constructor(e){this.injector=e}retrieve(e,r){let n=r;return this.injector.get(e,n.optional?hs:Mn,n)}},_s="ngTempTokenPath",EC="ngTokenPath",wC=/\n/gm,MC="\u0275",np="__source";function SC(t,e=z.Default){if(ho()===void 0)throw new R(-203,!1);if(ho()===null)return og(t,void 0,e);{let r=ho(),n;return r instanceof Ts?n=r.injector:n=r,n.get(t,e&z.Optional?null:void 0,e)}}function k(t,e=z.Default){return(rg()||SC)(Se(t),e)}function TC(t){throw new R(202,!1)}function I(t,e=z.Default){return k(t,Js(e))}function Js(t){return typeof t>"u"||typeof t=="number"?t:0|(t.optional&&8)|(t.host&&1)|(t.self&&2)|(t.skipSelf&&4)}function Xl(t){let e=[];for(let r=0;r<t.length;r++){let n=Se(t[r]);if(Array.isArray(n)){if(n.length===0)throw new R(900,!1);let o,i=z.Default;for(let s=0;s<n.length;s++){let a=n[s],c=_C(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}e.push(k(o,i))}else e.push(k(n))}return e}function To(t,e){return t[Kl]=e,t.prototype[Kl]=e,t}function _C(t){return t[Kl]}function xC(t,e,r,n){let o=t[_s];throw e[np]&&o.unshift(e[np]),t.message=AC(`
`+t.message,o,r,n),t[EC]=o,t[_s]=null,t}function AC(t,e,r,n=null){t=t&&t.charAt(0)===`
`&&t.charAt(1)==MC?t.slice(2):t;let o=ke(e);if(Array.isArray(e))o=e.map(ke).join(" -> ");else if(typeof e=="object"){let i=[];for(let s in e)if(e.hasOwnProperty(s)){let a=e[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):ke(a)))}o=`{${i.join(", ")}}`}return`${r}${n?"("+n+")":""}[${o}]: ${t.replace(wC,`
  `)}`}var RC=To(Rr("Inject",t=>({token:t})),-1),ig=To(Rr("Optional"),8),NC=To(Rr("Self"),2),sg=To(Rr("SkipSelf"),4),OC=To(Rr("Host"),1);function Ir(t,e){let r=t.hasOwnProperty(Dr);return r?t[Dr]:null}function kC(t,e,r){if(t.length!==e.length)return!1;for(let n=0;n<t.length;n++){let o=t[n],i=e[n];if(r&&(o=r(o),i=r(i)),i!==o)return!1}return!0}function FC(t){return t.flat(Number.POSITIVE_INFINITY)}function Yu(t,e){t.forEach(r=>Array.isArray(r)?Yu(r,e):e(r))}function ag(t,e,r){e>=t.length?t.push(r):t.splice(e,0,r)}function xs(t,e){return e>=t.length-1?t.pop():t.splice(e,1)[0]}function Is(t,e){let r=[];for(let n=0;n<t;n++)r.push(e);return r}function PC(t,e,r,n){let o=t.length;if(o==e)t.push(r,n);else if(o===1)t.push(n,t[0]),t[0]=r;else{for(o--,t.push(t[o-1],t[o]);o>e;){let i=o-2;t[o]=t[i],o--}t[e]=r,t[e+1]=n}}function jC(t,e,r){let n=_o(t,e);return n>=0?t[n|1]=r:(n=~n,PC(t,n,e,r)),n}function Fl(t,e){let r=_o(t,e);if(r>=0)return t[r|1]}function _o(t,e){return LC(t,e,1)}function LC(t,e,r){let n=0,o=t.length>>r;for(;o!==n;){let i=n+(o-n>>1),s=t[i<<r];if(e===s)return i<<r;s>e?o=i:n=i+1}return~(o<<r)}var xn={},Ke=[],Io=new N(""),cg=new N("",-1),lg=new N(""),As=class{get(e,r=Mn){if(r===Mn){let n=new Error(`NullInjectorError: No provider for ${ke(e)}!`);throw n.name="NullInjectorError",n}return r}};function ug(t,e){let r=t[IC]||null;if(!r&&e===!0)throw new Error(`Type ${ke(t)} does not have '\u0275mod' property.`);return r}function nn(t){return t[vC]||null}function VC(t){return t[yC]||null}function BC(t){return t[DC]||null}function ea(t){return{\u0275providers:t}}function UC(...t){return{\u0275providers:dg(!0,t),\u0275fromNgModule:!0}}function dg(t,...e){let r=[],n=new Set,o,i=s=>{r.push(s)};return Yu(e,s=>{let a=s;Jl(a,i,[],n)&&(o||(o=[]),o.push(a))}),o!==void 0&&fg(o,i),r}function fg(t,e){for(let r=0;r<t.length;r++){let{ngModule:n,providers:o}=t[r];Qu(o,i=>{e(i,n)})}}function Jl(t,e,r,n){if(t=Se(t),!t)return!1;let o=null,i=Jh(t),s=!i&&nn(t);if(!i&&!s){let c=t.ngModule;if(i=Jh(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=t}let a=n.has(o);if(s){if(a)return!1;if(n.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)Jl(l,e,r,n)}}else if(i){if(i.imports!=null&&!a){n.add(o);let l;try{Yu(i.imports,d=>{Jl(d,e,r,n)&&(l||(l=[]),l.push(d))})}finally{}l!==void 0&&fg(l,e)}if(!a){let l=Ir(o)||(()=>new o);e({provide:o,useFactory:l,deps:Ke},o),e({provide:lg,useValue:o,multi:!0},o),e({provide:Io,useValue:()=>k(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=t;Qu(c,d=>{e(d,l)})}}else return!1;return o!==t&&t.providers!==void 0}function Qu(t,e){for(let r of t)tg(r)&&(r=r.\u0275providers),Array.isArray(r)?Qu(r,e):e(r)}var HC=J({provide:String,useValue:J});function hg(t){return t!==null&&typeof t=="object"&&HC in t}function $C(t){return!!(t&&t.useExisting)}function zC(t){return!!(t&&t.useFactory)}function Cr(t){return typeof t=="function"}function GC(t){return!!t.useClass}var ta=new N(""),Cs={},rp={},Pl;function na(){return Pl===void 0&&(Pl=new As),Pl}var ae=class{},Co=class extends ae{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(e,r,n,o){super(),this.parent=r,this.source=n,this.scopes=o,tu(e,s=>this.processProvider(s)),this.records.set(cg,hr(void 0,this)),o.has("environment")&&this.records.set(ae,hr(void 0,this));let i=this.records.get(ta);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(lg,Ke,z.Self))}retrieve(e,r){let n=r;return this.get(e,n.optional?hs:Mn,n)}destroy(){go(this),this._destroyed=!0;let e=Z(null);try{for(let n of this._ngOnDestroyHooks)n.ngOnDestroy();let r=this._onDestroyHooks;this._onDestroyHooks=[];for(let n of r)n()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),Z(e)}}onDestroy(e){return go(this),this._onDestroyHooks.push(e),()=>this.removeOnDestroy(e)}runInContext(e){go(this);let r=Pt(this),n=Qe(void 0),o;try{return e()}finally{Pt(r),Qe(n)}}get(e,r=Mn,n=z.Default){if(go(this),e.hasOwnProperty(tp))return e[tp](this);n=Js(n);let o,i=Pt(this),s=Qe(void 0);try{if(!(n&z.SkipSelf)){let c=this.records.get(e);if(c===void 0){let l=QC(e)&&Xs(e);l&&this.injectableDefInScope(l)?c=hr(eu(e),Cs):c=null,this.records.set(e,c)}if(c!=null)return this.hydrate(e,c)}let a=n&z.Self?na():this.parent;return r=n&z.Optional&&r===Mn?null:r,a.get(e,r)}catch(a){if(a.name==="NullInjectorError"){if((a[_s]=a[_s]||[]).unshift(ke(e)),i)throw a;return xC(a,e,"R3InjectorError",this.source)}else throw a}finally{Qe(s),Pt(i)}}resolveInjectorInitializers(){let e=Z(null),r=Pt(this),n=Qe(void 0),o;try{let i=this.get(Io,Ke,z.Self);for(let s of i)s()}finally{Pt(r),Qe(n),Z(e)}}toString(){let e=[],r=this.records;for(let n of r.keys())e.push(ke(n));return`R3Injector[${e.join(", ")}]`}processProvider(e){e=Se(e);let r=Cr(e)?e:Se(e&&e.provide),n=qC(e);if(!Cr(e)&&e.multi===!0){let o=this.records.get(r);o||(o=hr(void 0,Cs,!0),o.factory=()=>Xl(o.multi),this.records.set(r,o)),r=e,o.multi.push(e)}this.records.set(r,n)}hydrate(e,r){let n=Z(null);try{return r.value===rp?ng(ke(e)):r.value===Cs&&(r.value=rp,r.value=r.factory()),typeof r.value=="object"&&r.value&&YC(r.value)&&this._ngOnDestroyHooks.add(r.value),r.value}finally{Z(n)}}injectableDefInScope(e){if(!e.providedIn)return!1;let r=Se(e.providedIn);return typeof r=="string"?r==="any"||this.scopes.has(r):this.injectorDefTypes.has(r)}removeOnDestroy(e){let r=this._onDestroyHooks.indexOf(e);r!==-1&&this._onDestroyHooks.splice(r,1)}};function eu(t){let e=Xs(t),r=e!==null?e.factory:Ir(t);if(r!==null)return r;if(t instanceof N)throw new R(204,!1);if(t instanceof Function)return WC(t);throw new R(204,!1)}function WC(t){if(t.length>0)throw new R(204,!1);let r=gC(t);return r!==null?()=>r.factory(t):()=>new t}function qC(t){if(hg(t))return hr(void 0,t.useValue);{let e=pg(t);return hr(e,Cs)}}function pg(t,e,r){let n;if(Cr(t)){let o=Se(t);return Ir(o)||eu(o)}else if(hg(t))n=()=>Se(t.useValue);else if(zC(t))n=()=>t.useFactory(...Xl(t.deps||[]));else if($C(t))n=()=>k(Se(t.useExisting));else{let o=Se(t&&(t.useClass||t.provide));if(ZC(t))n=()=>new o(...Xl(t.deps));else return Ir(o)||eu(o)}return n}function go(t){if(t.destroyed)throw new R(205,!1)}function hr(t,e,r=!1){return{factory:t,value:e,multi:r?[]:void 0}}function ZC(t){return!!t.deps}function YC(t){return t!==null&&typeof t=="object"&&typeof t.ngOnDestroy=="function"}function QC(t){return typeof t=="function"||typeof t=="object"&&t instanceof N}function tu(t,e){for(let r of t)Array.isArray(r)?tu(r,e):r&&tg(r)?tu(r.\u0275providers,e):e(r)}function je(t,e){let r;t instanceof Co?(go(t),r=t):r=new Ts(t);let n,o=Pt(r),i=Qe(void 0);try{return e()}finally{Pt(o),Qe(i)}}function gg(){return rg()!==void 0||ho()!=null}function KC(t){if(!gg())throw new R(-203,!1)}function nu(t){let e=Fe.ng;if(e&&e.\u0275compilerFacade)return e.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}var op={\u0275\u0275defineInjectable:A,\u0275\u0275defineInjector:_e,\u0275\u0275inject:k,\u0275\u0275invalidFactoryDep:TC,resolveForwardRef:Se},XC=Function;function mo(t){return typeof t=="function"}var JC=/^function\s+\S+\(\)\s*{[\s\S]+\.apply\(this,\s*(arguments|(?:[^()]+\(\[\],)?[^()]+\(arguments\).*)\)/,e0=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{/,t0=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{[\s\S]*constructor\s*\(/,n0=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{[\s\S]*constructor\s*\(\)\s*{[^}]*super\(\.\.\.arguments\)/;function r0(t){return JC.test(t)||n0.test(t)||e0.test(t)&&!t0.test(t)}var ru=class{_reflect;constructor(e){this._reflect=e||Fe.Reflect}factory(e){return(...r)=>new e(...r)}_zipTypesAndAnnotations(e,r){let n;typeof e>"u"?n=Is(r.length):n=Is(e.length);for(let o=0;o<n.length;o++)typeof e>"u"?n[o]=[]:e[o]&&e[o]!=Object?n[o]=[e[o]]:n[o]=[],r&&r[o]!=null&&(n[o]=n[o].concat(r[o]));return n}_ownParameters(e,r){let n=e.toString();if(r0(n))return null;if(e.parameters&&e.parameters!==r.parameters)return e.parameters;let o=e.ctorParameters;if(o&&o!==r.ctorParameters){let a=typeof o=="function"?o():o,c=a.map(d=>d&&d.type),l=a.map(d=>d&&jl(d.decorators));return this._zipTypesAndAnnotations(c,l)}let i=e.hasOwnProperty(fr)&&e[fr],s=this._reflect&&this._reflect.getOwnMetadata&&this._reflect.getOwnMetadata("design:paramtypes",e);return s||i?this._zipTypesAndAnnotations(s,i):Is(e.length)}parameters(e){if(!mo(e))return[];let r=ps(e),n=this._ownParameters(e,r);return!n&&r!==Object&&(n=this.parameters(r)),n||[]}_ownAnnotations(e,r){if(e.annotations&&e.annotations!==r.annotations){let n=e.annotations;return typeof n=="function"&&n.annotations&&(n=n.annotations),n}return e.decorators&&e.decorators!==r.decorators?jl(e.decorators):e.hasOwnProperty(dr)?e[dr]:null}annotations(e){if(!mo(e))return[];let r=ps(e),n=this._ownAnnotations(e,r)||[];return(r!==Object?this.annotations(r):[]).concat(n)}_ownPropMetadata(e,r){if(e.propMetadata&&e.propMetadata!==r.propMetadata){let n=e.propMetadata;return typeof n=="function"&&n.propMetadata&&(n=n.propMetadata),n}if(e.propDecorators&&e.propDecorators!==r.propDecorators){let n=e.propDecorators,o={};return Object.keys(n).forEach(i=>{o[i]=jl(n[i])}),o}return e.hasOwnProperty(Qh)?e[Qh]:null}propMetadata(e){if(!mo(e))return{};let r=ps(e),n={};if(r!==Object){let i=this.propMetadata(r);Object.keys(i).forEach(s=>{n[s]=i[s]})}let o=this._ownPropMetadata(e,r);return o&&Object.keys(o).forEach(i=>{let s=[];n.hasOwnProperty(i)&&s.push(...n[i]),s.push(...o[i]),n[i]=s}),n}ownPropMetadata(e){return mo(e)?this._ownPropMetadata(e,ps(e))||{}:{}}hasLifecycleHook(e,r){return e instanceof XC&&r in e.prototype}};function jl(t){return t?t.map(e=>{let n=e.type.annotationCls,o=e.args?e.args:[];return new n(...o)}):[]}function ps(t){let e=t.prototype?Object.getPrototypeOf(t.prototype):null;return(e?e.constructor:null)||Object}var Vt=0,W=1,V=2,Te=3,st=4,Le=5,bo=6,vr=7,Pe=8,br=9,jt=10,de=11,Eo=12,ip=13,Nr=14,Je=15,An=16,pr=17,at=18,ra=19,mg=20,Jt=21,Ll=22,Rs=23,Xe=24,en=25,et=26,vg=1;var Rn=7,Ns=8,Er=9,He=10;function tn(t){return Array.isArray(t)&&typeof t[vg]=="object"}function Bt(t){return Array.isArray(t)&&t[vg]===!0}function Ku(t){return(t.flags&4)!==0}function Or(t){return t.componentOffset>-1}function oa(t){return(t.flags&1)===1}function vt(t){return!!t.template}function Os(t){return(t[V]&512)!==0}function xo(t){return(t[V]&256)===256}var ou=class{previousValue;currentValue;firstChange;constructor(e,r,n){this.previousValue=e,this.currentValue=r,this.firstChange=n}isFirstChange(){return this.firstChange}};function yg(t,e,r,n){e!==null?e.applyValueToInputSignal(e,n):t[r]=n}var tt=(()=>{let t=()=>Dg;return t.ngInherit=!0,t})();function Dg(t){return t.type.prototype.ngOnChanges&&(t.setInput=i0),o0}function o0(){let t=Cg(this),e=t==null?void 0:t.current;if(e){let r=t.previous;if(r===xn)t.previous=e;else for(let n in e)r[n]=e[n];t.current=null,this.ngOnChanges(e)}}function i0(t,e,r,n,o){let i=this.declaredInputs[n],s=Cg(t)||s0(t,{previous:xn,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new ou(l&&l.currentValue,r,c===xn),yg(t,e,o,r)}var Ig="__ngSimpleChanges__";function Cg(t){return t[Ig]||null}function s0(t,e){return t[Ig]=e}var sp=null;var ie=function(t,e=null,r){sp!=null&&sp(t,e,r)},a0="svg",c0="math";function yt(t){for(;Array.isArray(t);)t=t[Vt];return t}function bg(t,e){return yt(e[t])}function wt(t,e){return yt(e[t.index])}function Eg(t,e){return t.data[e]}function Dt(t,e){let r=e[t];return tn(r)?r:r[Vt]}function l0(t){return(t[V]&4)===4}function Xu(t){return(t[V]&128)===128}function u0(t){return Bt(t[Te])}function wr(t,e){return e==null?null:t[e]}function wg(t){t[pr]=0}function Mg(t){t[V]&1024||(t[V]|=1024,Xu(t)&&Ao(t))}function d0(t,e){for(;t>0;)e=e[Nr],t--;return e}function ia(t){var e;return!!(t[V]&9216||(e=t[Xe])!=null&&e.dirty)}function iu(t){var e;(e=t[jt].changeDetectionScheduler)==null||e.notify(8),t[V]&64&&(t[V]|=1024),ia(t)&&Ao(t)}function Ao(t){var r;(r=t[jt].changeDetectionScheduler)==null||r.notify(0);let e=Nn(t);for(;e!==null&&!(e[V]&8192||(e[V]|=8192,!Xu(e)));)e=Nn(e)}function Sg(t,e){if(xo(t))throw new R(911,!1);t[Jt]===null&&(t[Jt]=[]),t[Jt].push(e)}function f0(t,e){if(t[Jt]===null)return;let r=t[Jt].indexOf(e);r!==-1&&t[Jt].splice(r,1)}function Nn(t){let e=t[Te];return Bt(e)?e[Te]:e}function Ju(t){var e;return(e=t[vr])!=null?e:t[vr]=[]}function ed(t){var e;return(e=t.cleanup)!=null?e:t.cleanup=[]}function h0(t,e,r,n){let o=Ju(e);o.push(r),t.firstCreatePass&&ed(t).push(n,o.length-1)}var G={lFrame:kg(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var su=!1;function p0(){return G.lFrame.elementDepthCount}function g0(){G.lFrame.elementDepthCount++}function m0(){G.lFrame.elementDepthCount--}function td(){return G.bindingsEnabled}function Tg(){return G.skipHydrationRootTNode!==null}function v0(t){return G.skipHydrationRootTNode===t}function y0(){G.skipHydrationRootTNode=null}function Y(){return G.lFrame.lView}function ge(){return G.lFrame.tView}function nd(t){return G.lFrame.contextLView=t,t[Pe]}function rd(t){return G.lFrame.contextLView=null,t}function xe(){let t=_g();for(;t!==null&&t.type===64;)t=t.parent;return t}function _g(){return G.lFrame.currentTNode}function D0(){let t=G.lFrame,e=t.currentTNode;return t.isParent?e:e.parent}function Ln(t,e){let r=G.lFrame;r.currentTNode=t,r.isParent=e}function od(){return G.lFrame.isParent}function id(){G.lFrame.isParent=!1}function xg(){return su}function ap(t){let e=su;return su=t,e}function I0(){let t=G.lFrame,e=t.bindingRootIndex;return e===-1&&(e=t.bindingRootIndex=t.tView.bindingStartIndex),e}function C0(){return G.lFrame.bindingIndex}function b0(t){return G.lFrame.bindingIndex=t}function sa(){return G.lFrame.bindingIndex++}function Ag(t){let e=G.lFrame,r=e.bindingIndex;return e.bindingIndex=e.bindingIndex+t,r}function E0(){return G.lFrame.inI18n}function w0(t,e){let r=G.lFrame;r.bindingIndex=r.bindingRootIndex=t,au(e)}function M0(){return G.lFrame.currentDirectiveIndex}function au(t){G.lFrame.currentDirectiveIndex=t}function S0(t){let e=G.lFrame.currentDirectiveIndex;return e===-1?null:t[e]}function Rg(){return G.lFrame.currentQueryIndex}function sd(t){G.lFrame.currentQueryIndex=t}function T0(t){let e=t[W];return e.type===2?e.declTNode:e.type===1?t[Le]:null}function Ng(t,e,r){if(r&z.SkipSelf){let o=e,i=t;for(;o=o.parent,o===null&&!(r&z.Host);)if(o=T0(i),o===null||(i=i[Nr],o.type&10))break;if(o===null)return!1;e=o,t=i}let n=G.lFrame=Og();return n.currentTNode=e,n.lView=t,!0}function ad(t){let e=Og(),r=t[W];G.lFrame=e,e.currentTNode=r.firstChild,e.lView=t,e.tView=r,e.contextLView=t,e.bindingIndex=r.bindingStartIndex,e.inI18n=!1}function Og(){let t=G.lFrame,e=t===null?null:t.child;return e===null?kg(t):e}function kg(t){let e={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:t,child:null,inI18n:!1};return t!==null&&(t.child=e),e}function Fg(){let t=G.lFrame;return G.lFrame=t.parent,t.currentTNode=null,t.lView=null,t}var Pg=Fg;function cd(){let t=Fg();t.isParent=!0,t.tView=null,t.selectedIndex=-1,t.contextLView=null,t.elementDepthCount=0,t.currentDirectiveIndex=-1,t.currentNamespace=null,t.bindingRootIndex=-1,t.bindingIndex=-1,t.currentQueryIndex=0}function _0(t){return(G.lFrame.contextLView=d0(t,G.lFrame.contextLView))[Pe]}function Vn(){return G.lFrame.selectedIndex}function On(t){G.lFrame.selectedIndex=t}function aa(){let t=G.lFrame;return Eg(t.tView,t.selectedIndex)}function x0(){return G.lFrame.currentNamespace}var jg=!0;function ca(){return jg}function la(t){jg=t}function A0(t,e,r){var s,a,c,l,d;let{ngOnChanges:n,ngOnInit:o,ngDoCheck:i}=e.type.prototype;if(n){let h=Dg(e);((s=r.preOrderHooks)!=null?s:r.preOrderHooks=[]).push(t,h),((a=r.preOrderCheckHooks)!=null?a:r.preOrderCheckHooks=[]).push(t,h)}o&&((c=r.preOrderHooks)!=null?c:r.preOrderHooks=[]).push(0-t,o),i&&(((l=r.preOrderHooks)!=null?l:r.preOrderHooks=[]).push(t,i),((d=r.preOrderCheckHooks)!=null?d:r.preOrderCheckHooks=[]).push(t,i))}function ld(t,e){var r,n,o,i,s,a,c;for(let l=e.directiveStart,d=e.directiveEnd;l<d;l++){let g=t.data[l].type.prototype,{ngAfterContentInit:p,ngAfterContentChecked:v,ngAfterViewInit:y,ngAfterViewChecked:x,ngOnDestroy:O}=g;p&&((r=t.contentHooks)!=null?r:t.contentHooks=[]).push(-l,p),v&&(((n=t.contentHooks)!=null?n:t.contentHooks=[]).push(l,v),((o=t.contentCheckHooks)!=null?o:t.contentCheckHooks=[]).push(l,v)),y&&((i=t.viewHooks)!=null?i:t.viewHooks=[]).push(-l,y),x&&(((s=t.viewHooks)!=null?s:t.viewHooks=[]).push(l,x),((a=t.viewCheckHooks)!=null?a:t.viewCheckHooks=[]).push(l,x)),O!=null&&((c=t.destroyHooks)!=null?c:t.destroyHooks=[]).push(l,O)}}function bs(t,e,r){Lg(t,e,3,r)}function Es(t,e,r,n){(t[V]&3)===r&&Lg(t,e,r,n)}function Vl(t,e){let r=t[V];(r&3)===e&&(r&=16383,r+=1,t[V]=r)}function Lg(t,e,r,n){let o=n!==void 0?t[pr]&65535:0,i=n!=null?n:-1,s=e.length-1,a=0;for(let c=o;c<s;c++)if(typeof e[c+1]=="number"){if(a=e[c],n!=null&&a>=n)break}else e[c]<0&&(t[pr]+=65536),(a<i||i==-1)&&(R0(t,r,e,c),t[pr]=(t[pr]&**********)+c+2),c++}function cp(t,e){ie(4,t,e);let r=Z(null);try{e.call(t)}finally{Z(r),ie(5,t,e)}}function R0(t,e,r,n){let o=r[n]<0,i=r[n+1],s=o?-r[n]:r[n],a=t[s];o?t[V]>>14<t[pr]>>16&&(t[V]&3)===e&&(t[V]+=16384,cp(a,i)):cp(a,i)}var yr=-1,kn=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(e,r,n){this.factory=e,this.canSeeViewProviders=r,this.injectImpl=n}};function N0(t){return(t.flags&8)!==0}function O0(t){return(t.flags&16)!==0}function k0(t,e,r){let n=0;for(;n<r.length;){let o=r[n];if(typeof o=="number"){if(o!==0)break;n++;let i=r[n++],s=r[n++],a=r[n++];t.setAttribute(e,s,a,i)}else{let i=o,s=r[++n];F0(i)?t.setProperty(e,i,s):t.setAttribute(e,i,s),n++}}return n}function Vg(t){return t===3||t===4||t===6}function F0(t){return t.charCodeAt(0)===64}function Mr(t,e){if(!(e===null||e.length===0))if(t===null||t.length===0)t=e.slice();else{let r=-1;for(let n=0;n<e.length;n++){let o=e[n];typeof o=="number"?r=o:r===0||(r===-1||r===2?lp(t,r,o,null,e[++n]):lp(t,r,o,null,null))}}return t}function lp(t,e,r,n,o){let i=0,s=t.length;if(e===-1)s=-1;else for(;i<t.length;){let a=t[i++];if(typeof a=="number"){if(a===e){s=-1;break}else if(a>e){s=i-1;break}}}for(;i<t.length;){let a=t[i];if(typeof a=="number")break;if(a===r){o!==null&&(t[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(t.splice(s,0,e),i=s+1),t.splice(i++,0,r),o!==null&&t.splice(i++,0,o)}function Bg(t){return t!==yr}function ks(t){return t&32767}function P0(t){return t>>16}function Fs(t,e){let r=P0(t),n=e;for(;r>0;)n=n[Nr],r--;return n}var cu=!0;function up(t){let e=cu;return cu=t,e}var j0=256,Ug=j0-1,Hg=5,L0=0,mt={};function V0(t,e,r){let n;typeof r=="string"?n=r.charCodeAt(0)||0:r.hasOwnProperty(yo)&&(n=r[yo]),n==null&&(n=r[yo]=L0++);let o=n&Ug,i=1<<o;e.data[t+(o>>Hg)]|=i}function Ps(t,e){let r=$g(t,e);if(r!==-1)return r;let n=e[W];n.firstCreatePass&&(t.injectorIndex=e.length,Bl(n.data,t),Bl(e,null),Bl(n.blueprint,null));let o=ud(t,e),i=t.injectorIndex;if(Bg(o)){let s=ks(o),a=Fs(o,e),c=a[W].data;for(let l=0;l<8;l++)e[i+l]=a[s+l]|c[s+l]}return e[i+8]=o,i}function Bl(t,e){t.push(0,0,0,0,0,0,0,0,e)}function $g(t,e){return t.injectorIndex===-1||t.parent&&t.parent.injectorIndex===t.injectorIndex||e[t.injectorIndex+8]===null?-1:t.injectorIndex}function ud(t,e){if(t.parent&&t.parent.injectorIndex!==-1)return t.parent.injectorIndex;let r=0,n=null,o=e;for(;o!==null;){if(n=Zg(o),n===null)return yr;if(r++,o=o[Nr],n.injectorIndex!==-1)return n.injectorIndex|r<<16}return yr}function lu(t,e,r){V0(t,e,r)}function B0(t,e){if(e==="class")return t.classes;if(e==="style")return t.styles;let r=t.attrs;if(r){let n=r.length,o=0;for(;o<n;){let i=r[o];if(Vg(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<n&&typeof r[o]=="string";)o++;else{if(i===e)return r[o+1];o=o+2}}}return null}function zg(t,e,r){if(r&z.Optional||t!==void 0)return t;Zu(e,"NodeInjector")}function Gg(t,e,r,n){if(r&z.Optional&&n===void 0&&(n=null),(r&(z.Self|z.Host))===0){let o=t[br],i=Qe(void 0);try{return o?o.get(e,n,r&z.Optional):og(e,n,r&z.Optional)}finally{Qe(i)}}return zg(n,e,r)}function Wg(t,e,r,n=z.Default,o){if(t!==null){if(e[V]&2048&&!(n&z.Self)){let s=z0(t,e,r,n,mt);if(s!==mt)return s}let i=qg(t,e,r,n,mt);if(i!==mt)return i}return Gg(e,r,n,o)}function qg(t,e,r,n,o){let i=H0(r);if(typeof i=="function"){if(!Ng(e,t,n))return n&z.Host?zg(o,r,n):Gg(e,r,n,o);try{let s;if(s=i(n),s==null&&!(n&z.Optional))Zu(r);else return s}finally{Pg()}}else if(typeof i=="number"){let s=null,a=$g(t,e),c=yr,l=n&z.Host?e[Je][Le]:null;for((a===-1||n&z.SkipSelf)&&(c=a===-1?ud(t,e):e[a+8],c===yr||!fp(n,!1)?a=-1:(s=e[W],a=ks(c),e=Fs(c,e)));a!==-1;){let d=e[W];if(dp(i,a,d.data)){let h=U0(a,e,r,s,n,l);if(h!==mt)return h}c=e[a+8],c!==yr&&fp(n,e[W].data[a+8]===l)&&dp(i,a,e)?(s=d,a=ks(c),e=Fs(c,e)):a=-1}}return o}function U0(t,e,r,n,o,i){let s=e[W],a=s.data[t+8],c=n==null?Or(a)&&cu:n!=s&&(a.type&3)!==0,l=o&z.Host&&i===a,d=ws(a,s,r,c,l);return d!==null?wo(e,s,d,a):mt}function ws(t,e,r,n,o){let i=t.providerIndexes,s=e.data,a=i&1048575,c=t.directiveStart,l=t.directiveEnd,d=i>>20,h=n?a:a+d,g=o?a+d:l;for(let p=h;p<g;p++){let v=s[p];if(p<c&&r===v||p>=c&&v.type===r)return p}if(o){let p=s[c];if(p&&vt(p)&&p.type===r)return c}return null}function wo(t,e,r,n){let o=t[r],i=e.data;if(o instanceof kn){let s=o;s.resolving&&ng(CC(i[r]));let a=up(s.canSeeViewProviders);s.resolving=!0;let c,l=s.injectImpl?Qe(s.injectImpl):null,d=Ng(t,n,z.Default);try{o=t[r]=s.factory(void 0,i,t,n),e.firstCreatePass&&r>=n.directiveStart&&A0(r,i[r],e)}finally{l!==null&&Qe(l),up(a),s.resolving=!1,Pg()}}return o}function H0(t){if(typeof t=="string")return t.charCodeAt(0)||0;let e=t.hasOwnProperty(yo)?t[yo]:void 0;return typeof e=="number"?e>=0?e&Ug:$0:e}function dp(t,e,r){let n=1<<t;return!!(r[e+(t>>Hg)]&n)}function fp(t,e){return!(t&z.Self)&&!(t&z.Host&&e)}var Sn=class{_tNode;_lView;constructor(e,r){this._tNode=e,this._lView=r}get(e,r,n){return Wg(this._tNode,this._lView,e,Js(n),r)}};function $0(){return new Sn(xe(),Y())}function we(t){return Ar(()=>{let e=t.prototype.constructor,r=e[Dr]||uu(e),n=Object.prototype,o=Object.getPrototypeOf(t.prototype).constructor;for(;o&&o!==n;){let i=o[Dr]||uu(o);if(i&&i!==r)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function uu(t){return Xp(t)?()=>{let e=uu(Se(t));return e&&e()}:Ir(t)}function z0(t,e,r,n,o){let i=t,s=e;for(;i!==null&&s!==null&&s[V]&2048&&!Os(s);){let a=qg(i,s,r,n|z.Self,mt);if(a!==mt)return a;let c=i.parent;if(!c){let l=s[mg];if(l){let d=l.get(r,mt,n);if(d!==mt)return d}c=Zg(s),s=s[Nr]}i=c}return o}function Zg(t){let e=t[W],r=e.type;return r===2?e.declTNode:r===1?t[Le]:null}function Mt(t){return B0(xe(),t)}var G0=Rr("Attribute",t=>({attributeName:t,__NG_ELEMENT_ID__:()=>Mt(t)})),hp=null;function W0(){return hp=hp||new ru}function q0(t){return Yg(W0().parameters(t))}function Yg(t){return t.map(e=>Z0(e))}function Z0(t){let e={token:null,attribute:null,host:!1,optional:!1,self:!1,skipSelf:!1};if(Array.isArray(t)&&t.length>0)for(let r=0;r<t.length;r++){let n=t[r];if(n===void 0)continue;let o=Object.getPrototypeOf(n);if(n instanceof ig||o.ngMetadataName==="Optional")e.optional=!0;else if(n instanceof sg||o.ngMetadataName==="SkipSelf")e.skipSelf=!0;else if(n instanceof NC||o.ngMetadataName==="Self")e.self=!0;else if(n instanceof OC||o.ngMetadataName==="Host")e.host=!0;else if(n instanceof RC)e.token=n.token;else if(n instanceof G0){if(n.attributeName===void 0)throw new R(204,!1);e.attribute=n.attributeName}else e.token=n}else t===void 0||Array.isArray(t)&&t.length===0?e.token=null:e.token=t;return e}function Y0(t,e){let r=null,n=null;t.hasOwnProperty(Ss)||Object.defineProperty(t,Ss,{get:()=>(r===null&&(r=nu({usage:0,kind:"injectable",type:t}).compileInjectable(op,`ng:///${t.name}/\u0275prov.js`,J0(t,e))),r)}),t.hasOwnProperty(Dr)||Object.defineProperty(t,Dr,{get:()=>{if(n===null){let o=nu({usage:0,kind:"injectable",type:t});n=o.compileFactory(op,`ng:///${t.name}/\u0275fac.js`,{name:t.name,type:t,typeArgumentCount:0,deps:q0(t),target:o.FactoryTarget.Injectable})}return n},configurable:!0})}var Q0=J({provide:String,useValue:J});function pp(t){return t.useClass!==void 0}function K0(t){return Q0 in t}function gp(t){return t.useFactory!==void 0}function X0(t){return t.useExisting!==void 0}function J0(t,e){let r=e||{providedIn:null},n={name:t.name,type:t,typeArgumentCount:0,providedIn:r.providedIn};return(pp(r)||gp(r))&&r.deps!==void 0&&(n.deps=Yg(r.deps)),pp(r)?n.useClass=r.useClass:K0(r)?n.useValue=r.useValue:gp(r)?n.useFactory=r.useFactory:X0(r)&&(n.useExisting=r.useExisting),n}var yV=fC("Injectable",void 0,void 0,void 0,(t,e)=>Y0(t,e));function mp(t,e=null,r=null,n){let o=Qg(t,e,r,n);return o.resolveInjectorInitializers(),o}function Qg(t,e=null,r=null,n,o=new Set){let i=[r||Ke,UC(t)];return n=n||(typeof t=="object"?void 0:ke(t)),new Co(i,e||na(),n||null,o)}var wn=class wn{static create(e,r){var n;if(Array.isArray(e))return mp({name:""},r,e,"");{let o=(n=e.name)!=null?n:"";return mp({name:o},e.parent,e.providers,o)}}};u(wn,"THROW_IF_NOT_FOUND",Mn),u(wn,"NULL",new As),u(wn,"\u0275prov",A({token:wn,providedIn:"any",factory:()=>k(cg)})),u(wn,"__NG_ELEMENT_ID__",-1);var te=wn;var eb=new N("");eb.__NG_ELEMENT_ID__=t=>{let e=xe();if(e===null)throw new R(204,!1);if(e.type&2)return e.value;if(t&z.Optional)return null;throw new R(204,!1)};var Kg=!1,Ro=(()=>{class t{}return u(t,"__NG_ELEMENT_ID__",tb),u(t,"__NG_ENV_ID__",r=>r),t})(),du=class extends Ro{_lView;constructor(e){super(),this._lView=e}onDestroy(e){return Sg(this._lView,e),()=>f0(this._lView,e)}};function tb(){return new du(Y())}var Sr=class{},Xg=new N("",{providedIn:"root",factory:()=>!1});var Jg=new N(""),em=new N(""),Ut=(()=>{let e=class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new pe(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}};u(e,"\u0275prov",A({token:e,providedIn:"root",factory:()=>new e}));let t=e;return t})();var fu=class extends ee{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(e=!1){var r,n;super(),this.__isAsync=e,gg()&&(this.destroyRef=(r=I(Ro,{optional:!0}))!=null?r:void 0,this.pendingTasks=(n=I(Ut,{optional:!0}))!=null?n:void 0)}emit(e){let r=Z(null);try{super.next(e)}finally{Z(r)}}subscribe(e,r,n){var c,l,d;let o=e,i=r||(()=>null),s=n;if(e&&typeof e=="object"){let h=e;o=(c=h.next)==null?void 0:c.bind(h),i=(l=h.error)==null?void 0:l.bind(h),s=(d=h.complete)==null?void 0:d.bind(h)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return e instanceof ue&&e.add(a),a}wrapInTimeout(e){return r=>{var o;let n=(o=this.pendingTasks)==null?void 0:o.add();setTimeout(()=>{var i;e(r),n!==void 0&&((i=this.pendingTasks)==null||i.remove(n))})}}},se=fu;function js(...t){}function tm(t){let e,r;function n(){t=js;try{r!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(r),e!==void 0&&clearTimeout(e)}catch{}}return e=setTimeout(()=>{t(),n()}),typeof requestAnimationFrame=="function"&&(r=requestAnimationFrame(()=>{t(),n()})),()=>n()}function vp(t){return queueMicrotask(()=>t()),()=>{t=js}}var dd="isAngularZone",Ls=dd+"_ID",nb=0,m=class t{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new se(!1);onMicrotaskEmpty=new se(!1);onStable=new se(!1);onError=new se(!1);constructor(e){let{enableLongStackTrace:r=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Kg}=e;if(typeof Zone>"u")throw new R(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),r&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&n,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,ib(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(dd)===!0}static assertInAngularZone(){if(!t.isInAngularZone())throw new R(909,!1)}static assertNotInAngularZone(){if(t.isInAngularZone())throw new R(909,!1)}run(e,r,n){return this._inner.run(e,r,n)}runTask(e,r,n,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,e,rb,js,js);try{return i.runTask(s,r,n)}finally{i.cancelTask(s)}}runGuarded(e,r,n){return this._inner.runGuarded(e,r,n)}runOutsideAngular(e){return this._outer.run(e)}},rb={};function fd(t){if(t._nesting==0&&!t.hasPendingMicrotasks&&!t.isStable)try{t._nesting++,t.onMicrotaskEmpty.emit(null)}finally{if(t._nesting--,!t.hasPendingMicrotasks)try{t.runOutsideAngular(()=>t.onStable.emit(null))}finally{t.isStable=!0}}}function ob(t){if(t.isCheckStableRunning||t.callbackScheduled)return;t.callbackScheduled=!0;function e(){tm(()=>{t.callbackScheduled=!1,hu(t),t.isCheckStableRunning=!0,fd(t),t.isCheckStableRunning=!1})}t.scheduleInRootZone?Zone.root.run(()=>{e()}):t._outer.run(()=>{e()}),hu(t)}function ib(t){let e=()=>{ob(t)},r=nb++;t._inner=t._inner.fork({name:"angular",properties:{[dd]:!0,[Ls]:r,[Ls+r]:!0},onInvokeTask:(n,o,i,s,a,c)=>{if(sb(c))return n.invokeTask(i,s,a,c);try{return yp(t),n.invokeTask(i,s,a,c)}finally{(t.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||t.shouldCoalesceRunChangeDetection)&&e(),Dp(t)}},onInvoke:(n,o,i,s,a,c,l)=>{try{return yp(t),n.invoke(i,s,a,c,l)}finally{t.shouldCoalesceRunChangeDetection&&!t.callbackScheduled&&!ab(c)&&e(),Dp(t)}},onHasTask:(n,o,i,s)=>{n.hasTask(i,s),o===i&&(s.change=="microTask"?(t._hasPendingMicrotasks=s.microTask,hu(t),fd(t)):s.change=="macroTask"&&(t.hasPendingMacrotasks=s.macroTask))},onHandleError:(n,o,i,s)=>(n.handleError(i,s),t.runOutsideAngular(()=>t.onError.emit(s)),!1)})}function hu(t){t._hasPendingMicrotasks||(t.shouldCoalesceEventChangeDetection||t.shouldCoalesceRunChangeDetection)&&t.callbackScheduled===!0?t.hasPendingMicrotasks=!0:t.hasPendingMicrotasks=!1}function yp(t){t._nesting++,t.isStable&&(t.isStable=!1,t.onUnstable.emit(null))}function Dp(t){t._nesting--,fd(t)}var Vs=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new se;onMicrotaskEmpty=new se;onStable=new se;onError=new se;run(e,r,n){return e.apply(r,n)}runGuarded(e,r,n){return e.apply(r,n)}runOutsideAngular(e){return e()}runTask(e,r,n,o){return e.apply(r,n)}};function sb(t){return nm(t,"__ignore_ng_zone__")}function ab(t){return nm(t,"__scheduler_tick__")}function nm(t,e){var r,n;return!Array.isArray(t)||t.length!==1?!1:((n=(r=t[0])==null?void 0:r.data)==null?void 0:n[e])===!0}function cb(t="zone.js",e){return t==="noop"?new Vs:t==="zone.js"?new m(e):t}var It=class{_console=console;handleError(e){this._console.error("ERROR",e)}},lb=new N("",{providedIn:"root",factory:()=>{let t=I(m),e=I(It);return r=>t.runOutsideAngular(()=>e.handleError(r))}});function Ip(t,e){return Qp(t,e)}function ub(t){return Qp(Yp,t)}var rm=(Ip.required=ub,Ip);function db(){return kr(xe(),Y())}function kr(t,e){return new D(wt(t,e))}var D=(()=>{class t{nativeElement;constructor(r){this.nativeElement=r}}return u(t,"__NG_ELEMENT_ID__",db),t})();function fb(t){return t instanceof D?t.nativeElement:t}function hb(t){return typeof t=="function"&&t[Ye]!==void 0}function No(t,e){let r=Rl(t,e==null?void 0:e.equal),n=r[Ye];return r.set=o=>fo(n,o),r.update=o=>Nl(n,o),r.asReadonly=pb.bind(r),r}function pb(){let t=this[Ye];if(t.readonlyFn===void 0){let e=()=>this();e[Ye]=t,t.readonlyFn=e}return t.readonlyFn}function om(t){return hb(t)&&typeof t.set=="function"}function gb(){return this._results[Symbol.iterator]()}var pu=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){var e;return(e=this._changes)!=null?e:this._changes=new ee}constructor(e=!1){this._emitDistinctChangesOnly=e}get(e){return this._results[e]}map(e){return this._results.map(e)}filter(e){return this._results.filter(e)}find(e){return this._results.find(e)}reduce(e,r){return this._results.reduce(e,r)}forEach(e){this._results.forEach(e)}some(e){return this._results.some(e)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(e,r){this.dirty=!1;let n=FC(e);(this._changesDetected=!kC(this._results,n,r))&&(this._results=n,this.length=n.length,this.last=n[this.length-1],this.first=n[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(e){this._onDirty=e}setDirty(){var e;this.dirty=!0,(e=this._onDirty)==null||e.call(this)}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=gb};function im(t){return(t.flags&128)===128}var sm=function(t){return t[t.OnPush=0]="OnPush",t[t.Default=1]="Default",t}(sm||{}),am=new Map,mb=0;function vb(){return mb++}function yb(t){am.set(t[ra],t)}function gu(t){am.delete(t[ra])}var Cp="__ngContext__";function Fr(t,e){tn(e)?(t[Cp]=e[ra],yb(e)):t[Cp]=e}function cm(t){return um(t[Eo])}function lm(t){return um(t[st])}function um(t){for(;t!==null&&!Bt(t);)t=t[st];return t}var mu;function dm(t){mu=t}function fm(){if(mu!==void 0)return mu;if(typeof document<"u")return document;throw new R(210,!1)}var hd=new N("",{providedIn:"root",factory:()=>Db}),Db="ng",pd=new N(""),rn=new N("",{providedIn:"platform",factory:()=>"unknown"});var gd=new N("",{providedIn:"root",factory:()=>{var t,e;return((e=(t=fm().body)==null?void 0:t.querySelector("[ngCspNonce]"))==null?void 0:e.getAttribute("ngCspNonce"))||null}});var Ib="h",Cb="b";var hm=!1,bb=new N("",{providedIn:"root",factory:()=>hm});var md=function(t){return t[t.CHANGE_DETECTION=0]="CHANGE_DETECTION",t[t.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",t}(md||{}),Pr=new N(""),bp=new Set;function Oo(t){var e;bp.has(t)||(bp.add(t),(e=performance==null?void 0:performance.mark)==null||e.call(performance,"mark_feature_usage",{detail:{feature:t}}))}var pm=(()=>{class t{view;node;constructor(r,n){this.view=r,this.node=n}}return u(t,"__NG_ELEMENT_ID__",Eb),t})();function Eb(){return new pm(Y(),xe())}var gr=function(t){return t[t.EarlyRead=0]="EarlyRead",t[t.Write=1]="Write",t[t.MixedReadWrite=2]="MixedReadWrite",t[t.Read=3]="Read",t}(gr||{}),gm=(()=>{let e=class e{impl=null;execute(){var n;(n=this.impl)==null||n.execute()}};u(e,"\u0275prov",A({token:e,providedIn:"root",factory:()=>new e}));let t=e;return t})(),wb=[gr.EarlyRead,gr.Write,gr.MixedReadWrite,gr.Read],Mb=(()=>{let e=class e{ngZone=I(m);scheduler=I(Sr);errorHandler=I(It,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){I(Pr,{optional:!0})}execute(){var o;let n=this.sequences.size>0;n&&ie(16),this.executing=!0;for(let i of wb)for(let s of this.sequences)if(!(s.erroredOrDestroyed||!s.hooks[i]))try{s.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let a=s.hooks[i];return a(s.pipelinedValue)},s.snapshot))}catch(a){s.erroredOrDestroyed=!0,(o=this.errorHandler)==null||o.handleError(a)}this.executing=!1;for(let i of this.sequences)i.afterRun(),i.once&&(this.sequences.delete(i),i.destroy());for(let i of this.deferredRegistrations)this.sequences.add(i);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&ie(17)}register(n){var i;let{view:o}=n;o!==void 0?(((i=o[en])!=null?i:o[en]=[]).push(n),Ao(o),o[V]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,o){return o?o.run(md.AFTER_NEXT_RENDER,n):n()}};u(e,"\u0275prov",A({token:e,providedIn:"root",factory:()=>new e}));let t=e;return t})(),vu=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(e,r,n,o,i,s=null){this.impl=e,this.hooks=r,this.view=n,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i==null?void 0:i.onDestroy(()=>this.destroy())}afterRun(){var e;this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,(e=this.snapshot)==null||e.dispose(),this.snapshot=null}destroy(){var r,n;this.impl.unregister(this),(r=this.unregisterOnDestroy)==null||r.call(this);let e=(n=this.view)==null?void 0:n[en];e&&(this.view[en]=e.filter(o=>o!==this))}};function vd(t,e){var n;!(e!=null&&e.injector)&&KC(vd);let r=(n=e==null?void 0:e.injector)!=null?n:I(te);return Oo("NgAfterNextRender"),Tb(t,r,e,!0)}function Sb(t,e){if(t instanceof Function){let r=[void 0,void 0,void 0,void 0];return r[e]=t,r}else return[t.earlyRead,t.write,t.mixedReadWrite,t.read]}function Tb(t,e,r,n){var d,h;let o=e.get(gm);(d=o.impl)!=null||(o.impl=e.get(Mb));let i=e.get(Pr,null,{optional:!0}),s=(h=r==null?void 0:r.phase)!=null?h:gr.MixedReadWrite,a=(r==null?void 0:r.manualCleanup)!==!0?e.get(Ro):null,c=e.get(pm,null,{optional:!0}),l=new vu(o.impl,Sb(t,s),c==null?void 0:c.view,n,a,i==null?void 0:i.snapshot(null));return o.impl.register(l),l}var _b=()=>null;function mm(t,e,r=!1){return _b(t,e,r)}function vm(t,e){let r=t.contentQueries;if(r!==null){let n=Z(null);try{for(let o=0;o<r.length;o+=2){let i=r[o],s=r[o+1];if(s!==-1){let a=t.data[s];sd(i),a.contentQueries(2,e[s],s)}}}finally{Z(n)}}}function yu(t,e,r){sd(0);let n=Z(null);try{e(t,r)}finally{Z(n)}}function yd(t,e,r){if(Ku(e)){let n=Z(null);try{let o=e.directiveStart,i=e.directiveEnd;for(let s=o;s<i;s++){let a=t.data[s];if(a.contentQueries){let c=r[s];a.contentQueries(1,c,s)}}}finally{Z(n)}}}var Ct=function(t){return t[t.Emulated=0]="Emulated",t[t.None=2]="None",t[t.ShadowDom=3]="ShadowDom",t}(Ct||{}),gs;function xb(){if(gs===void 0&&(gs=null,Fe.trustedTypes))try{gs=Fe.trustedTypes.createPolicy("angular",{createHTML:t=>t,createScript:t=>t,createScriptURL:t=>t})}catch{}return gs}function ua(t){var e;return((e=xb())==null?void 0:e.createHTML(t))||t}var ms;function ym(){if(ms===void 0&&(ms=null,Fe.trustedTypes))try{ms=Fe.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:t=>t,createScript:t=>t,createScriptURL:t=>t})}catch{}return ms}function Ep(t){var e;return((e=ym())==null?void 0:e.createHTML(t))||t}function wp(t){var e;return((e=ym())==null?void 0:e.createScriptURL(t))||t}var Bs=class{changingThisBreaksApplicationSecurity;constructor(e){this.changingThisBreaksApplicationSecurity=e}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${qp})`}};function ko(t){return t instanceof Bs?t.changingThisBreaksApplicationSecurity:t}function Dd(t,e){let r=Ab(t);if(r!=null&&r!==e){if(r==="ResourceURL"&&e==="URL")return!0;throw new Error(`Required a safe ${e}, got a ${r} (see ${qp})`)}return r===e}function Ab(t){return t instanceof Bs&&t.getTypeName()||null}function Rb(t){let e=new Iu(t);return Nb()?new Du(e):e}var Du=class{inertDocumentHelper;constructor(e){this.inertDocumentHelper=e}getInertBodyElement(e){var r;e="<body><remove></remove>"+e;try{let n=new window.DOMParser().parseFromString(ua(e),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(e):((r=n.firstChild)==null||r.remove(),n)}catch{return null}}},Iu=class{defaultDoc;inertDocument;constructor(e){this.defaultDoc=e,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(e){let r=this.inertDocument.createElement("template");return r.innerHTML=ua(e),r}};function Nb(){try{return!!new window.DOMParser().parseFromString(ua(""),"text/html")}catch{return!1}}var Ob=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Dm(t){return t=String(t),t.match(Ob)?t:"unsafe:"+t}function Ht(t){let e={};for(let r of t.split(","))e[r]=!0;return e}function Fo(...t){let e={};for(let r of t)for(let n in r)r.hasOwnProperty(n)&&(e[n]=!0);return e}var Im=Ht("area,br,col,hr,img,wbr"),Cm=Ht("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),bm=Ht("rp,rt"),kb=Fo(bm,Cm),Fb=Fo(Cm,Ht("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),Pb=Fo(bm,Ht("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),Mp=Fo(Im,Fb,Pb,kb),Em=Ht("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),jb=Ht("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),Lb=Ht("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),Vb=Fo(Em,jb,Lb),Bb=Ht("script,style,template"),Cu=class{sanitizedSomething=!1;buf=[];sanitizeChildren(e){let r=e.firstChild,n=!0,o=[];for(;r;){if(r.nodeType===Node.ELEMENT_NODE?n=this.startElement(r):r.nodeType===Node.TEXT_NODE?this.chars(r.nodeValue):this.sanitizedSomething=!0,n&&r.firstChild){o.push(r),r=$b(r);continue}for(;r;){r.nodeType===Node.ELEMENT_NODE&&this.endElement(r);let i=Hb(r);if(i){r=i;break}r=o.pop()}}return this.buf.join("")}startElement(e){let r=Sp(e).toLowerCase();if(!Mp.hasOwnProperty(r))return this.sanitizedSomething=!0,!Bb.hasOwnProperty(r);this.buf.push("<"),this.buf.push(r);let n=e.attributes;for(let o=0;o<n.length;o++){let i=n.item(o),s=i.name,a=s.toLowerCase();if(!Vb.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;Em[a]&&(c=Dm(c)),this.buf.push(" ",s,'="',Tp(c),'"')}return this.buf.push(">"),!0}endElement(e){let r=Sp(e).toLowerCase();Mp.hasOwnProperty(r)&&!Im.hasOwnProperty(r)&&(this.buf.push("</"),this.buf.push(r),this.buf.push(">"))}chars(e){this.buf.push(Tp(e))}};function Ub(t,e){return(t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function Hb(t){let e=t.nextSibling;if(e&&t!==e.previousSibling)throw wm(e);return e}function $b(t){let e=t.firstChild;if(e&&Ub(t,e))throw wm(e);return e}function Sp(t){let e=t.nodeName;return typeof e=="string"?e:"FORM"}function wm(t){return new Error(`Failed to sanitize html because the element is clobbered: ${t.outerHTML}`)}var zb=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Gb=/([^\#-~ |!])/g;function Tp(t){return t.replace(/&/g,"&amp;").replace(zb,function(e){let r=e.charCodeAt(0),n=e.charCodeAt(1);return"&#"+((r-55296)*1024+(n-56320)+65536)+";"}).replace(Gb,function(e){return"&#"+e.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var vs;function Wb(t,e){let r=null;try{vs=vs||Rb(t);let n=e?String(e):"";r=vs.getInertBodyElement(n);let o=5,i=n;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,n=i,i=r.innerHTML,r=vs.getInertBodyElement(n)}while(n!==i);let a=new Cu().sanitizeChildren(_p(r)||r);return ua(a)}finally{if(r){let n=_p(r)||r;for(;n.firstChild;)n.firstChild.remove()}}}function _p(t){return"content"in t&&qb(t)?t.content:null}function qb(t){return t.nodeType===Node.ELEMENT_NODE&&t.nodeName==="TEMPLATE"}var da=function(t){return t[t.NONE=0]="NONE",t[t.HTML=1]="HTML",t[t.STYLE=2]="STYLE",t[t.SCRIPT=3]="SCRIPT",t[t.URL=4]="URL",t[t.RESOURCE_URL=5]="RESOURCE_URL",t}(da||{});function DV(t){let e=Id();return e?Ep(e.sanitize(da.HTML,t)||""):Dd(t,"HTML")?Ep(ko(t)):Wb(fm(),_n(t))}function Zb(t){let e=Id();return e?e.sanitize(da.URL,t)||"":Dd(t,"URL")?ko(t):Dm(_n(t))}function Yb(t){let e=Id();if(e)return wp(e.sanitize(da.RESOURCE_URL,t)||"");if(Dd(t,"ResourceURL"))return wp(ko(t));throw new R(904,!1)}function Qb(t,e){return e==="src"&&(t==="embed"||t==="frame"||t==="iframe"||t==="media"||t==="script")||e==="href"&&(t==="base"||t==="link")?Yb:Zb}function Mm(t,e,r){return Qb(e,r)(t)}function Id(){let t=Y();return t&&t[jt].sanitizer}var Kb=/^>|^->|<!--|-->|--!>|<!-$/g,Xb=/(<|>)/g,Jb="\u200B$1\u200B";function eE(t){return t.replace(Kb,e=>e.replace(Xb,Jb))}function Sm(t){return t instanceof Function?t():t}function tE(t,e,r){let n=t.length;for(;;){let o=t.indexOf(e,r);if(o===-1)return o;if(o===0||t.charCodeAt(o-1)<=32){let i=e.length;if(o+i===n||t.charCodeAt(o+i)<=32)return o}r=o+1}}var Tm="ng-template";function nE(t,e,r,n){let o=0;if(n){for(;o<e.length&&typeof e[o]=="string";o+=2)if(e[o]==="class"&&tE(e[o+1].toLowerCase(),r,0)!==-1)return!0}else if(Cd(t))return!1;if(o=e.indexOf(1,o),o>-1){let i;for(;++o<e.length&&typeof(i=e[o])=="string";)if(i.toLowerCase()===r)return!0}return!1}function Cd(t){return t.type===4&&t.value!==Tm}function rE(t,e,r){let n=t.type===4&&!r?Tm:t.value;return e===n}function oE(t,e,r){let n=4,o=t.attrs,i=o!==null?aE(o):0,s=!1;for(let a=0;a<e.length;a++){let c=e[a];if(typeof c=="number"){if(!s&&!it(n)&&!it(c))return!1;if(s&&it(c))continue;s=!1,n=c|n&1;continue}if(!s)if(n&4){if(n=2|n&1,c!==""&&!rE(t,c,r)||c===""&&e.length===1){if(it(n))return!1;s=!0}}else if(n&8){if(o===null||!nE(t,o,c,r)){if(it(n))return!1;s=!0}}else{let l=e[++a],d=iE(c,o,Cd(t),r);if(d===-1){if(it(n))return!1;s=!0;continue}if(l!==""){let h;if(d>i?h="":h=o[d+1].toLowerCase(),n&2&&l!==h){if(it(n))return!1;s=!0}}}}return it(n)||s}function it(t){return(t&1)===0}function iE(t,e,r,n){if(e===null)return-1;let o=0;if(n||!r){let i=!1;for(;o<e.length;){let s=e[o];if(s===t)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=e[++o];for(;typeof a=="string";)a=e[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return cE(e,t)}function _m(t,e,r=!1){for(let n=0;n<e.length;n++)if(oE(t,e[n],r))return!0;return!1}function sE(t){let e=t.attrs;if(e!=null){let r=e.indexOf(5);if((r&1)===0)return e[r+1]}return null}function aE(t){for(let e=0;e<t.length;e++){let r=t[e];if(Vg(r))return e}return t.length}function cE(t,e){let r=t.indexOf(4);if(r>-1)for(r++;r<t.length;){let n=t[r];if(typeof n=="number")return-1;if(n===e)return r;r++}return-1}function lE(t,e){e:for(let r=0;r<e.length;r++){let n=e[r];if(t.length===n.length){for(let o=0;o<t.length;o++)if(t[o]!==n[o])continue e;return!0}}return!1}function xp(t,e){return t?":not("+e.trim()+")":e}function uE(t){let e=t[0],r=1,n=2,o="",i=!1;for(;r<t.length;){let s=t[r];if(typeof s=="string")if(n&2){let a=t[++r];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else n&8?o+="."+s:n&4&&(o+=" "+s);else o!==""&&!it(s)&&(e+=xp(i,o),o=""),n=s,i=i||!it(n);r++}return o!==""&&(e+=xp(i,o)),e}function dE(t){return t.map(uE).join(",")}function fE(t){let e=[],r=[],n=1,o=2;for(;n<t.length;){let i=t[n];if(typeof i=="string")o===2?i!==""&&e.push(i,t[++n]):o===8&&r.push(i);else{if(!it(o))break;o=i}n++}return r.length&&e.push(1,...r),e}var St={};function hE(t,e){return t.createText(e)}function pE(t,e,r){t.setValue(e,r)}function gE(t,e){return t.createComment(eE(e))}function xm(t,e,r){return t.createElement(e,r)}function Us(t,e,r,n,o){t.insertBefore(e,r,n,o)}function Am(t,e,r){t.appendChild(e,r)}function Ap(t,e,r,n,o){n!==null?Us(t,e,r,n,o):Am(t,e,r)}function mE(t,e,r){t.removeChild(null,e,r)}function vE(t,e,r){t.setAttribute(e,"style",r)}function yE(t,e,r){r===""?t.removeAttribute(e,"class"):t.setAttribute(e,"class",r)}function Rm(t,e,r){let{mergedAttrs:n,classes:o,styles:i}=r;n!==null&&k0(t,e,n),o!==null&&yE(t,e,o),i!==null&&vE(t,e,i)}function bd(t,e,r,n,o,i,s,a,c,l,d){let h=et+n,g=h+o,p=DE(h,g),v=typeof l=="function"?l():l;return p[W]={type:t,blueprint:p,template:r,queries:null,viewQuery:a,declTNode:e,data:p.slice().fill(null,h),bindingStartIndex:h,expandoStartIndex:g,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:v,incompleteFirstPass:!1,ssrId:d}}function DE(t,e){let r=[];for(let n=0;n<e;n++)r.push(n<t?null:St);return r}function IE(t){let e=t.tView;return e===null||e.incompleteFirstPass?t.tView=bd(1,null,t.template,t.decls,t.vars,t.directiveDefs,t.pipeDefs,t.viewQuery,t.schemas,t.consts,t.id):e}function Ed(t,e,r,n,o,i,s,a,c,l,d){let h=e.blueprint.slice();return h[Vt]=o,h[V]=n|4|128|8|64|1024,(l!==null||t&&t[V]&2048)&&(h[V]|=2048),wg(h),h[Te]=h[Nr]=t,h[Pe]=r,h[jt]=s||t&&t[jt],h[de]=a||t&&t[de],h[br]=c||t&&t[br]||null,h[Le]=i,h[ra]=vb(),h[bo]=d,h[mg]=l,h[Je]=e.type==2?t[Je]:h,h}function CE(t,e,r){let n=wt(e,t),o=IE(r),i=t[jt].rendererFactory,s=wd(t,Ed(t,o,null,Nm(r),n,e,null,i.createRenderer(n,r),null,null,null));return t[e.index]=s}function Nm(t){let e=16;return t.signals?e=4096:t.onPush&&(e=64),e}function Om(t,e,r,n){if(r===0)return-1;let o=e.length;for(let i=0;i<r;i++)e.push(n),t.blueprint.push(n),t.data.push(null);return o}function wd(t,e){return t[Eo]?t[ip][st]=e:t[Eo]=e,t[ip]=e,e}function fa(t=1){km(ge(),Y(),Vn()+t,!1)}function km(t,e,r,n){if(!n)if((e[V]&3)===3){let i=t.preOrderCheckHooks;i!==null&&bs(e,i,r)}else{let i=t.preOrderHooks;i!==null&&Es(e,i,0,r)}On(r)}var ha=function(t){return t[t.None=0]="None",t[t.SignalBased=1]="SignalBased",t[t.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",t}(ha||{});function bu(t,e,r,n){let o=Z(null);try{let[i,s,a]=t.inputs[r],c=null;(s&ha.SignalBased)!==0&&(c=e[i][Ye]),c!==null&&c.transformFn!==void 0?n=c.transformFn(n):a!==null&&(n=a.call(e,n)),t.setInput!==null?t.setInput(e,c,n,r,i):yg(e,c,i,n)}finally{Z(o)}}function Fm(t,e,r,n,o){let i=Vn(),s=n&2;try{On(-1),s&&e.length>et&&km(t,e,et,!1),ie(s?2:0,o),r(n,o)}finally{On(i),ie(s?3:1,o)}}function pa(t,e,r){TE(t,e,r),(r.flags&64)===64&&_E(t,e,r)}function Md(t,e,r=wt){let n=e.localNames;if(n!==null){let o=e.index+1;for(let i=0;i<n.length;i+=2){let s=n[i+1],a=s===-1?r(e,t):t[s];t[o++]=a}}}function bE(t,e,r,n){let i=n.get(bb,hm)||r===Ct.ShadowDom,s=t.selectRootElement(e,i);return EE(s),s}function EE(t){wE(t)}var wE=()=>null;function ME(t){return t==="class"?"className":t==="for"?"htmlFor":t==="formaction"?"formAction":t==="innerHtml"?"innerHTML":t==="readonly"?"readOnly":t==="tabindex"?"tabIndex":t}function Sd(t,e,r,n,o,i,s,a){if(!a&&_d(e,t,r,n,o)){Or(e)&&SE(r,e.index);return}if(e.type&3){let c=wt(e,r);n=ME(n),o=s!=null?s(o,e.value||"",n):o,i.setProperty(c,n,o)}else e.type&12}function SE(t,e){let r=Dt(e,t);r[V]&16||(r[V]|=64)}function TE(t,e,r){let n=r.directiveStart,o=r.directiveEnd;Or(r)&&CE(e,r,t.data[n+r.componentOffset]),t.firstCreatePass||Ps(r,e);let i=r.initialInputs;for(let s=n;s<o;s++){let a=t.data[s],c=wo(e,t,s,r);if(Fr(c,e),i!==null&&NE(e,s-n,c,a,r,i),vt(a)){let l=Dt(r.index,e);l[Pe]=wo(e,t,s,r)}}}function _E(t,e,r){let n=r.directiveStart,o=r.directiveEnd,i=r.index,s=M0();try{On(i);for(let a=n;a<o;a++){let c=t.data[a],l=e[a];au(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&xE(c,l)}}finally{On(-1),au(s)}}function xE(t,e){t.hostBindings!==null&&t.hostBindings(1,e)}function Td(t,e){let r=t.directiveRegistry,n=null;if(r)for(let o=0;o<r.length;o++){let i=r[o];_m(e,i.selectors,!1)&&(n!=null||(n=[]),vt(i)?n.unshift(i):n.push(i))}return n}function AE(t,e,r,n,o,i){let s=wt(t,e);RE(e[de],s,i,t.value,r,n,o)}function RE(t,e,r,n,o,i,s){if(i==null)t.removeAttribute(e,o,r);else{let a=s==null?_n(i):s(i,n||"",o);t.setAttribute(e,o,a,r)}}function NE(t,e,r,n,o,i){let s=i[e];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];bu(n,r,c,l)}}function OE(t,e){let r=t[br],n=r?r.get(It,null):null;n&&n.handleError(e)}function _d(t,e,r,n,o){var c,l;let i=(c=t.inputs)==null?void 0:c[n],s=(l=t.hostDirectiveInputs)==null?void 0:l[n],a=!1;if(s)for(let d=0;d<s.length;d+=2){let h=s[d],g=s[d+1],p=e.data[h];bu(p,r[h],g,o),a=!0}if(i)for(let d of i){let h=r[d],g=e.data[d];bu(g,h,n,o),a=!0}return a}function kE(t,e){let r=Dt(e,t),n=r[W];FE(n,r);let o=r[Vt];o!==null&&r[bo]===null&&(r[bo]=mm(o,r[br])),ie(18),xd(n,r,r[Pe]),ie(19,r[Pe])}function FE(t,e){for(let r=e.length;r<t.blueprint.length;r++)e.push(t.blueprint[r])}function xd(t,e,r){var n;ad(e);try{let o=t.viewQuery;o!==null&&yu(1,o,r);let i=t.template;i!==null&&Fm(t,e,i,1,r),t.firstCreatePass&&(t.firstCreatePass=!1),(n=e[at])==null||n.finishViewCreation(t),t.staticContentQueries&&vm(t,e),t.staticViewQueries&&yu(2,t.viewQuery,r);let s=t.components;s!==null&&PE(e,s)}catch(o){throw t.firstCreatePass&&(t.incompleteFirstPass=!0,t.firstCreatePass=!1),o}finally{e[V]&=-5,cd()}}function PE(t,e){for(let r=0;r<e.length;r++)kE(t,e[r])}function Pm(t,e,r,n){var i,s,a;let o=Z(null);try{let c=e.tView,d=t[V]&4096?4096:16,h=Ed(t,c,r,d,null,e,null,null,(i=n==null?void 0:n.injector)!=null?i:null,(s=n==null?void 0:n.embeddedViewInjector)!=null?s:null,(a=n==null?void 0:n.dehydratedView)!=null?a:null),g=t[e.index];h[An]=g;let p=t[at];return p!==null&&(h[at]=p.createEmbeddedView(c)),xd(c,h,r),h}finally{Z(o)}}function Eu(t,e){return!e||e.firstChild===null||im(t)}var jE;function Ad(t,e){return jE(t,e)}var Lt=function(t){return t[t.Important=1]="Important",t[t.DashCase=2]="DashCase",t}(Lt||{});function Rd(t){return(t.flags&32)===32}function mr(t,e,r,n,o){if(n!=null){let i,s=!1;Bt(n)?i=n:tn(n)&&(s=!0,n=n[Vt]);let a=yt(n);t===0&&r!==null?o==null?Am(e,r,a):Us(e,r,a,o||null,!0):t===1&&r!==null?Us(e,r,a,o||null,!0):t===2?mE(e,a,s):t===3&&e.destroyNode(a),i!=null&&qE(e,t,i,r,o)}}function LE(t,e){jm(t,e),e[Vt]=null,e[Le]=null}function VE(t,e,r,n,o,i){n[Vt]=o,n[Le]=e,ma(t,n,r,1,o,i)}function jm(t,e){var r;(r=e[jt].changeDetectionScheduler)==null||r.notify(9),ma(t,e,e[de],2,null,null)}function BE(t){let e=t[Eo];if(!e)return Ul(t[W],t);for(;e;){let r=null;if(tn(e))r=e[Eo];else{let n=e[He];n&&(r=n)}if(!r){for(;e&&!e[st]&&e!==t;)tn(e)&&Ul(e[W],e),e=e[Te];e===null&&(e=t),tn(e)&&Ul(e[W],e),r=e&&e[st]}e=r}}function Nd(t,e){let r=t[Er],n=r.indexOf(e);r.splice(n,1)}function Lm(t,e){if(xo(e))return;let r=e[de];r.destroyNode&&ma(t,e,r,3,null,null),BE(e)}function Ul(t,e){if(xo(e))return;let r=Z(null);try{e[V]&=-129,e[V]|=256,e[Xe]&&_l(e[Xe]),HE(t,e),UE(t,e),e[W].type===1&&e[de].destroy();let n=e[An];if(n!==null&&Bt(e[Te])){n!==e[Te]&&Nd(n,e);let o=e[at];o!==null&&o.detachView(t)}gu(e)}finally{Z(r)}}function UE(t,e){let r=t.cleanup,n=e[vr];if(r!==null)for(let s=0;s<r.length-1;s+=2)if(typeof r[s]=="string"){let a=r[s+3];a>=0?n[a]():n[-a].unsubscribe(),s+=2}else{let a=n[r[s+1]];r[s].call(a)}n!==null&&(e[vr]=null);let o=e[Jt];if(o!==null){e[Jt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=e[Rs];if(i!==null){e[Rs]=null;for(let s of i)s.destroy()}}function HE(t,e){let r;if(t!=null&&(r=t.destroyHooks)!=null)for(let n=0;n<r.length;n+=2){let o=e[r[n]];if(!(o instanceof kn)){let i=r[n+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];ie(4,a,c);try{c.call(a)}finally{ie(5,a,c)}}else{ie(4,o,i);try{i.call(o)}finally{ie(5,o,i)}}}}}function Vm(t,e,r){return $E(t,e.parent,r)}function $E(t,e,r){let n=e;for(;n!==null&&n.type&168;)e=n,n=e.parent;if(n===null)return r[Vt];if(Or(n)){let{encapsulation:o}=t.data[n.directiveStart+n.componentOffset];if(o===Ct.None||o===Ct.Emulated)return null}return wt(n,r)}function Bm(t,e,r){return GE(t,e,r)}function zE(t,e,r){return t.type&40?wt(t,r):null}var GE=zE,Rp;function ga(t,e,r,n){let o=Vm(t,n,e),i=e[de],s=n.parent||e[Le],a=Bm(s,n,e);if(o!=null)if(Array.isArray(r))for(let c=0;c<r.length;c++)Ap(i,o,r[c],a,!1);else Ap(i,o,r,a,!1);Rp!==void 0&&Rp(i,n,e,r,o)}function vo(t,e){if(e!==null){let r=e.type;if(r&3)return wt(e,t);if(r&4)return wu(-1,t[e.index]);if(r&8){let n=e.child;if(n!==null)return vo(t,n);{let o=t[e.index];return Bt(o)?wu(-1,o):yt(o)}}else{if(r&128)return vo(t,e.next);if(r&32)return Ad(e,t)()||yt(t[e.index]);{let n=Um(t,e);if(n!==null){if(Array.isArray(n))return n[0];let o=Nn(t[Je]);return vo(o,n)}else return vo(t,e.next)}}}return null}function Um(t,e){if(e!==null){let n=t[Je][Le],o=e.projection;return n.projection[o]}return null}function wu(t,e){let r=He+t+1;if(r<e.length){let n=e[r],o=n[W].firstChild;if(o!==null)return vo(n,o)}return e[Rn]}function Od(t,e,r,n,o,i,s){for(;r!=null;){if(r.type===128){r=r.next;continue}let a=n[r.index],c=r.type;if(s&&e===0&&(a&&Fr(yt(a),n),r.flags|=2),!Rd(r))if(c&8)Od(t,e,r.child,n,o,i,!1),mr(e,t,o,a,i);else if(c&32){let l=Ad(r,n),d;for(;d=l();)mr(e,t,o,d,i);mr(e,t,o,a,i)}else c&16?Hm(t,e,n,r,o,i):mr(e,t,o,a,i);r=s?r.projectionNext:r.next}}function ma(t,e,r,n,o,i){Od(r,n,t.firstChild,e,o,i,!1)}function WE(t,e,r){let n=e[de],o=Vm(t,r,e),i=r.parent||e[Le],s=Bm(i,r,e);Hm(n,0,e,r,o,s)}function Hm(t,e,r,n,o,i){let s=r[Je],c=s[Le].projection[n.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let d=c[l];mr(e,t,o,d,i)}else{let l=c,d=s[Te];im(n)&&(l.flags|=128),Od(t,e,l,d,o,i,!0)}}function qE(t,e,r,n,o){let i=r[Rn],s=yt(r);i!==s&&mr(e,t,n,i,o);for(let a=He;a<r.length;a++){let c=r[a];ma(c[W],c,t,e,n,i)}}function ZE(t,e,r,n,o){if(e)o?t.addClass(r,n):t.removeClass(r,n);else{let i=n.indexOf("-")===-1?void 0:Lt.DashCase;o==null?t.removeStyle(r,n,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Lt.Important),t.setStyle(r,n,o,i))}}function Hs(t,e,r,n,o=!1){for(;r!==null;){if(r.type===128){r=o?r.projectionNext:r.next;continue}let i=e[r.index];i!==null&&n.push(yt(i)),Bt(i)&&YE(i,n);let s=r.type;if(s&8)Hs(t,e,r.child,n);else if(s&32){let a=Ad(r,e),c;for(;c=a();)n.push(c)}else if(s&16){let a=Um(e,r);if(Array.isArray(a))n.push(...a);else{let c=Nn(e[Je]);Hs(c[W],c,a,n,!0)}}r=o?r.projectionNext:r.next}return n}function YE(t,e){for(let r=He;r<t.length;r++){let n=t[r],o=n[W].firstChild;o!==null&&Hs(n[W],n,o,e)}t[Rn]!==t[Vt]&&e.push(t[Rn])}function $m(t){if(t[en]!==null){for(let e of t[en])e.impl.addSequence(e);t[en].length=0}}var zm=[];function QE(t){var e;return(e=t[Xe])!=null?e:KE(t)}function KE(t){var r;let e=(r=zm.pop())!=null?r:Object.create(JE);return e.lView=t,e}function XE(t){t.lView[Xe]!==t&&(t.lView=null,zm.push(t))}var JE=L(b({},lo),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:t=>{Ao(t.lView)},consumerOnSignalRead(){this.lView[Xe]=this}});function ew(t){var r;let e=(r=t[Xe])!=null?r:Object.create(tw);return e.lView=t,e}var tw=L(b({},lo),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:t=>{let e=Nn(t.lView);for(;e&&!Gm(e[W]);)e=Nn(e);e&&Mg(e)},consumerOnSignalRead(){this.lView[Xe]=this}});function Gm(t){return t.type!==2}function Wm(t){if(t[Rs]===null)return;let e=!0;for(;e;){let r=!1;for(let n of t[Rs])n.dirty&&(r=!0,n.zone===null||Zone.current===n.zone?n.run():n.zone.run(()=>n.run()));e=r&&!!(t[V]&8192)}}var nw=100;function qm(t,e=!0,r=0){var s,a;let o=t[jt].rendererFactory,i=!1;i||(s=o.begin)==null||s.call(o);try{rw(t,r)}catch(c){throw e&&OE(t,c),c}finally{i||(a=o.end)==null||a.call(o)}}function rw(t,e){let r=xg();try{ap(!0),Mu(t,e);let n=0;for(;ia(t);){if(n===nw)throw new R(103,!1);n++,Mu(t,1)}}finally{ap(r)}}function ow(t,e,r,n){if(xo(e))return;let o=e[V],i=!1,s=!1;ad(e);let a=!0,c=null,l=null;i||(Gm(t)?(l=QE(e),c=ls(l)):bl()===null?(a=!1,l=ew(e),c=ls(l)):e[Xe]&&(_l(e[Xe]),e[Xe]=null));try{wg(e),b0(t.bindingStartIndex),r!==null&&Fm(t,e,r,2,n);let d=(o&3)===3;if(!i)if(d){let p=t.preOrderCheckHooks;p!==null&&bs(e,p,null)}else{let p=t.preOrderHooks;p!==null&&Es(e,p,0,null),Vl(e,0)}if(s||iw(e),Wm(e),Zm(e,0),t.contentQueries!==null&&vm(t,e),!i)if(d){let p=t.contentCheckHooks;p!==null&&bs(e,p)}else{let p=t.contentHooks;p!==null&&Es(e,p,1),Vl(e,1)}aw(t,e);let h=t.components;h!==null&&Qm(e,h,0);let g=t.viewQuery;if(g!==null&&yu(2,g,n),!i)if(d){let p=t.viewCheckHooks;p!==null&&bs(e,p)}else{let p=t.viewHooks;p!==null&&Es(e,p,2),Vl(e,2)}if(t.firstUpdatePass===!0&&(t.firstUpdatePass=!1),e[Ll]){for(let p of e[Ll])p();e[Ll]=null}i||($m(e),e[V]&=-73)}catch(d){throw i||Ao(e),d}finally{l!==null&&(Sl(l,c),a&&XE(l)),cd()}}function Zm(t,e){for(let r=cm(t);r!==null;r=lm(r))for(let n=He;n<r.length;n++){let o=r[n];Ym(o,e)}}function iw(t){for(let e=cm(t);e!==null;e=lm(e)){if(!(e[V]&2))continue;let r=e[Er];for(let n=0;n<r.length;n++){let o=r[n];Mg(o)}}}function sw(t,e,r){ie(18);let n=Dt(e,t);Ym(n,r),ie(19,n[Pe])}function Ym(t,e){Xu(t)&&Mu(t,e)}function Mu(t,e){let n=t[W],o=t[V],i=t[Xe],s=!!(e===0&&o&16);if(s||(s=!!(o&64&&e===0)),s||(s=!!(o&1024)),s||(s=!!(i!=null&&i.dirty&&Tl(i))),s||(s=!1),i&&(i.dirty=!1),t[V]&=-9217,s)ow(n,t,n.template,t[Pe]);else if(o&8192){Wm(t),Zm(t,1);let a=n.components;a!==null&&Qm(t,a,1),$m(t)}}function Qm(t,e,r){for(let n=0;n<e.length;n++)sw(t,e[n],r)}function aw(t,e){let r=t.hostBindingOpCodes;if(r!==null)try{for(let n=0;n<r.length;n++){let o=r[n];if(o<0)On(~o);else{let i=o,s=r[++n],a=r[++n];w0(s,i);let c=e[i];ie(24,c),a(2,c),ie(25,c)}}}finally{On(-1)}}function kd(t,e){var n;let r=xg()?64:1088;for((n=t[jt].changeDetectionScheduler)==null||n.notify(e);t;){t[V]|=r;let o=Nn(t);if(Os(t)&&!o)return t;t=o}return null}function Km(t,e,r,n){return[t,!0,0,e,null,n,null,r,null,null]}function Xm(t,e,r,n=!0){let o=e[W];if(cw(o,e,t,r),n){let s=wu(r,t),a=e[de],c=a.parentNode(t[Rn]);c!==null&&VE(o,t[Le],a,e,c,s)}let i=e[bo];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Su(t,e){if(t.length<=He)return;let r=He+e,n=t[r];if(n){let o=n[An];o!==null&&o!==t&&Nd(o,n),e>0&&(t[r-1][st]=n[st]);let i=xs(t,He+e);LE(n[W],n);let s=i[at];s!==null&&s.detachView(i[W]),n[Te]=null,n[st]=null,n[V]&=-129}return n}function cw(t,e,r,n){let o=He+n,i=r.length;n>0&&(r[o-1][st]=e),n<i-He?(e[st]=r[o],ag(r,He+n,e)):(r.push(e),e[st]=null),e[Te]=r;let s=e[An];s!==null&&r!==s&&Jm(s,e);let a=e[at];a!==null&&a.insertView(t),iu(e),e[V]|=128}function Jm(t,e){let r=t[Er],n=e[Te];if(tn(n))t[V]|=2;else{let o=n[Te][Je];e[Je]!==o&&(t[V]|=2)}r===null?t[Er]=[e]:r.push(e)}var Mo=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let e=this._lView,r=e[W];return Hs(r,e,r.firstChild,[])}constructor(e,r,n=!0){this._lView=e,this._cdRefInjectingView=r,this.notifyErrorHandler=n}get context(){return this._lView[Pe]}set context(e){this._lView[Pe]=e}get destroyed(){return xo(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let e=this._lView[Te];if(Bt(e)){let r=e[Ns],n=r?r.indexOf(this):-1;n>-1&&(Su(e,n),xs(r,n))}this._attachedToViewContainer=!1}Lm(this._lView[W],this._lView)}onDestroy(e){Sg(this._lView,e)}markForCheck(){kd(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[V]&=-129}reattach(){iu(this._lView),this._lView[V]|=128}detectChanges(){this._lView[V]|=1024,qm(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new R(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let e=Os(this._lView),r=this._lView[An];r!==null&&!e&&Nd(r,this._lView),jm(this._lView[W],this._lView)}attachToAppRef(e){if(this._attachedToViewContainer)throw new R(902,!1);this._appRef=e;let r=Os(this._lView),n=this._lView[An];n!==null&&!r&&Jm(n,this._lView),iu(this._lView)}};var bt=(()=>{class t{}return u(t,"__NG_ELEMENT_ID__",dw),t})(),lw=bt,uw=class extends lw{_declarationLView;_declarationTContainer;elementRef;constructor(e,r,n){super(),this._declarationLView=e,this._declarationTContainer=r,this.elementRef=n}get ssrId(){var e;return((e=this._declarationTContainer.tView)==null?void 0:e.ssrId)||null}createEmbeddedView(e,r){return this.createEmbeddedViewImpl(e,r)}createEmbeddedViewImpl(e,r,n){let o=Pm(this._declarationLView,this._declarationTContainer,e,{embeddedViewInjector:r,dehydratedView:n});return new Mo(o)}};function dw(){return Fd(xe(),Y())}function Fd(t,e){return t.type&4?new uw(e,t,kr(t,e)):null}function Po(t,e,r,n,o){let i=t.data[e];if(i===null)i=fw(t,e,r,n,o),E0()&&(i.flags|=32);else if(i.type&64){i.type=r,i.value=n,i.attrs=o;let s=D0();i.injectorIndex=s===null?-1:s.injectorIndex}return Ln(i,!0),i}function fw(t,e,r,n,o){let i=_g(),s=od(),a=s?i:i&&i.parent,c=t.data[e]=pw(t,a,r,e,n,o);return hw(t,c,i,s),c}function hw(t,e,r,n){t.firstChild===null&&(t.firstChild=e),r!==null&&(n?r.child==null&&e.parent!==null&&(r.child=e):r.next===null&&(r.next=e,e.prev=r))}function pw(t,e,r,n,o,i){let s=e?e.injectorIndex:-1,a=0;return Tg()&&(a|=128),{type:r,index:n,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:e,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var bV=new RegExp(`^(\\d+)*(${Cb}|${Ib})*(.*)`);var gw=()=>null;function Tu(t,e){return gw(t,e)}var mw=class{},ev=class{},_u=class{resolveComponentFactory(e){throw Error(`No component factory found for ${ke(e)}.`)}},Yl,va=(Yl=class{},u(Yl,"NULL",new _u),Yl),Tr=class{},on=(()=>{class t{destroyNode=null}return u(t,"__NG_ELEMENT_ID__",()=>vw()),t})();function vw(){let t=Y(),e=xe(),r=Dt(e.index,t);return(tn(r)?r:t)[de]}var yw=(()=>{let e=class e{};u(e,"\u0275prov",A({token:e,providedIn:"root",factory:()=>null}));let t=e;return t})();var Hl={},xu=class{injector;parentInjector;constructor(e,r){this.injector=e,this.parentInjector=r}get(e,r,n){n=Js(n);let o=this.injector.get(e,Hl,n);return o!==Hl||r===Hl?o:this.parentInjector.get(e,r,n)}};function Au(t,e,r){let n=r?t.styles:null,o=r?t.classes:null,i=0;if(e!==null)for(let s=0;s<e.length;s++){let a=e[s];if(typeof a=="number")i=a;else if(i==1)o=Kh(o,a);else if(i==2){let c=a,l=e[++s];n=Kh(n,c+": "+l+";")}}r?t.styles=n:t.stylesWithoutHost=n,r?t.classes=o:t.classesWithoutHost=o}function f(t,e=z.Default){let r=Y();if(r===null)return k(t,e);let n=xe();return Wg(n,r,Se(t),e)}function tv(){let t="invalid";throw new Error(t)}function Pd(t,e,r,n,o){let i=n===null?null:{"":-1},s=o(t,r);if(s!==null){let a,c=null,l=null,d=Iw(s);d===null?a=s:[a,c,l]=d,Ew(t,e,r,a,i,c,l)}i!==null&&n!==null&&Dw(r,n,i)}function Dw(t,e,r){let n=t.localNames=[];for(let o=0;o<e.length;o+=2){let i=r[e[o+1]];if(i==null)throw new R(-301,!1);n.push(e[o],i)}}function Iw(t){let e=null,r=!1;for(let s=0;s<t.length;s++){let a=t[s];if(s===0&&vt(a)&&(e=a),a.findHostDirectiveDefs!==null){r=!0;break}}if(!r)return null;let n=null,o=null,i=null;for(let s of t)s.findHostDirectiveDefs!==null&&(n!=null||(n=[]),o!=null||(o=new Map),i!=null||(i=new Map),Cw(s,n,i,o)),s===e&&(n!=null||(n=[]),n.push(s));return n!==null?(n.push(...e===null?t:t.slice(1)),[n,o,i]):null}function Cw(t,e,r,n){let o=e.length;t.findHostDirectiveDefs(t,e,n),r.set(t,[o,e.length-1])}function bw(t,e,r){var n;e.componentOffset=r,((n=t.components)!=null?n:t.components=[]).push(e.index)}function Ew(t,e,r,n,o,i,s){var g,p;let a=n.length,c=!1;for(let v=0;v<a;v++){let y=n[v];!c&&vt(y)&&(c=!0,bw(t,r,v)),lu(Ps(r,e),t,y.type)}xw(r,t.data.length,a);for(let v=0;v<a;v++){let y=n[v];y.providersResolver&&y.providersResolver(y)}let l=!1,d=!1,h=Om(t,e,a,null);a>0&&(r.directiveToIndex=new Map);for(let v=0;v<a;v++){let y=n[v];if(r.mergedAttrs=Mr(r.mergedAttrs,y.hostAttrs),Mw(t,r,e,h,y),_w(h,y,o),s!==null&&s.has(y)){let[O,X]=s.get(y);r.directiveToIndex.set(y.type,[h,O+r.directiveStart,X+r.directiveStart])}else(i===null||!i.has(y))&&r.directiveToIndex.set(y.type,h);y.contentQueries!==null&&(r.flags|=4),(y.hostBindings!==null||y.hostAttrs!==null||y.hostVars!==0)&&(r.flags|=64);let x=y.type.prototype;!l&&(x.ngOnChanges||x.ngOnInit||x.ngDoCheck)&&(((g=t.preOrderHooks)!=null?g:t.preOrderHooks=[]).push(r.index),l=!0),!d&&(x.ngOnChanges||x.ngDoCheck)&&(((p=t.preOrderCheckHooks)!=null?p:t.preOrderCheckHooks=[]).push(r.index),d=!0),h++}ww(t,r,i)}function ww(t,e,r){for(let n=e.directiveStart;n<e.directiveEnd;n++){let o=t.data[n];if(r===null||!r.has(o))Np(0,e,o,n),Np(1,e,o,n),kp(e,n,!1);else{let i=r.get(o);Op(0,e,i,n),Op(1,e,i,n),kp(e,n,!0)}}}function Np(t,e,r,n){var i,s,a;let o=t===0?r.inputs:r.outputs;for(let c in o)if(o.hasOwnProperty(c)){let l;t===0?l=(i=e.inputs)!=null?i:e.inputs={}:l=(s=e.outputs)!=null?s:e.outputs={},(a=l[c])!=null||(l[c]=[]),l[c].push(n),nv(e,c)}}function Op(t,e,r,n){var i,s,a;let o=t===0?r.inputs:r.outputs;for(let c in o)if(o.hasOwnProperty(c)){let l=o[c],d;t===0?d=(i=e.hostDirectiveInputs)!=null?i:e.hostDirectiveInputs={}:d=(s=e.hostDirectiveOutputs)!=null?s:e.hostDirectiveOutputs={},(a=d[l])!=null||(d[l]=[]),d[l].push(n,c),nv(e,l)}}function nv(t,e){e==="class"?t.flags|=8:e==="style"&&(t.flags|=16)}function kp(t,e,r){var c,l;let{attrs:n,inputs:o,hostDirectiveInputs:i}=t;if(n===null||!r&&o===null||r&&i===null||Cd(t)){(c=t.initialInputs)!=null||(t.initialInputs=[]),t.initialInputs.push(null);return}let s=null,a=0;for(;a<n.length;){let d=n[a];if(d===0){a+=4;continue}else if(d===5){a+=2;continue}else if(typeof d=="number")break;if(!r&&o.hasOwnProperty(d)){let h=o[d];for(let g of h)if(g===e){s!=null||(s=[]),s.push(d,n[a+1]);break}}else if(r&&i.hasOwnProperty(d)){let h=i[d];for(let g=0;g<h.length;g+=2)if(h[g]===e){s!=null||(s=[]),s.push(h[g+1],n[a+1]);break}}a+=2}(l=t.initialInputs)!=null||(t.initialInputs=[]),t.initialInputs.push(s)}function Mw(t,e,r,n,o){t.data[n]=o;let i=o.factory||(o.factory=Ir(o.type,!0)),s=new kn(i,vt(o),f);t.blueprint[n]=s,r[n]=s,Sw(t,e,n,Om(t,r,o.hostVars,St),o)}function Sw(t,e,r,n,o){let i=o.hostBindings;if(i){let s=t.hostBindingOpCodes;s===null&&(s=t.hostBindingOpCodes=[]);let a=~e.index;Tw(s)!=a&&s.push(a),s.push(r,n,i)}}function Tw(t){let e=t.length;for(;e>0;){let r=t[--e];if(typeof r=="number"&&r<0)return r}return 0}function _w(t,e,r){if(r){if(e.exportAs)for(let n=0;n<e.exportAs.length;n++)r[e.exportAs[n]]=t;vt(e)&&(r[""]=t)}}function xw(t,e,r){t.flags|=1,t.directiveStart=e,t.directiveEnd=e+r,t.providerIndexes=e}function rv(t,e,r,n,o,i,s,a){let c=e.consts,l=wr(c,s),d=Po(e,t,2,n,l);return i&&Pd(e,r,d,wr(c,a),o),d.mergedAttrs=Mr(d.mergedAttrs,d.attrs),d.attrs!==null&&Au(d,d.attrs,!1),d.mergedAttrs!==null&&Au(d,d.mergedAttrs,!0),e.queries!==null&&e.queries.elementStart(e,d),d}function ov(t,e){ld(t,e),Ku(e)&&t.queries.elementEnd(e)}var $s=class extends va{ngModule;constructor(e){super(),this.ngModule=e}resolveComponentFactory(e){let r=nn(e);return new Fn(r,this.ngModule)}};function Aw(t){return Object.keys(t).map(e=>{let[r,n,o]=t[e],i={propName:r,templateName:e,isSignal:(n&ha.SignalBased)!==0};return o&&(i.transform=o),i})}function Rw(t){return Object.keys(t).map(e=>({propName:t[e],templateName:e}))}function Nw(t,e,r){let n=e instanceof ae?e:e==null?void 0:e.injector;return n&&t.getStandaloneInjector!==null&&(n=t.getStandaloneInjector(n)||n),n?new xu(r,n):r}function Ow(t){let e=t.get(Tr,null);if(e===null)throw new R(407,!1);let r=t.get(yw,null),n=t.get(Sr,null);return{rendererFactory:e,sanitizer:r,changeDetectionScheduler:n}}function kw(t,e){let r=(t.selectors[0][0]||"div").toLowerCase();return xm(e,r,r==="svg"?a0:r==="math"?c0:null)}var Fn=class extends ev{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){var e;return(e=this.cachedInputs)!=null||(this.cachedInputs=Aw(this.componentDef.inputs)),this.cachedInputs}get outputs(){var e;return(e=this.cachedOutputs)!=null||(this.cachedOutputs=Rw(this.componentDef.outputs)),this.cachedOutputs}constructor(e,r){var n;super(),this.componentDef=e,this.ngModule=r,this.componentType=e.type,this.selector=dE(e.selectors),this.ngContentSelectors=(n=e.ngContentSelectors)!=null?n:[],this.isBoundToModule=!!r}create(e,r,n,o){ie(22);let i=Z(null);try{let s=this.componentDef,a=n?["ng-version","19.2.7"]:fE(this.componentDef.selectors[0]),c=bd(0,null,null,1,0,null,null,null,null,[a],null),l=Nw(s,o||this.ngModule,e),d=Ow(l),h=d.rendererFactory.createRenderer(null,s),g=n?bE(h,n,s.encapsulation,l):kw(s,h),p=Ed(null,c,null,512|Nm(s),null,null,d,h,l,null,mm(g,l,!0));p[et]=g,ad(p);let v=null;try{let y=rv(et,c,p,"#host",()=>[this.componentDef],!0,0);g&&(Rm(h,g,y),Fr(g,p)),pa(c,p,y),yd(c,y,p),ov(c,y),r!==void 0&&Fw(y,this.ngContentSelectors,r),v=Dt(y.index,p),p[Pe]=v[Pe],xd(c,p,null)}catch(y){throw v!==null&&gu(v),gu(p),y}finally{ie(23),cd()}return new Ru(this.componentType,p)}finally{Z(i)}}},Ru=class extends mw{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(e,r){super(),this._rootLView=r,this._tNode=Eg(r[W],et),this.location=kr(this._tNode,r),this.instance=Dt(this._tNode.index,r)[Pe],this.hostView=this.changeDetectorRef=new Mo(r,void 0,!1),this.componentType=e}setInput(e,r){var a;let n=this._tNode;if((a=this.previousInputValues)!=null||(this.previousInputValues=new Map),this.previousInputValues.has(e)&&Object.is(this.previousInputValues.get(e),r))return;let o=this._rootLView,i=_d(n,o[W],o,e,r);this.previousInputValues.set(e,r);let s=Dt(n.index,o);kd(s,1)}get injector(){return new Sn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(e){this.hostView.onDestroy(e)}};function Fw(t,e,r){let n=t.projection=[];for(let o=0;o<e.length;o++){let i=r[o];n.push(i!=null&&i.length?Array.from(i):null)}}var ze=(()=>{class t{}return u(t,"__NG_ELEMENT_ID__",Pw),t})();function Pw(){let t=xe();return sv(t,Y())}var jw=ze,iv=class extends jw{_lContainer;_hostTNode;_hostLView;constructor(e,r,n){super(),this._lContainer=e,this._hostTNode=r,this._hostLView=n}get element(){return kr(this._hostTNode,this._hostLView)}get injector(){return new Sn(this._hostTNode,this._hostLView)}get parentInjector(){let e=ud(this._hostTNode,this._hostLView);if(Bg(e)){let r=Fs(e,this._hostLView),n=ks(e),o=r[W].data[n+8];return new Sn(o,r)}else return new Sn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(e){let r=Fp(this._lContainer);return r!==null&&r[e]||null}get length(){return this._lContainer.length-He}createEmbeddedView(e,r,n){let o,i;typeof n=="number"?o=n:n!=null&&(o=n.index,i=n.injector);let s=Tu(this._lContainer,e.ssrId),a=e.createEmbeddedViewImpl(r||{},i,s);return this.insertImpl(a,o,Eu(this._hostTNode,s)),a}createComponent(e,r,n,o,i){var v,y,x;let s=e&&!mo(e),a;if(s)a=r;else{let O=r||{};a=O.index,n=O.injector,o=O.projectableNodes,i=O.environmentInjector||O.ngModuleRef}let c=s?e:new Fn(nn(e)),l=n||this.parentInjector;if(!i&&c.ngModule==null){let X=(s?l:this.parentInjector).get(ae,null);X&&(i=X)}let d=nn((v=c.componentType)!=null?v:{}),h=Tu(this._lContainer,(y=d==null?void 0:d.id)!=null?y:null),g=(x=h==null?void 0:h.firstChild)!=null?x:null,p=c.create(l,o,g,i);return this.insertImpl(p.hostView,a,Eu(this._hostTNode,h)),p}insert(e,r){return this.insertImpl(e,r,!0)}insertImpl(e,r,n){let o=e._lView;if(u0(o)){let a=this.indexOf(e);if(a!==-1)this.detach(a);else{let c=o[Te],l=new iv(c,c[Le],c[Te]);l.detach(l.indexOf(e))}}let i=this._adjustIndex(r),s=this._lContainer;return Xm(s,o,i,n),e.attachToViewContainerRef(),ag($l(s),i,e),e}move(e,r){return this.insert(e,r)}indexOf(e){let r=Fp(this._lContainer);return r!==null?r.indexOf(e):-1}remove(e){let r=this._adjustIndex(e,-1),n=Su(this._lContainer,r);n&&(xs($l(this._lContainer),r),Lm(n[W],n))}detach(e){let r=this._adjustIndex(e,-1),n=Su(this._lContainer,r);return n&&xs($l(this._lContainer),r)!=null?new Mo(n):null}_adjustIndex(e,r=0){return e==null?this.length+r:e}};function Fp(t){return t[Ns]}function $l(t){return t[Ns]||(t[Ns]=[])}function sv(t,e){let r,n=e[t.index];return Bt(n)?r=n:(r=Km(n,e,null,t),e[t.index]=r,wd(e,r)),Vw(r,e,t,n),new iv(r,t,e)}function Lw(t,e){let r=t[de],n=r.createComment(""),o=wt(e,t),i=r.parentNode(o);return Us(r,i,n,r.nextSibling(o),!1),n}var Vw=Hw,Bw=()=>!1;function Uw(t,e,r){return Bw(t,e,r)}function Hw(t,e,r,n){if(t[Rn])return;let o;r.type&8?o=yt(n):o=Lw(e,r),t[Rn]=o}var Nu=class t{queryList;matches=null;constructor(e){this.queryList=e}clone(){return new t(this.queryList)}setDirty(){this.queryList.setDirty()}},Ou=class t{queries;constructor(e=[]){this.queries=e}createEmbeddedView(e){let r=e.queries;if(r!==null){let n=e.contentQueries!==null?e.contentQueries[0]:r.length,o=[];for(let i=0;i<n;i++){let s=r.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new t(o)}return null}insertView(e){this.dirtyQueriesWithMatches(e)}detachView(e){this.dirtyQueriesWithMatches(e)}finishViewCreation(e){this.dirtyQueriesWithMatches(e)}dirtyQueriesWithMatches(e){for(let r=0;r<this.queries.length;r++)jd(e,r).matches!==null&&this.queries[r].setDirty()}},zs=class{flags;read;predicate;constructor(e,r,n=null){this.flags=r,this.read=n,typeof e=="string"?this.predicate=Qw(e):this.predicate=e}},ku=class t{queries;constructor(e=[]){this.queries=e}elementStart(e,r){for(let n=0;n<this.queries.length;n++)this.queries[n].elementStart(e,r)}elementEnd(e){for(let r=0;r<this.queries.length;r++)this.queries[r].elementEnd(e)}embeddedTView(e){let r=null;for(let n=0;n<this.length;n++){let o=r!==null?r.length:0,i=this.getByIndex(n).embeddedTView(e,o);i&&(i.indexInDeclarationView=n,r!==null?r.push(i):r=[i])}return r!==null?new t(r):null}template(e,r){for(let n=0;n<this.queries.length;n++)this.queries[n].template(e,r)}getByIndex(e){return this.queries[e]}get length(){return this.queries.length}track(e){this.queries.push(e)}},Fu=class t{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(e,r=-1){this.metadata=e,this._declarationNodeIndex=r}elementStart(e,r){this.isApplyingToNode(r)&&this.matchTNode(e,r)}elementEnd(e){this._declarationNodeIndex===e.index&&(this._appliesToNextNode=!1)}template(e,r){this.elementStart(e,r)}embeddedTView(e,r){return this.isApplyingToNode(e)?(this.crossesNgTemplate=!0,this.addMatch(-e.index,r),new t(this.metadata)):null}isApplyingToNode(e){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let r=this._declarationNodeIndex,n=e.parent;for(;n!==null&&n.type&8&&n.index!==r;)n=n.parent;return r===(n!==null?n.index:-1)}return this._appliesToNextNode}matchTNode(e,r){let n=this.metadata.predicate;if(Array.isArray(n))for(let o=0;o<n.length;o++){let i=n[o];this.matchTNodeWithReadOption(e,r,$w(r,i)),this.matchTNodeWithReadOption(e,r,ws(r,e,i,!1,!1))}else n===bt?r.type&4&&this.matchTNodeWithReadOption(e,r,-1):this.matchTNodeWithReadOption(e,r,ws(r,e,n,!1,!1))}matchTNodeWithReadOption(e,r,n){if(n!==null){let o=this.metadata.read;if(o!==null)if(o===D||o===ze||o===bt&&r.type&4)this.addMatch(r.index,-2);else{let i=ws(r,e,o,!1,!1);i!==null&&this.addMatch(r.index,i)}else this.addMatch(r.index,n)}}addMatch(e,r){this.matches===null?this.matches=[e,r]:this.matches.push(e,r)}};function $w(t,e){let r=t.localNames;if(r!==null){for(let n=0;n<r.length;n+=2)if(r[n]===e)return r[n+1]}return null}function zw(t,e){return t.type&11?kr(t,e):t.type&4?Fd(t,e):null}function Gw(t,e,r,n){return r===-1?zw(e,t):r===-2?Ww(t,e,n):wo(t,t[W],r,e)}function Ww(t,e,r){if(r===D)return kr(e,t);if(r===bt)return Fd(e,t);if(r===ze)return sv(e,t)}function av(t,e,r,n){let o=e[at].queries[n];if(o.matches===null){let i=t.data,s=r.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let d=i[l];a.push(Gw(e,d,s[c+1],r.metadata.read))}}o.matches=a}return o.matches}function Pu(t,e,r,n){let o=t.queries.getByIndex(r),i=o.matches;if(i!==null){let s=av(t,e,o,r);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)n.push(s[a/2]);else{let l=i[a+1],d=e[-c];for(let h=He;h<d.length;h++){let g=d[h];g[An]===g[Te]&&Pu(g[W],g,l,n)}if(d[Er]!==null){let h=d[Er];for(let g=0;g<h.length;g++){let p=h[g];Pu(p[W],p,l,n)}}}}}return n}function qw(t,e){return t[at].queries[e].queryList}function cv(t,e,r){var i;let n=new pu((r&4)===4);return h0(t,e,n,n.destroy),((i=e[at])!=null?i:e[at]=new Ou).queries.push(new Nu(n))-1}function Zw(t,e,r){let n=ge();return n.firstCreatePass&&(lv(n,new zs(t,e,r),-1),(e&2)===2&&(n.staticViewQueries=!0)),cv(n,Y(),e)}function Yw(t,e,r,n){let o=ge();if(o.firstCreatePass){let i=xe();lv(o,new zs(e,r,n),i.index),Kw(o,t),(r&2)===2&&(o.staticContentQueries=!0)}return cv(o,Y(),r)}function Qw(t){return t.split(",").map(e=>e.trim())}function lv(t,e,r){t.queries===null&&(t.queries=new ku),t.queries.track(new Fu(e,r))}function Kw(t,e){let r=t.contentQueries||(t.contentQueries=[]),n=r.length?r[r.length-1]:-1;e!==n&&r.push(t.queries.length-1,e)}function jd(t,e){return t.queries.getByIndex(e)}function Xw(t,e){let r=t[W],n=jd(r,e);return n.crossesNgTemplate?Pu(r,t,e,[]):av(r,t,n,e)}function Jw(t){let e=[],r=new Map;function n(o){let i=r.get(o);if(!i){let s=t(o);r.set(o,i=s.then(rM))}return i}return Gs.forEach((o,i)=>{var l,d;let s=[];o.templateUrl&&s.push(n(o.templateUrl).then(h=>{o.template=h}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&((l=o.styleUrls)!=null&&l.length))throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if((d=o.styleUrls)!=null&&d.length){let h=o.styles.length,g=o.styleUrls;o.styleUrls.forEach((p,v)=>{a.push(""),s.push(n(p).then(y=>{a[h+v]=y,g.splice(g.indexOf(p),1),g.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(n(o.styleUrl).then(h=>{a.push(h),o.styleUrl=void 0}));let c=Promise.all(s).then(()=>oM(i));e.push(c)}),tM(),Promise.all(e).then(()=>{})}var Gs=new Map,eM=new Set;function tM(){let t=Gs;return Gs=new Map,t}function nM(){return Gs.size===0}function rM(t){return typeof t=="string"?t:t.text()}function oM(t){eM.delete(t)}var _r=class{},Ld=class{};var Ws=class extends _r{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new $s(this);constructor(e,r,n,o=!0){super(),this.ngModuleType=e,this._parent=r;let i=ug(e);this._bootstrapComponents=Sm(i.bootstrap),this._r3Injector=Qg(e,r,[{provide:_r,useValue:this},{provide:va,useValue:this.componentFactoryResolver},...n],ke(e),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let e=this._r3Injector;!e.destroyed&&e.destroy(),this.destroyCbs.forEach(r=>r()),this.destroyCbs=null}onDestroy(e){this.destroyCbs.push(e)}},qs=class extends Ld{moduleType;constructor(e){super(),this.moduleType=e}create(e){return new Ws(this.moduleType,e,[])}};function iM(t,e,r){return new Ws(t,e,r,!1)}var ju=class extends _r{injector;componentFactoryResolver=new $s(this);instance=null;constructor(e){super();let r=new Co([...e.providers,{provide:_r,useValue:this},{provide:va,useValue:this.componentFactoryResolver}],e.parent||na(),e.debugName,new Set(["environment"]));this.injector=r,e.runEnvironmentInitializers&&r.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(e){this.injector.onDestroy(e)}};function jo(t,e,r=null){return new ju({providers:t,parent:e,debugName:r,runEnvironmentInitializers:!0}).injector}var sM=(()=>{let e=class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let o=dg(!1,n.type),i=o.length>0?jo([o],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,i)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}};u(e,"\u0275prov",A({token:e,providedIn:"environment",factory:()=>new e(k(ae))}));let t=e;return t})();function E(t){return Ar(()=>{var o;let e=uv(t),r=L(b({},e),{decls:t.decls,vars:t.vars,template:t.template,consts:t.consts||null,ngContentSelectors:t.ngContentSelectors,onPush:t.changeDetection===sm.OnPush,directiveDefs:null,pipeDefs:null,dependencies:e.standalone&&t.dependencies||null,getStandaloneInjector:e.standalone?i=>i.get(sM).getOrCreateStandaloneInjector(r):null,getExternalStyles:null,signals:(o=t.signals)!=null?o:!1,data:t.data||{},encapsulation:t.encapsulation||Ct.Emulated,styles:t.styles||Ke,_:null,schemas:t.schemas||null,tView:null,id:""});e.standalone&&Oo("NgStandalone"),dv(r);let n=t.dependencies;return r.directiveDefs=Pp(n,!1),r.pipeDefs=Pp(n,!0),r.id=dM(r),r})}function aM(t){return nn(t)||VC(t)}function cM(t){return t!==null}function Re(t){return Ar(()=>({type:t.type,bootstrap:t.bootstrap||Ke,declarations:t.declarations||Ke,imports:t.imports||Ke,exports:t.exports||Ke,transitiveCompileScopes:null,schemas:t.schemas||null,id:t.id||null}))}function lM(t,e){var n;if(t==null)return xn;let r={};for(let o in t)if(t.hasOwnProperty(o)){let i=t[o],s,a,c,l;Array.isArray(i)?(c=i[0],s=i[1],a=(n=i[2])!=null?n:s,l=i[3]||null):(s=i,a=i,c=ha.None,l=null),r[s]=[o,c,l],e[s]=a}return r}function uM(t){if(t==null)return xn;let e={};for(let r in t)t.hasOwnProperty(r)&&(e[t[r]]=r);return e}function U(t){return Ar(()=>{let e=uv(t);return dv(e),e})}function uv(t){var r;let e={};return{type:t.type,providersResolver:null,factory:null,hostBindings:t.hostBindings||null,hostVars:t.hostVars||0,hostAttrs:t.hostAttrs||null,contentQueries:t.contentQueries||null,declaredInputs:e,inputConfig:t.inputs||xn,exportAs:t.exportAs||null,standalone:(r=t.standalone)!=null?r:!0,signals:t.signals===!0,selectors:t.selectors||Ke,viewQuery:t.viewQuery||null,features:t.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:lM(t.inputs,e),outputs:uM(t.outputs),debugInfo:null}}function dv(t){var e;(e=t.features)==null||e.forEach(r=>r(t))}function Pp(t,e){if(!t)return null;let r=e?BC:aM;return()=>(typeof t=="function"?t():t).map(n=>r(n)).filter(cM)}function dM(t){let e=0,r=typeof t.consts=="function"?"":t.consts,n=[t.selectors,t.ngContentSelectors,t.hostVars,t.hostAttrs,r,t.vars,t.decls,t.encapsulation,t.standalone,t.signals,t.exportAs,JSON.stringify(t.inputs),JSON.stringify(t.outputs),Object.getOwnPropertyNames(t.type.prototype),!!t.contentQueries,!!t.viewQuery];for(let i of n.join("|"))e=Math.imul(31,e)+i.charCodeAt(0)<<0;return e+=2147483648,"c"+e}function fM(t){return Object.getPrototypeOf(t.prototype).constructor}function ne(t){let e=fM(t.type),r=!0,n=[t];for(;e;){let o;if(vt(t))o=e.\u0275cmp||e.\u0275dir;else{if(e.\u0275cmp)throw new R(903,!1);o=e.\u0275dir}if(o){if(r){n.push(o);let s=t;s.inputs=zl(t.inputs),s.declaredInputs=zl(t.declaredInputs),s.outputs=zl(t.outputs);let a=o.hostBindings;a&&vM(t,a);let c=o.viewQuery,l=o.contentQueries;if(c&&gM(t,c),l&&mM(t,l),hM(t,o),hC(t.outputs,o.outputs),vt(o)&&o.data.animation){let d=t.data;d.animation=(d.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(t),a===ne&&(r=!1)}}e=Object.getPrototypeOf(e)}pM(n)}function hM(t,e){for(let r in e.inputs){if(!e.inputs.hasOwnProperty(r)||t.inputs.hasOwnProperty(r))continue;let n=e.inputs[r];n!==void 0&&(t.inputs[r]=n,t.declaredInputs[r]=e.declaredInputs[r])}}function pM(t){let e=0,r=null;for(let n=t.length-1;n>=0;n--){let o=t[n];o.hostVars=e+=o.hostVars,o.hostAttrs=Mr(o.hostAttrs,r=Mr(r,o.hostAttrs))}}function zl(t){return t===xn?{}:t===Ke?[]:t}function gM(t,e){let r=t.viewQuery;r?t.viewQuery=(n,o)=>{e(n,o),r(n,o)}:t.viewQuery=e}function mM(t,e){let r=t.contentQueries;r?t.contentQueries=(n,o,i)=>{e(n,o,i),r(n,o,i)}:t.contentQueries=e}function vM(t,e){let r=t.hostBindings;r?t.hostBindings=(n,o)=>{e(n,o),r(n,o)}:t.hostBindings=e}function fv(t){return DM(t)?Array.isArray(t)||!(t instanceof Map)&&Symbol.iterator in t:!1}function yM(t,e){if(Array.isArray(t))for(let r=0;r<t.length;r++)e(t[r]);else{let r=t[Symbol.iterator](),n;for(;!(n=r.next()).done;)e(n.value)}}function DM(t){return t!==null&&(typeof t=="function"||typeof t=="object")}function IM(t,e,r){return t[e]=r}function CM(t,e){return t[e]}function Pn(t,e,r){let n=t[e];return Object.is(n,r)?!1:(t[e]=r,!0)}function bM(t,e,r,n){let o=Pn(t,e,r);return Pn(t,e+1,n)||o}function EM(t,e,r,n,o,i,s,a,c){let l=e.consts,d=Po(e,t,4,s||null,a||null);td()&&Pd(e,r,d,wr(l,c),Td),d.mergedAttrs=Mr(d.mergedAttrs,d.attrs),ld(e,d);let h=d.tView=bd(2,d,n,o,i,e.directiveRegistry,e.pipeRegistry,null,e.schemas,l,null);return e.queries!==null&&(e.queries.template(e,d),h.queries=e.queries.embeddedTView(d)),d}function hv(t,e,r,n,o,i,s,a,c,l){let d=r+et,h=e.firstCreatePass?EM(d,e,t,n,o,i,s,a,c):e.data[d];Ln(h,!1);let g=wM(e,t,h,r);ca()&&ga(e,t,g,h),Fr(g,t);let p=Km(g,t,g,h);return t[d]=p,wd(t,p),Uw(p,h,t),oa(h)&&pa(e,t,h),c!=null&&Md(t,h,l),h}function Lo(t,e,r,n,o,i,s,a){let c=Y(),l=ge(),d=wr(l.consts,i);return hv(c,l,t,e,r,n,o,d,s,a),Lo}var wM=MM;function MM(t,e,r,n){return la(!0),e[de].createComment("")}var pv=(()=>{let e=class e{log(n){console.log(n)}warn(n){console.warn(n)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"platform"}));let t=e;return t})();var Vd=new N(""),Vo=new N(""),ya=(()=>{let e=class e{_ngZone;registry;_isZoneStable=!0;_callbacks=[];taskTrackingZone=null;constructor(n,o,i){this._ngZone=n,this.registry=o,Do||(SM(i),i.addToWindow(o)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{m.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(o=>o.updateCb&&o.updateCb(n)?(clearTimeout(o.timeoutId),!1):!0)}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,o,i){let s=-1;o&&o>0&&(s=setTimeout(()=>{this._callbacks=this._callbacks.filter(a=>a.timeoutId!==s),n()},o)),this._callbacks.push({doneCb:n,timeoutId:s,updateCb:i})}whenStable(n,o,i){if(i&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,o,i),this._runCallbacksIfReady()}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,o,i){return[]}};u(e,"\u0275fac",function(o){return new(o||e)(k(m),k(Da),k(Vo))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),Da=(()=>{let e=class e{_applications=new Map;registerApplication(n,o){this._applications.set(n,o)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,o=!0){var i;return(i=Do==null?void 0:Do.findTestabilityInTree(this,n,o))!=null?i:null}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"platform"}));let t=e;return t})();function SM(t){Do=t}var Do,TM=(()=>{let e=class e{};u(e,"\u0275prov",A({token:e,providedIn:"root",factory:()=>new Lu}));let t=e;return t})(),Lu=class{queuedEffectCount=0;queues=new Map;schedule(e){this.enqueue(e)}remove(e){let r=e.zone,n=this.queues.get(r);n.has(e)&&(n.delete(e),this.queuedEffectCount--)}enqueue(e){let r=e.zone;this.queues.has(r)||this.queues.set(r,new Set);let n=this.queues.get(r);n.has(e)||(this.queuedEffectCount++,n.add(e))}flush(){for(;this.queuedEffectCount>0;)for(let[e,r]of this.queues)e===null?this.flushQueue(r):e.run(()=>this.flushQueue(r))}flushQueue(e){for(let r of e)e.delete(r),this.queuedEffectCount--,r.run()}};function Bn(t){return!!t&&typeof t.then=="function"}function gv(t){return!!t&&typeof t.subscribe=="function"}var Ia=new N("");function Bd(t){return ea([{provide:Ia,multi:!0,useValue:t}])}var mv=(()=>{var e;let r=class r{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((o,i)=>{this.resolve=o,this.reject=i});appInits=(e=I(Ia,{optional:!0}))!=null?e:[];injector=I(te);constructor(){}runInitializers(){if(this.initialized)return;let o=[];for(let s of this.appInits){let a=je(this.injector,s);if(Bn(a))o.push(a);else if(gv(a)){let c=new Promise((l,d)=>{a.subscribe({complete:l,error:d})});o.push(c)}}let i=()=>{this.done=!0,this.resolve()};Promise.all(o).then(()=>{i()}).catch(s=>{this.reject(s)}),o.length===0&&i(),this.initialized=!0}};u(r,"\u0275fac",function(i){return new(i||r)}),u(r,"\u0275prov",A({token:r,factory:r.\u0275fac,providedIn:"root"}));let t=r;return t})(),Ud=new N("");function _M(){Al(()=>{throw new R(600,!1)})}function xM(t){return t.isBoundToModule}var AM=10;function vv(t,e){return Array.isArray(e)?e.reduce(vv,t):b(b({},t),e)}var Et=(()=>{let e=class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=I(lb);afterRenderManager=I(gm);zonelessEnabled=I(Xg);rootEffectScheduler=I(TM);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new ee;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=I(Ut).hasPendingTasks.pipe($(n=>!n));constructor(){I(Pr,{optional:!0})}whenStable(){let n;return new Promise(o=>{n=this.isStable.subscribe({next:i=>{i&&o()}})}).finally(()=>{n.unsubscribe()})}_injector=I(ae);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,o){return this.bootstrapImpl(n,o)}bootstrapImpl(n,o,i=te.NULL){ie(10);let s=n instanceof ev;if(!this._injector.get(mv).done){let v="";throw new R(405,v)}let c;s?c=n:c=this._injector.get(va).resolveComponentFactory(n),this.componentTypes.push(c.componentType);let l=xM(c)?void 0:this._injector.get(_r),d=o||c.selector,h=c.create(i,[],d,l),g=h.location.nativeElement,p=h.injector.get(Vd,null);return p==null||p.registerApplication(g),h.onDestroy(()=>{this.detachView(h.hostView),Ms(this.components,h),p==null||p.unregisterApplication(g)}),this._loadComponent(h),ie(11,h),h}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){ie(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(md.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{var o;if(this._runningTick)throw new R(101,!1);let n=Z(null);try{this._runningTick=!0,this.synchronize()}catch(i){this.internalErrorHandler(i)}finally{this._runningTick=!1,(o=this.tracingSnapshot)==null||o.dispose(),this.tracingSnapshot=null,Z(n),this.afterTick.next(),ie(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Tr,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<AM;)ie(14),this.synchronizeOnce(),ie(15)}synchronizeOnce(){var n,o,i,s;if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let a=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:c,notifyErrorHandler:l}of this.allViews)RM(c,l,a,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else(o=(n=this._rendererFactory)==null?void 0:n.begin)==null||o.call(n),(s=(i=this._rendererFactory)==null?void 0:i.end)==null||s.call(i);this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>ia(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let o=n;this._views.push(o),o.attachToAppRef(this)}detachView(n){let o=n;Ms(this._views,o),o.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(Ud,[]).forEach(i=>i(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Ms(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new R(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();function Ms(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}function RM(t,e,r,n){if(!r&&!ia(t))return;qm(t,e,r&&!n?0:1)}function ct(t,e,r,n){let o=Y(),i=sa();if(Pn(o,i,e)){let s=ge(),a=aa();AE(a,o,t,e,r,n)}return ct}function yv(t,e,r,n){return Pn(t,sa(),r)?e+_n(r)+n:St}function NM(t,e,r,n,o,i){let s=C0(),a=bM(t,s,r,o);return Ag(2),a?e+_n(r)+n+_n(o)+i:St}function ys(t,e){return t<<17|e<<2}function jn(t){return t>>17&32767}function OM(t){return(t&2)==2}function kM(t,e){return t&131071|e<<17}function Vu(t){return t|2}function xr(t){return(t&131068)>>2}function Gl(t,e){return t&-131069|e<<2}function FM(t){return(t&1)===1}function Bu(t){return t|1}function PM(t,e,r,n,o,i){let s=i?e.classBindings:e.styleBindings,a=jn(s),c=xr(s);t[n]=r;let l=!1,d;if(Array.isArray(r)){let h=r;d=h[1],(d===null||_o(h,d)>0)&&(l=!0)}else d=r;if(o)if(c!==0){let g=jn(t[a+1]);t[n+1]=ys(g,a),g!==0&&(t[g+1]=Gl(t[g+1],n)),t[a+1]=kM(t[a+1],n)}else t[n+1]=ys(a,0),a!==0&&(t[a+1]=Gl(t[a+1],n)),a=n;else t[n+1]=ys(c,0),a===0?a=n:t[c+1]=Gl(t[c+1],n),c=n;l&&(t[n+1]=Vu(t[n+1])),jp(t,d,n,!0),jp(t,d,n,!1),jM(e,d,t,n,i),s=ys(a,c),i?e.classBindings=s:e.styleBindings=s}function jM(t,e,r,n,o){let i=o?t.residualClasses:t.residualStyles;i!=null&&typeof e=="string"&&_o(i,e)>=0&&(r[n+1]=Bu(r[n+1]))}function jp(t,e,r,n){let o=t[r+1],i=e===null,s=n?jn(o):xr(o),a=!1;for(;s!==0&&(a===!1||i);){let c=t[s],l=t[s+1];LM(c,e)&&(a=!0,t[s+1]=n?Bu(l):Vu(l)),s=n?jn(l):xr(l)}a&&(t[r+1]=n?Vu(o):Bu(o))}function LM(t,e){return t===null||e==null||(Array.isArray(t)?t[1]:t)===e?!0:Array.isArray(t)&&typeof e=="string"?_o(t,e)>=0:!1}function sn(t,e,r){let n=Y(),o=sa();if(Pn(n,o,e)){let i=ge(),s=aa();Sd(i,s,n,t,e,n[de],r,!1)}return sn}function Lp(t,e,r,n,o){_d(e,t,r,o?"class":"style",n)}function Ca(t,e){return VM(t,e,null,!0),Ca}function VM(t,e,r,n){let o=Y(),i=ge(),s=Ag(2);if(i.firstUpdatePass&&UM(i,t,s,n),e!==St&&Pn(o,s,e)){let a=i.data[Vn()];WM(i,a,o,o[de],t,o[s+1]=qM(e,r),n,s)}}function BM(t,e){return e>=t.expandoStartIndex}function UM(t,e,r,n){let o=t.data;if(o[r+1]===null){let i=o[Vn()],s=BM(t,r);ZM(i,n)&&e===null&&!s&&(e=!1),e=HM(o,i,e,n),PM(o,i,e,r,s,n)}}function HM(t,e,r,n){let o=S0(t),i=n?e.residualClasses:e.residualStyles;if(o===null)(n?e.classBindings:e.styleBindings)===0&&(r=Wl(null,t,e,r,n),r=So(r,e.attrs,n),i=null);else{let s=e.directiveStylingLast;if(s===-1||t[s]!==o)if(r=Wl(o,t,e,r,n),i===null){let c=$M(t,e,n);c!==void 0&&Array.isArray(c)&&(c=Wl(null,t,e,c[1],n),c=So(c,e.attrs,n),zM(t,e,n,c))}else i=GM(t,e,n)}return i!==void 0&&(n?e.residualClasses=i:e.residualStyles=i),r}function $M(t,e,r){let n=r?e.classBindings:e.styleBindings;if(xr(n)!==0)return t[jn(n)]}function zM(t,e,r,n){let o=r?e.classBindings:e.styleBindings;t[jn(o)]=n}function GM(t,e,r){let n,o=e.directiveEnd;for(let i=1+e.directiveStylingLast;i<o;i++){let s=t[i].hostAttrs;n=So(n,s,r)}return So(n,e.attrs,r)}function Wl(t,e,r,n,o){let i=null,s=r.directiveEnd,a=r.directiveStylingLast;for(a===-1?a=r.directiveStart:a++;a<s&&(i=e[a],n=So(n,i.hostAttrs,o),i!==t);)a++;return t!==null&&(r.directiveStylingLast=a),n}function So(t,e,r){let n=r?1:2,o=-1;if(e!==null)for(let i=0;i<e.length;i++){let s=e[i];typeof s=="number"?o=s:o===n&&(Array.isArray(t)||(t=t===void 0?[]:["",t]),jC(t,s,r?!0:e[++i]))}return t===void 0?null:t}function WM(t,e,r,n,o,i,s,a){if(!(e.type&3))return;let c=t.data,l=c[a+1],d=FM(l)?Vp(c,e,r,o,xr(l),s):void 0;if(!Zs(d)){Zs(i)||OM(l)&&(i=Vp(c,null,r,o,a,s));let h=bg(Vn(),r);ZE(n,s,h,o,i)}}function Vp(t,e,r,n,o,i){let s=e===null,a;for(;o>0;){let c=t[o],l=Array.isArray(c),d=l?c[1]:c,h=d===null,g=r[o+1];g===St&&(g=h?Ke:void 0);let p=h?Fl(g,n):d===n?g:void 0;if(l&&!Zs(p)&&(p=Fl(c,n)),Zs(p)&&(a=p,s))return a;let v=t[o+1];o=s?jn(v):xr(v)}if(e!==null){let c=i?e.residualClasses:e.residualStyles;c!=null&&(a=Fl(c,n))}return a}function Zs(t){return t!==void 0}function qM(t,e){return t==null||t===""||(typeof e=="string"?t=t+e:typeof t=="object"&&(t=ke(ko(t)))),t}function ZM(t,e){return(t.flags&(e?8:16))!==0}function jr(t,e,r,n){let o=Y(),i=ge(),s=et+t,a=o[de],c=i.firstCreatePass?rv(s,i,o,e,Td,td(),r,n):i.data[s],l=YM(i,o,c,a,e,t);o[s]=l;let d=oa(c);return Ln(c,!0),Rm(a,l,c),!Rd(c)&&ca()&&ga(i,o,l,c),(p0()===0||d)&&Fr(l,o),g0(),d&&(pa(i,o,c),yd(i,c,o)),n!==null&&Md(o,c),jr}function Lr(){let t=xe();od()?id():(t=t.parent,Ln(t,!1));let e=t;v0(e)&&y0(),m0();let r=ge();return r.firstCreatePass&&ov(r,e),e.classesWithoutHost!=null&&N0(e)&&Lp(r,e,Y(),e.classesWithoutHost,!0),e.stylesWithoutHost!=null&&O0(e)&&Lp(r,e,Y(),e.stylesWithoutHost,!1),Lr}function Hd(t,e,r,n){return jr(t,e,r,n),Lr(),Hd}var YM=(t,e,r,n,o,i)=>(la(!0),xm(n,o,x0()));function QM(t,e,r,n,o){let i=e.consts,s=wr(i,n),a=Po(e,t,8,"ng-container",s);s!==null&&Au(a,s,!0);let c=wr(i,o);return td()&&Pd(e,r,a,c,Td),a.mergedAttrs=Mr(a.mergedAttrs,a.attrs),e.queries!==null&&e.queries.elementStart(e,a),a}function ba(t,e,r){let n=Y(),o=ge(),i=t+et,s=o.firstCreatePass?QM(i,o,n,e,r):o.data[i];Ln(s,!0);let a=KM(o,n,s,t);return n[i]=a,ca()&&ga(o,n,a,s),Fr(a,n),oa(s)&&(pa(o,n,s),yd(o,s,n)),r!=null&&Md(n,s),ba}function Ea(){let t=xe(),e=ge();return od()?id():(t=t.parent,Ln(t,!1)),e.firstCreatePass&&(ld(e,t),Ku(t)&&e.queries.elementEnd(t)),Ea}function wa(t,e,r){return ba(t,e,r),Ea(),wa}var KM=(t,e,r,n)=>(la(!0),gE(e[de],""));function Dv(){return Y()}var Ys="en-US";var XM=Ys;function JM(t){typeof t=="string"&&(XM=t.toLowerCase().replace(/_/g,"-"))}function Bp(t,e,r){return function n(o){if(o===Function)return r;let i=Or(t)?Dt(t.index,e):e;kd(i,5);let s=e[Pe],a=Up(e,s,r,o),c=n.__ngNextListenerFn__;for(;c;)a=Up(e,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function Up(t,e,r,n){let o=Z(null);try{return ie(6,e,r),r(n)!==!1}catch(i){return eS(t,i),!1}finally{ie(7,e,r),Z(o)}}function eS(t,e){let r=t[br],n=r?r.get(It,null):null;n&&n.handleError(e)}function Hp(t,e,r,n,o,i){let s=e[r],a=e[W],l=a.data[r].outputs[n],d=s[l],h=a.firstCreatePass?ed(a):null,g=Ju(e),p=d.subscribe(i),v=g.length;g.push(i,p),h&&h.push(o,t.index,v,-(v+1))}var tS=(t,e,r)=>{};function Ie(t,e,r,n){let o=Y(),i=ge(),s=xe();return Iv(i,o,o[de],s,t,e,n),Ie}function nS(t,e,r,n){let o=t.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===r&&o[i+1]===n){let a=e[vr],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Iv(t,e,r,n,o,i,s){var g,p;let a=oa(n),l=t.firstCreatePass?ed(t):null,d=Ju(e),h=!0;if(n.type&3||s){let v=wt(n,e),y=s?s(v):v,x=d.length,O=s?Ue=>s(yt(Ue[n.index])):n.index,X=null;if(!s&&a&&(X=nS(t,e,o,n.index)),X!==null){let Ue=X.__ngLastListenerFn__||X;Ue.__ngNextListenerFn__=i,X.__ngLastListenerFn__=i,h=!1}else{i=Bp(n,e,i),tS(y,o,i);let Ue=r.listen(y,o,i);d.push(i,Ue),l&&l.push(o,O,x,x+1)}}else i=Bp(n,e,i);if(h){let v=(g=n.outputs)==null?void 0:g[o],y=(p=n.hostDirectiveOutputs)==null?void 0:p[o];if(y&&y.length)for(let x=0;x<y.length;x+=2){let O=y[x],X=y[x+1];Hp(n,e,O,X,o,i)}if(v&&v.length)for(let x of v)Hp(n,e,x,o,o,i)}}function Bo(t=1){return _0(t)}function rS(t,e){let r=null,n=sE(t);for(let o=0;o<e.length;o++){let i=e[o];if(i==="*"){r=o;continue}if(n===null?_m(t,i,!0):lE(n,i))return o}return r}function S(t){let e=Y()[Je][Le];if(!e.projection){let r=t?t.length:1,n=e.projection=Is(r,null),o=n.slice(),i=e.child;for(;i!==null;){if(i.type!==128){let s=t?rS(i,t):0;s!==null&&(o[s]?o[s].projectionNext=i:n[s]=i,o[s]=i)}i=i.next}}}function w(t,e=0,r,n,o,i){let s=Y(),a=ge(),c=n?t+1:null;c!==null&&hv(s,a,c,n,o,i,null,r);let l=Po(a,et+t,16,null,r||null);l.projection===null&&(l.projection=e),id();let h=!s[bo]||Tg();s[Je][Le].projection[l.projection]===null&&c!==null?oS(s,a,c):h&&!Rd(l)&&WE(a,s,l)}function oS(t,e,r){let n=et+r,o=e.data[n],i=t[n],s=Tu(i,o.tView.ssrId),a=Pm(t,o,void 0,{dehydratedView:s});Xm(i,a,0,Eu(o,s))}function iS(t,e,r){return Cv(t,"",e,"",r),iS}function Cv(t,e,r,n,o){let i=Y(),s=yv(i,e,r,n);if(s!==St){let a=ge(),c=aa();Sd(a,c,i,t,s,i[de],o,!1)}return Cv}function an(t,e,r,n){Yw(t,e,r,n)}function Uo(t,e,r){Zw(t,e,r)}function lt(t){let e=Y(),r=ge(),n=Rg();sd(n+1);let o=jd(r,n);if(t.dirty&&l0(e)===((o.metadata.flags&2)===2)){if(o.matches===null)t.reset([]);else{let i=Xw(e,n);t.reset(i,fb),t.notifyOnChanges()}return!0}return!1}function ut(){return qw(Y(),Rg())}function xV(t,e=""){let r=Y(),n=ge(),o=t+et,i=n.firstCreatePass?Po(n,o,1,e,null):n.data[o],s=sS(n,r,i,e,t);r[o]=s,ca()&&ga(n,r,s,i),Ln(i,!1)}var sS=(t,e,r,n,o)=>(la(!0),hE(e[de],n));function aS(t){return bv("",t,""),aS}function bv(t,e,r){let n=Y(),o=yv(n,t,e,r);return o!==St&&Ev(n,Vn(),o),bv}function cS(t,e,r,n,o){let i=Y(),s=NM(i,t,e,r,n,o);return s!==St&&Ev(i,Vn(),s),cS}function Ev(t,e,r){let n=bg(e,t);pE(t[de],n,r)}function lS(t,e,r){om(e)&&(e=e());let n=Y(),o=sa();if(Pn(n,o,e)){let i=ge(),s=aa();Sd(i,s,n,t,e,n[de],r,!1)}return lS}function AV(t,e){let r=om(t);return r&&t.set(e),r}function uS(t,e){let r=Y(),n=ge(),o=xe();return Iv(n,r,r[de],o,t,e),uS}function dS(t,e,r){let n=ge();if(n.firstCreatePass){let o=vt(t);Uu(r,n.data,n.blueprint,o,!0),Uu(e,n.data,n.blueprint,o,!1)}}function Uu(t,e,r,n,o){if(t=Se(t),Array.isArray(t))for(let i=0;i<t.length;i++)Uu(t[i],e,r,n,o);else{let i=ge(),s=Y(),a=xe(),c=Cr(t)?t:Se(t.provide),l=pg(t),d=a.providerIndexes&1048575,h=a.directiveStart,g=a.providerIndexes>>20;if(Cr(t)||!t.multi){let p=new kn(l,o,f),v=Zl(c,e,o?d:d+g,h);v===-1?(lu(Ps(a,s),i,c),ql(i,t,e.length),e.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),r.push(p),s.push(p)):(r[v]=p,s[v]=p)}else{let p=Zl(c,e,d+g,h),v=Zl(c,e,d,d+g),y=p>=0&&r[p],x=v>=0&&r[v];if(o&&!x||!o&&!y){lu(Ps(a,s),i,c);let O=pS(o?hS:fS,r.length,o,n,l);!o&&x&&(r[v].providerFactory=O),ql(i,t,e.length,0),e.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),r.push(O),s.push(O)}else{let O=wv(r[o?v:p],l,!o&&n);ql(i,t,p>-1?p:v,O)}!o&&n&&x&&r[v].componentProviders++}}}function ql(t,e,r,n){let o=Cr(e),i=GC(e);if(o||i){let c=(i?Se(e.useClass):e).prototype.ngOnDestroy;if(c){let l=t.destroyHooks||(t.destroyHooks=[]);if(!o&&e.multi){let d=l.indexOf(r);d===-1?l.push(r,[n,c]):l[d+1].push(n,c)}else l.push(r,c)}}}function wv(t,e,r){return r&&t.componentProviders++,t.multi.push(e)-1}function Zl(t,e,r,n){for(let o=r;o<n;o++)if(e[o]===t)return o;return-1}function fS(t,e,r,n){return Hu(this.multi,[])}function hS(t,e,r,n){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=wo(r,r[W],this.providerFactory.index,n);i=a.slice(0,s),Hu(o,i);for(let c=s;c<a.length;c++)i.push(a[c])}else i=[],Hu(o,i);return i}function Hu(t,e){for(let r=0;r<t.length;r++){let n=t[r];e.push(n())}return e}function pS(t,e,r,n,o){let i=new kn(t,r,f);return i.multi=[],i.index=e,i.componentProviders=0,wv(i,o,n&&!r),i}function Ne(t,e=[]){return r=>{r.providersResolver=(n,o)=>dS(n,o?o(t):t,e)}}function RV(t,e,r){let n=I0()+t,o=Y();return o[n]===St?IM(o,n,r?e.call(r):e()):CM(o,n)}var Ds=null;function gS(t){Ds!==null&&(t.defaultEncapsulation!==Ds.defaultEncapsulation||t.preserveWhitespaces!==Ds.preserveWhitespaces)||(Ds=t)}var $u=class{ngModuleFactory;componentFactories;constructor(e,r){this.ngModuleFactory=e,this.componentFactories=r}},Ma=(()=>{let e=class e{compileModuleSync(n){return new qs(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let o=this.compileModuleSync(n),i=ug(n),s=Sm(i.declarations).reduce((a,c)=>{let l=nn(c);return l&&a.push(new Fn(l)),a},[]);return new $u(o,s)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),mS=new N("");function vS(t,e,r){let n=new qs(r);return Promise.resolve(n)}function $p(t){for(let e=t.length-1;e>=0;e--)if(t[e]!==void 0)return t[e]}var yS=(()=>{let e=class e{zone=I(m);changeDetectionScheduler=I(Sr);applicationRef=I(Et);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){var n;(n=this._onMicrotaskEmptySubscription)==null||n.unsubscribe()}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();function DS({ngZoneFactory:t,ignoreChangesOutsideZone:e,scheduleInRootZone:r}){return t!=null||(t=()=>new m(L(b({},Mv()),{scheduleInRootZone:r}))),[{provide:m,useFactory:t},{provide:Io,multi:!0,useFactory:()=>{let n=I(yS,{optional:!0});return()=>n.initialize()}},{provide:Io,multi:!0,useFactory:()=>{let n=I(IS);return()=>{n.initialize()}}},e===!0?{provide:Jg,useValue:!0}:[],{provide:em,useValue:r!=null?r:Kg}]}function Mv(t){var e,r;return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:(e=t==null?void 0:t.eventCoalescing)!=null?e:!1,shouldCoalesceRunChangeDetection:(r=t==null?void 0:t.runCoalescing)!=null?r:!1}}var IS=(()=>{let e=class e{subscription=new ue;initialized=!1;zone=I(m);pendingTasks=I(Ut);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{m.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{m.assertInAngularZone(),n!=null||(n=this.pendingTasks.add())}))}ngOnDestroy(){this.subscription.unsubscribe()}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();var CS=(()=>{var e,r,n;let o=class o{appRef=I(Et);taskService=I(Ut);ngZone=I(m);zonelessEnabled=I(Xg);tracing=I(Pr,{optional:!0});disableScheduling=(e=I(Jg,{optional:!0}))!=null?e:!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new ue;angularZoneId=this.zoneIsDefined?(r=this.ngZone._inner)==null?void 0:r.get(Ls):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&((n=I(em,{optional:!0}))!=null?n:!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||(this.disableScheduling=!this.zonelessEnabled&&(this.ngZone instanceof Vs||!this.zoneIsDefined))}notify(s){var l,d;if(!this.zonelessEnabled&&s===5)return;let a=!1;switch(s){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,a=!0;break}case 12:{this.appRef.dirtyFlags|=16,a=!0;break}case 13:{this.appRef.dirtyFlags|=2,a=!0;break}case 11:{a=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=(d=(l=this.tracing)==null?void 0:l.snapshot(this.appRef.tracingSnapshot))!=null?d:null,!this.shouldScheduleTick(a))return;let c=this.useMicrotaskScheduler?vp:tm;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>c(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>c(()=>this.tick()))}shouldScheduleTick(s){return!(this.disableScheduling&&!s||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Ls+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let s=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(a){throw this.taskService.remove(s),a}finally{this.cleanup()}this.useMicrotaskScheduler=!0,vp(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(s)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){var s;if(this.runningTick=!1,(s=this.cancelScheduledCallback)==null||s.call(this),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let a=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(a)}}};u(o,"\u0275fac",function(a){return new(a||o)}),u(o,"\u0275prov",A({token:o,factory:o.\u0275fac,providedIn:"root"}));let t=o;return t})();function bS(){return typeof $localize<"u"&&$localize.locale||Ys}var $d=new N("",{providedIn:"root",factory:()=>I($d,z.Optional|z.SkipSelf)||bS()});var Qs=new N(""),ES=new N("");function po(t){return!t.moduleRef}function wS(t){let e=po(t)?t.r3Injector:t.moduleRef.injector,r=e.get(m);return r.run(()=>{po(t)?t.r3Injector.resolveInjectorInitializers():t.moduleRef.resolveInjectorInitializers();let n=e.get(It,null),o;if(r.runOutsideAngular(()=>{o=r.onError.subscribe({next:i=>{n.handleError(i)}})}),po(t)){let i=()=>e.destroy(),s=t.platformInjector.get(Qs);s.add(i),e.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>t.moduleRef.destroy(),s=t.platformInjector.get(Qs);s.add(i),t.moduleRef.onDestroy(()=>{Ms(t.allPlatformModules,t.moduleRef),o.unsubscribe(),s.delete(i)})}return SS(n,r,()=>{let i=e.get(mv);return i.runInitializers(),i.donePromise.then(()=>{let s=e.get($d,Ys);if(JM(s||Ys),!e.get(ES,!0))return po(t)?e.get(Et):(t.allPlatformModules.push(t.moduleRef),t.moduleRef);if(po(t)){let c=e.get(Et);return t.rootComponent!==void 0&&c.bootstrap(t.rootComponent),c}else return MS(t.moduleRef,t.allPlatformModules),t.moduleRef})})})}function MS(t,e){let r=t.injector.get(Et);if(t._bootstrapComponents.length>0)t._bootstrapComponents.forEach(n=>r.bootstrap(n));else if(t.instance.ngDoBootstrap)t.instance.ngDoBootstrap(r);else throw new R(-403,!1);e.push(t)}function SS(t,e,r){try{let n=r();return Bn(n)?n.catch(o=>{throw e.runOutsideAngular(()=>t.handleError(o)),o}):n}catch(n){throw e.runOutsideAngular(()=>t.handleError(n)),n}}var Sv=(()=>{let e=class e{_injector;_modules=[];_destroyListeners=[];_destroyed=!1;constructor(n){this._injector=n}bootstrapModuleFactory(n,o){let i=o==null?void 0:o.scheduleInRootZone,s=()=>cb(o==null?void 0:o.ngZone,L(b({},Mv({eventCoalescing:o==null?void 0:o.ngZoneEventCoalescing,runCoalescing:o==null?void 0:o.ngZoneRunCoalescing})),{scheduleInRootZone:i})),a=o==null?void 0:o.ignoreChangesOutsideZone,c=[DS({ngZoneFactory:s,ignoreChangesOutsideZone:a}),{provide:Sr,useExisting:CS}],l=iM(n.moduleType,this.injector,c);return wS({moduleRef:l,allPlatformModules:this._modules,platformInjector:this.injector})}bootstrapModule(n,o=[]){let i=vv({},o);return vS(this.injector,i,n).then(s=>this.bootstrapModuleFactory(s,i))}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new R(404,!1);this._modules.slice().forEach(o=>o.destroy()),this._destroyListeners.forEach(o=>o());let n=this._injector.get(Qs,null);n&&(n.forEach(o=>o()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}};u(e,"\u0275fac",function(o){return new(o||e)(k(te))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"platform"}));let t=e;return t})(),Tn=null,Tv=new N("");function TS(t){if(Tn&&!Tn.get(Tv,!1))throw new R(400,!1);_M(),Tn=t;let e=t.get(Sv);return AS(t),e}function zd(t,e,r=[]){let n=`Platform: ${e}`,o=new N(n);return(i=[])=>{let s=_v();if(!s||s.injector.get(Tv,!1)){let a=[...r,...i,{provide:o,useValue:!0}];t?t(a):TS(_S(a,n))}return xS(o)}}function _S(t=[],e){return te.create({name:e,providers:[{provide:ta,useValue:"platform"},{provide:Qs,useValue:new Set([()=>Tn=null])},...t]})}function xS(t){let e=_v();if(!e)throw new R(401,!1);return e}function _v(){var t;return(t=Tn==null?void 0:Tn.get(Sv))!=null?t:null}function AS(t){let e=t.get(pd,null);je(t,()=>{e==null||e.forEach(r=>r())})}var C=(()=>{class t{}return u(t,"__NG_ELEMENT_ID__",RS),t})();function RS(t){return NS(xe(),Y(),(t&16)===16)}function NS(t,e,r){if(Or(t)&&!r){let n=Dt(t.index,e);return new Mo(n,n)}else if(t.type&175){let n=e[Je];return new Mo(n,e)}return null}var zu=class{constructor(){}supports(e){return fv(e)}create(e){return new Gu(e)}},OS=(t,e)=>e,Gu=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(e){this._trackByFn=e||OS}forEachItem(e){let r;for(r=this._itHead;r!==null;r=r._next)e(r)}forEachOperation(e){let r=this._itHead,n=this._removalsHead,o=0,i=null;for(;r||n;){let s=!n||r&&r.currentIndex<zp(n,o,i)?r:n,a=zp(s,o,i),c=s.currentIndex;if(s===n)o--,n=n._nextRemoved;else if(r=r._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,d=c-o;if(l!=d){for(let g=0;g<l;g++){let p=g<i.length?i[g]:i[g]=0,v=p+g;d<=v&&v<l&&(i[g]=p+1)}let h=s.previousIndex;i[h]=d-l}}a!==c&&e(s,a,c)}}forEachPreviousItem(e){let r;for(r=this._previousItHead;r!==null;r=r._nextPrevious)e(r)}forEachAddedItem(e){let r;for(r=this._additionsHead;r!==null;r=r._nextAdded)e(r)}forEachMovedItem(e){let r;for(r=this._movesHead;r!==null;r=r._nextMoved)e(r)}forEachRemovedItem(e){let r;for(r=this._removalsHead;r!==null;r=r._nextRemoved)e(r)}forEachIdentityChange(e){let r;for(r=this._identityChangesHead;r!==null;r=r._nextIdentityChange)e(r)}diff(e){if(e==null&&(e=[]),!fv(e))throw new R(900,!1);return this.check(e)?this:null}onDestroy(){}check(e){this._reset();let r=this._itHead,n=!1,o,i,s;if(Array.isArray(e)){this.length=e.length;for(let a=0;a<this.length;a++)i=e[a],s=this._trackByFn(a,i),r===null||!Object.is(r.trackById,s)?(r=this._mismatch(r,i,s,a),n=!0):(n&&(r=this._verifyReinsertion(r,i,s,a)),Object.is(r.item,i)||this._addIdentityChange(r,i)),r=r._next}else o=0,yM(e,a=>{s=this._trackByFn(o,a),r===null||!Object.is(r.trackById,s)?(r=this._mismatch(r,a,s,o),n=!0):(n&&(r=this._verifyReinsertion(r,a,s,o)),Object.is(r.item,a)||this._addIdentityChange(r,a)),r=r._next,o++}),this.length=o;return this._truncate(r),this.collection=e,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let e;for(e=this._previousItHead=this._itHead;e!==null;e=e._next)e._nextPrevious=e._next;for(e=this._additionsHead;e!==null;e=e._nextAdded)e.previousIndex=e.currentIndex;for(this._additionsHead=this._additionsTail=null,e=this._movesHead;e!==null;e=e._nextMoved)e.previousIndex=e.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(e,r,n,o){let i;return e===null?i=this._itTail:(i=e._prev,this._remove(e)),e=this._unlinkedRecords===null?null:this._unlinkedRecords.get(n,null),e!==null?(Object.is(e.item,r)||this._addIdentityChange(e,r),this._reinsertAfter(e,i,o)):(e=this._linkedRecords===null?null:this._linkedRecords.get(n,o),e!==null?(Object.is(e.item,r)||this._addIdentityChange(e,r),this._moveAfter(e,i,o)):e=this._addAfter(new Wu(r,n),i,o)),e}_verifyReinsertion(e,r,n,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(n,null);return i!==null?e=this._reinsertAfter(i,e._prev,o):e.currentIndex!=o&&(e.currentIndex=o,this._addToMoves(e,o)),e}_truncate(e){for(;e!==null;){let r=e._next;this._addToRemovals(this._unlink(e)),e=r}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(e,r,n){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(e);let o=e._prevRemoved,i=e._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(e,r,n),this._addToMoves(e,n),e}_moveAfter(e,r,n){return this._unlink(e),this._insertAfter(e,r,n),this._addToMoves(e,n),e}_addAfter(e,r,n){return this._insertAfter(e,r,n),this._additionsTail===null?this._additionsTail=this._additionsHead=e:this._additionsTail=this._additionsTail._nextAdded=e,e}_insertAfter(e,r,n){let o=r===null?this._itHead:r._next;return e._next=o,e._prev=r,o===null?this._itTail=e:o._prev=e,r===null?this._itHead=e:r._next=e,this._linkedRecords===null&&(this._linkedRecords=new Ks),this._linkedRecords.put(e),e.currentIndex=n,e}_remove(e){return this._addToRemovals(this._unlink(e))}_unlink(e){this._linkedRecords!==null&&this._linkedRecords.remove(e);let r=e._prev,n=e._next;return r===null?this._itHead=n:r._next=n,n===null?this._itTail=r:n._prev=r,e}_addToMoves(e,r){return e.previousIndex===r||(this._movesTail===null?this._movesTail=this._movesHead=e:this._movesTail=this._movesTail._nextMoved=e),e}_addToRemovals(e){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Ks),this._unlinkedRecords.put(e),e.currentIndex=null,e._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=e,e._prevRemoved=null):(e._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=e),e}_addIdentityChange(e,r){return e.item=r,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=e:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=e,e}},Wu=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(e,r){this.item=e,this.trackById=r}},qu=class{_head=null;_tail=null;add(e){this._head===null?(this._head=this._tail=e,e._nextDup=null,e._prevDup=null):(this._tail._nextDup=e,e._prevDup=this._tail,e._nextDup=null,this._tail=e)}get(e,r){let n;for(n=this._head;n!==null;n=n._nextDup)if((r===null||r<=n.currentIndex)&&Object.is(n.trackById,e))return n;return null}remove(e){let r=e._prevDup,n=e._nextDup;return r===null?this._head=n:r._nextDup=n,n===null?this._tail=r:n._prevDup=r,this._head===null}},Ks=class{map=new Map;put(e){let r=e.trackById,n=this.map.get(r);n||(n=new qu,this.map.set(r,n)),n.add(e)}get(e,r){let n=e,o=this.map.get(n);return o?o.get(e,r):null}remove(e){let r=e.trackById;return this.map.get(r).remove(e)&&this.map.delete(r),e}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function zp(t,e,r){let n=t.previousIndex;if(n===null)return n;let o=0;return r&&n<r.length&&(o=r[n]),n+e+o}function Gp(){return new Gd([new zu])}var Gd=(()=>{let e=class e{factories;constructor(n){this.factories=n}static create(n,o){if(o!=null){let i=o.factories.slice();n=n.concat(i)}return new e(n)}static extend(n){return{provide:e,useFactory:o=>e.create(n,o||Gp()),deps:[[e,new sg,new ig]]}}find(n){let o=this.factories.find(i=>i.supports(n));if(o!=null)return o;throw new R(901,!1)}};u(e,"\u0275prov",A({token:e,providedIn:"root",factory:Gp}));let t=e;return t})();var xv=zd(null,"core",[]),Av=(()=>{let e=class e{constructor(n){}};u(e,"\u0275fac",function(o){return new(o||e)(k(Et))}),u(e,"\u0275mod",Re({type:e})),u(e,"\u0275inj",_e({}));let t=e;return t})();function cn(t){return typeof t=="boolean"?t:t!=null&&t!=="false"}function $t(t){return Ol(t)}function Ho(t,e){return xl(t,e==null?void 0:e.equal)}var Wp=class{[Ye];constructor(e){this[Ye]=e}destroy(){this[Ye].destroy()}};function Rv(t,e){let r=nn(t),n=e.elementInjector||na();return new Fn(r).create(n,e.projectableNodes,e.hostElement,e.environmentInjector)}function Sa(t){let e=nn(t);if(!e)return null;let r=new Fn(e);return{get selector(){return r.selector},get type(){return r.componentType},get inputs(){return r.inputs},get outputs(){return r.outputs},get ngContentSelectors(){return r.ngContentSelectors},get isStandalone(){return e.standalone},get isSignal(){return e.signals}}}var ce=new N("");var Ta=null;function Ge(){return Ta}function Wd(t){Ta!=null||(Ta=t)}var $o=class{},zo=(()=>{let e=class e{historyGo(n){throw new Error("")}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>I(kv),providedIn:"platform"}));let t=e;return t})(),qd=new N(""),kv=(()=>{let e=class e extends zo{_location;_history;_doc=I(ce);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Ge().getBaseHref(this._doc)}onPopState(n){let o=Ge().getGlobalEventTarget(this._doc,"window");return o.addEventListener("popstate",n,!1),()=>o.removeEventListener("popstate",n)}onHashChange(n){let o=Ge().getGlobalEventTarget(this._doc,"window");return o.addEventListener("hashchange",n,!1),()=>o.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,o,i){this._history.pushState(n,o,i)}replaceState(n,o,i){this._history.replaceState(n,o,i)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>new e,providedIn:"platform"}));let t=e;return t})();function _a(t,e){return t?e?t.endsWith("/")?e.startsWith("/")?t+e.slice(1):t+e:e.startsWith("/")?t+e:`${t}/${e}`:t:e}function Nv(t){let e=t.search(/#|\?|$/);return t[e-1]==="/"?t.slice(0,e-1)+t.slice(e):t}function ft(t){return t&&t[0]!=="?"?`?${t}`:t}var Ve=(()=>{let e=class e{historyGo(n){throw new Error("")}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>I(Aa),providedIn:"root"}));let t=e;return t})(),xa=new N(""),Aa=(()=>{let e=class e extends Ve{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,o){var i,s,a;super(),this._platformLocation=n,this._baseHref=(a=(s=o!=null?o:this._platformLocation.getBaseHrefFromDOM())!=null?s:(i=I(ce).location)==null?void 0:i.origin)!=null?a:""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return _a(this._baseHref,n)}path(n=!1){let o=this._platformLocation.pathname+ft(this._platformLocation.search),i=this._platformLocation.hash;return i&&n?`${o}${i}`:o}pushState(n,o,i,s){let a=this.prepareExternalUrl(i+ft(s));this._platformLocation.pushState(n,o,a)}replaceState(n,o,i,s){let a=this.prepareExternalUrl(i+ft(s));this._platformLocation.replaceState(n,o,a)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){var o,i;(i=(o=this._platformLocation).historyGo)==null||i.call(o,n)}};u(e,"\u0275fac",function(o){return new(o||e)(k(zo),k(xa,8))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),We=(()=>{let e=class e{_subject=new ee;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let o=this._locationStrategy.getBaseHref();this._basePath=PS(Nv(Ov(o))),this._locationStrategy.onPopState(i=>{this._subject.next({url:this.path(!0),pop:!0,state:i.state,type:i.type})})}ngOnDestroy(){var n;(n=this._urlChangeSubscription)==null||n.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,o=""){return this.path()==this.normalize(n+ft(o))}normalize(n){return e.stripTrailingSlash(FS(this._basePath,Ov(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,o="",i=null){this._locationStrategy.pushState(i,"",n,o),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+ft(o)),i)}replaceState(n,o="",i=null){this._locationStrategy.replaceState(i,"",n,o),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+ft(o)),i)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){var o,i;(i=(o=this._locationStrategy).historyGo)==null||i.call(o,n)}onUrlChange(n){var o;return this._urlChangeListeners.push(n),(o=this._urlChangeSubscription)!=null||(this._urlChangeSubscription=this.subscribe(i=>{this._notifyUrlChangeListeners(i.url,i.state)})),()=>{var s;let i=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(i,1),this._urlChangeListeners.length===0&&((s=this._urlChangeSubscription)==null||s.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",o){this._urlChangeListeners.forEach(i=>i(n,o))}subscribe(n,o,i){return this._subject.subscribe({next:n,error:o!=null?o:void 0,complete:i!=null?i:void 0})}};u(e,"normalizeQueryParams",ft),u(e,"joinWithSlash",_a),u(e,"stripTrailingSlash",Nv),u(e,"\u0275fac",function(o){return new(o||e)(k(Ve))}),u(e,"\u0275prov",A({token:e,factory:()=>kS(),providedIn:"root"}));let t=e;return t})();function kS(){return new We(k(Ve))}function FS(t,e){if(!t||!e.startsWith(t))return e;let r=e.substring(t.length);return r===""||["/",";","?","#"].includes(r[0])?r:e}function Ov(t){return t.replace(/\/index.html$/,"")}function PS(t){if(new RegExp("^(https?:)?//").test(t)){let[,r]=t.split(/\/\/[^\/]+/);return r}return t}var Yd=(()=>{let e=class e extends Ve{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,o){super(),this._platformLocation=n,o!=null&&(this._baseHref=o)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){var i;let o=(i=this._platformLocation.hash)!=null?i:"#";return o.length>0?o.substring(1):o}prepareExternalUrl(n){let o=_a(this._baseHref,n);return o.length>0?"#"+o:o}pushState(n,o,i,s){let a=this.prepareExternalUrl(i+ft(s))||this._platformLocation.pathname;this._platformLocation.pushState(n,o,a)}replaceState(n,o,i,s){let a=this.prepareExternalUrl(i+ft(s))||this._platformLocation.pathname;this._platformLocation.replaceState(n,o,a)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){var o,i;(i=(o=this._platformLocation).historyGo)==null||i.call(o,n)}};u(e,"\u0275fac",function(o){return new(o||e)(k(zo),k(xa,8))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();var Zd=/\s+/,Fv=[],jS=(()=>{let e=class e{_ngEl;_renderer;initialClasses=Fv;rawClass;stateMap=new Map;constructor(n,o){this._ngEl=n,this._renderer=o}set klass(n){this.initialClasses=n!=null?n.trim().split(Zd):Fv}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(Zd):n}ngDoCheck(){for(let o of this.initialClasses)this._updateState(o,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let o of n)this._updateState(o,!0);else if(n!=null)for(let o of Object.keys(n))this._updateState(o,!!n[o]);this._applyStateDiff()}_updateState(n,o){let i=this.stateMap.get(n);i!==void 0?(i.enabled!==o&&(i.changed=!0,i.enabled=o),i.touched=!0):this.stateMap.set(n,{enabled:o,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let o=n[0],i=n[1];i.changed?(this._toggleClass(o,i.enabled),i.changed=!1):i.touched||(i.enabled&&this._toggleClass(o,!1),this.stateMap.delete(o)),i.touched=!1}}_toggleClass(n,o){n=n.trim(),n.length>0&&n.split(Zd).forEach(i=>{o?this._renderer.addClass(this._ngEl.nativeElement,i):this._renderer.removeClass(this._ngEl.nativeElement,i)})}};u(e,"\u0275fac",function(o){return new(o||e)(f(D),f(on))}),u(e,"\u0275dir",U({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}}));let t=e;return t})();var Ra=class{$implicit;ngForOf;index;count;constructor(e,r,n,o){this.$implicit=e,this.ngForOf=r,this.index=n,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Lv=(()=>{let e=class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,o,i){this._viewContainer=n,this._template=o,this._differs=i}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let o=this._viewContainer;n.forEachOperation((i,s,a)=>{if(i.previousIndex==null)o.createEmbeddedView(this._template,new Ra(i.item,this._ngForOf,-1,-1),a===null?void 0:a);else if(a==null)o.remove(s===null?void 0:s);else if(s!==null){let c=o.get(s);o.move(c,a),Pv(c,i)}});for(let i=0,s=o.length;i<s;i++){let c=o.get(i).context;c.index=i,c.count=s,c.ngForOf=this._ngForOf}n.forEachIdentityChange(i=>{let s=o.get(i.currentIndex);Pv(s,i)})}static ngTemplateContextGuard(n,o){return!0}};u(e,"\u0275fac",function(o){return new(o||e)(f(ze),f(bt),f(Gd))}),u(e,"\u0275dir",U({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}}));let t=e;return t})();function Pv(t,e){t.context.$implicit=e.item}var Go=(()=>{let e=class e{_viewContainer;_context=new Na;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,o){this._viewContainer=n,this._thenTemplateRef=o}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){jv(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){jv(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(n,o){return!0}};u(e,"ngIfUseIfTypeGuard"),u(e,"ngTemplateGuard_ngIf"),u(e,"\u0275fac",function(o){return new(o||e)(f(ze),f(bt))}),u(e,"\u0275dir",U({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}}));let t=e;return t})(),Na=class{$implicit=null;ngIf=null};function jv(t,e){if(t&&!t.createEmbeddedView)throw new R(2020,!1)}var Oa=(()=>{let e=class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(n){this._viewContainerRef=n}ngOnChanges(n){var o;if(this._shouldRecreateView(n)){let i=this._viewContainerRef;if(this._viewRef&&i.remove(i.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let s=this._createContextForwardProxy();this._viewRef=i.createEmbeddedView(this.ngTemplateOutlet,s,{injector:(o=this.ngTemplateOutletInjector)!=null?o:void 0})}}_shouldRecreateView(n){return!!n.ngTemplateOutlet||!!n.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(n,o,i)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,o,i):!1,get:(n,o,i)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,o,i)}})}};u(e,"\u0275fac",function(o){return new(o||e)(f(ze))}),u(e,"\u0275dir",U({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[tt]}));let t=e;return t})();var Wo=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275mod",Re({type:e})),u(e,"\u0275inj",_e({}));let t=e;return t})();function qo(t,e){e=encodeURIComponent(e);for(let r of t.split(";")){let n=r.indexOf("="),[o,i]=n==-1?[r,""]:[r.slice(0,n),r.slice(n+1)];if(o.trim()===e)return decodeURIComponent(i)}return null}var Qd="browser",Vv="server";function ka(t){return t===Vv}var Un=class{};var Bv=(()=>{let e=class e{};u(e,"\u0275prov",A({token:e,providedIn:"root",factory:()=>new Kd(I(ce),window)}));let t=e;return t})(),Kd=class{document;window;offset=()=>[0,0];constructor(e,r){this.document=e,this.window=r}setOffset(e){Array.isArray(e)?this.offset=()=>e:this.offset=e}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(e){this.window.scrollTo(e[0],e[1])}scrollToAnchor(e){let r=VS(this.document,e);r&&(this.scrollToElement(r),r.focus())}setHistoryScrollRestoration(e){this.window.history.scrollRestoration=e}scrollToElement(e){let r=e.getBoundingClientRect(),n=r.left+this.window.pageXOffset,o=r.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(n-i[0],o-i[1])}};function VS(t,e){let r=t.getElementById(e)||t.getElementsByName(e)[0];if(r)return r;if(typeof t.createTreeWalker=="function"&&t.body&&typeof t.body.attachShadow=="function"){let n=t.createTreeWalker(t.body,NodeFilter.SHOW_ELEMENT),o=n.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(e)||i.querySelector(`[name="${e}"]`);if(s)return s}o=n.nextNode()}}return null}var ja=new N(""),nf=(()=>{let e=class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,o){this._zone=o,n.forEach(i=>{i.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,o,i,s){return this._findPluginFor(o).addEventListener(n,o,i,s)}getZone(){return this._zone}_findPluginFor(n){let o=this._eventNameToPlugin.get(n);if(o)return o;if(o=this._plugins.find(s=>s.supports(n)),!o)throw new R(5101,!1);return this._eventNameToPlugin.set(n,o),o}};u(e,"\u0275fac",function(o){return new(o||e)(k(ja),k(m))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),Zo=class{_doc;constructor(e){this._doc=e}manager},Fa="ng-app-id";function Uv(t){for(let e of t)e.remove()}function Hv(t,e){let r=e.createElement("style");return r.textContent=t,r}function BS(t,e,r,n){var i;let o=(i=t.head)==null?void 0:i.querySelectorAll(`style[${Fa}="${e}"],link[${Fa}="${e}"]`);if(o)for(let s of o)s.removeAttribute(Fa),s instanceof HTMLLinkElement?n.set(s.href.slice(s.href.lastIndexOf("/")+1),{usage:0,elements:[s]}):s.textContent&&r.set(s.textContent,{usage:0,elements:[s]})}function ef(t,e){let r=e.createElement("link");return r.setAttribute("rel","stylesheet"),r.setAttribute("href",t),r}var rf=(()=>{let e=class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,o,i,s={}){this.doc=n,this.appId=o,this.nonce=i,this.isServer=ka(s),BS(n,o,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,o){for(let i of n)this.addUsage(i,this.inline,Hv);o==null||o.forEach(i=>this.addUsage(i,this.external,ef))}removeStyles(n,o){for(let i of n)this.removeUsage(i,this.inline);o==null||o.forEach(i=>this.removeUsage(i,this.external))}addUsage(n,o,i){let s=o.get(n);s?s.usage++:o.set(n,{usage:1,elements:[...this.hosts].map(a=>this.addElement(a,i(n,this.doc)))})}removeUsage(n,o){let i=o.get(n);i&&(i.usage--,i.usage<=0&&(Uv(i.elements),o.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])Uv(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[o,{elements:i}]of this.inline)i.push(this.addElement(n,Hv(o,this.doc)));for(let[o,{elements:i}]of this.external)i.push(this.addElement(n,ef(o,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,o){return this.nonce&&o.setAttribute("nonce",this.nonce),this.isServer&&o.setAttribute(Fa,this.appId),n.appendChild(o)}};u(e,"\u0275fac",function(o){return new(o||e)(k(ce),k(hd),k(gd,8),k(rn))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),Jd={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},of=/%COMP%/g;var zv="%COMP%",US=`_nghost-${zv}`,HS=`_ngcontent-${zv}`,$S=!0,zS=new N("",{providedIn:"root",factory:()=>$S});function GS(t){return HS.replace(of,t)}function WS(t){return US.replace(of,t)}function Gv(t,e){return e.map(r=>r.replace(of,t))}var sf=(()=>{let e=class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,o,i,s,a,c,l,d=null,h=null){this.eventManager=n,this.sharedStylesHost=o,this.appId=i,this.removeStylesOnCompDestroy=s,this.doc=a,this.platformId=c,this.ngZone=l,this.nonce=d,this.tracingService=h,this.platformIsServer=ka(c),this.defaultRenderer=new Yo(n,a,l,this.platformIsServer,this.tracingService)}createRenderer(n,o){if(!n||!o)return this.defaultRenderer;this.platformIsServer&&o.encapsulation===Ct.ShadowDom&&(o=L(b({},o),{encapsulation:Ct.Emulated}));let i=this.getOrCreateRenderer(n,o);return i instanceof Pa?i.applyToHost(n):i instanceof Qo&&i.applyStyles(),i}getOrCreateRenderer(n,o){let i=this.rendererByCompId,s=i.get(o.id);if(!s){let a=this.doc,c=this.ngZone,l=this.eventManager,d=this.sharedStylesHost,h=this.removeStylesOnCompDestroy,g=this.platformIsServer,p=this.tracingService;switch(o.encapsulation){case Ct.Emulated:s=new Pa(l,d,o,this.appId,h,a,c,g,p);break;case Ct.ShadowDom:return new tf(l,d,n,o,a,c,this.nonce,g,p);default:s=new Qo(l,d,o,h,a,c,g,p);break}i.set(o.id,s)}return s}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}};u(e,"\u0275fac",function(o){return new(o||e)(k(nf),k(rf),k(hd),k(zS),k(ce),k(rn),k(m),k(gd),k(Pr,8))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),Yo=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(e,r,n,o,i){this.eventManager=e,this.doc=r,this.ngZone=n,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(e,r){return r?this.doc.createElementNS(Jd[r]||r,e):this.doc.createElement(e)}createComment(e){return this.doc.createComment(e)}createText(e){return this.doc.createTextNode(e)}appendChild(e,r){($v(e)?e.content:e).appendChild(r)}insertBefore(e,r,n){e&&($v(e)?e.content:e).insertBefore(r,n)}removeChild(e,r){r.remove()}selectRootElement(e,r){let n=typeof e=="string"?this.doc.querySelector(e):e;if(!n)throw new R(-5104,!1);return r||(n.textContent=""),n}parentNode(e){return e.parentNode}nextSibling(e){return e.nextSibling}setAttribute(e,r,n,o){if(o){r=o+":"+r;let i=Jd[o];i?e.setAttributeNS(i,r,n):e.setAttribute(r,n)}else e.setAttribute(r,n)}removeAttribute(e,r,n){if(n){let o=Jd[n];o?e.removeAttributeNS(o,r):e.removeAttribute(`${n}:${r}`)}else e.removeAttribute(r)}addClass(e,r){e.classList.add(r)}removeClass(e,r){e.classList.remove(r)}setStyle(e,r,n,o){o&(Lt.DashCase|Lt.Important)?e.style.setProperty(r,n,o&Lt.Important?"important":""):e.style[r]=n}removeStyle(e,r,n){n&Lt.DashCase?e.style.removeProperty(r):e.style[r]=""}setProperty(e,r,n){e!=null&&(e[r]=n)}setValue(e,r){e.nodeValue=r}listen(e,r,n,o){var s;if(typeof e=="string"&&(e=Ge().getGlobalEventTarget(this.doc,e),!e))throw new R(5102,!1);let i=this.decoratePreventDefault(n);return(s=this.tracingService)!=null&&s.wrapEventListener&&(i=this.tracingService.wrapEventListener(e,r,i)),this.eventManager.addEventListener(e,r,i,o)}decoratePreventDefault(e){return r=>{if(r==="__ngUnwrap__")return e;(this.platformIsServer?this.ngZone.runGuarded(()=>e(r)):e(r))===!1&&r.preventDefault()}}};function $v(t){return t.tagName==="TEMPLATE"&&t.content!==void 0}var tf=class extends Yo{sharedStylesHost;hostEl;shadowRoot;constructor(e,r,n,o,i,s,a,c,l){var p;super(e,i,s,c,l),this.sharedStylesHost=r,this.hostEl=n,this.shadowRoot=n.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let d=o.styles;d=Gv(o.id,d);for(let v of d){let y=document.createElement("style");a&&y.setAttribute("nonce",a),y.textContent=v,this.shadowRoot.appendChild(y)}let h=(p=o.getExternalStyles)==null?void 0:p.call(o);if(h)for(let v of h){let y=ef(v,i);a&&y.setAttribute("nonce",a),this.shadowRoot.appendChild(y)}}nodeOrShadowRoot(e){return e===this.hostEl?this.shadowRoot:e}appendChild(e,r){return super.appendChild(this.nodeOrShadowRoot(e),r)}insertBefore(e,r,n){return super.insertBefore(this.nodeOrShadowRoot(e),r,n)}removeChild(e,r){return super.removeChild(null,r)}parentNode(e){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(e)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Qo=class extends Yo{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(e,r,n,o,i,s,a,c,l){var g;super(e,i,s,a,c),this.sharedStylesHost=r,this.removeStylesOnCompDestroy=o;let d=n.styles;this.styles=l?Gv(l,d):d,this.styleUrls=(g=n.getExternalStyles)==null?void 0:g.call(n,l)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Pa=class extends Qo{contentAttr;hostAttr;constructor(e,r,n,o,i,s,a,c,l){let d=o+"-"+n.id;super(e,r,n,i,s,a,c,l,d),this.contentAttr=GS(d),this.hostAttr=WS(d)}applyToHost(e){this.applyStyles(),this.setAttribute(e,this.hostAttr,"")}createElement(e,r){let n=super.createElement(e,r);return super.setAttribute(n,this.contentAttr,""),n}};var La=class t extends $o{supportsDOMEvents=!0;static makeCurrent(){Wd(new t)}onAndCancel(e,r,n,o){return e.addEventListener(r,n,o),()=>{e.removeEventListener(r,n,o)}}dispatchEvent(e,r){e.dispatchEvent(r)}remove(e){e.remove()}createElement(e,r){return r=r||this.getDefaultDocument(),r.createElement(e)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(e){return e.nodeType===Node.ELEMENT_NODE}isShadowRoot(e){return e instanceof DocumentFragment}getGlobalEventTarget(e,r){return r==="window"?window:r==="document"?e:r==="body"?e.body:null}getBaseHref(e){let r=qS();return r==null?null:ZS(r)}resetBaseElement(){Ko=null}getUserAgent(){return window.navigator.userAgent}getCookie(e){return qo(document.cookie,e)}},Ko=null;function qS(){return Ko=Ko||document.querySelector("base"),Ko?Ko.getAttribute("href"):null}function ZS(t){return new URL(t,document.baseURI).pathname}var Va=class{addToWindow(e){Fe.getAngularTestability=(n,o=!0)=>{let i=e.findTestabilityInTree(n,o);if(i==null)throw new R(5103,!1);return i},Fe.getAllAngularTestabilities=()=>e.getAllTestabilities(),Fe.getAllAngularRootElements=()=>e.getAllRootElements();let r=n=>{let o=Fe.getAllAngularTestabilities(),i=o.length,s=function(){i--,i==0&&n()};o.forEach(a=>{a.whenStable(s)})};Fe.frameworkStabilizers||(Fe.frameworkStabilizers=[]),Fe.frameworkStabilizers.push(r)}findTestabilityInTree(e,r,n){if(r==null)return null;let o=e.getTestability(r);return o!=null?o:n?Ge().isShadowRoot(r)?this.findTestabilityInTree(e,r.host,!0):this.findTestabilityInTree(e,r.parentElement,!0):null}},YS=(()=>{let e=class e{build(){return new XMLHttpRequest}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),qv=(()=>{let e=class e extends Zo{constructor(n){super(n)}supports(n){return!0}addEventListener(n,o,i,s){return n.addEventListener(o,i,s),()=>this.removeEventListener(n,o,i,s)}removeEventListener(n,o,i,s){return n.removeEventListener(o,i,s)}};u(e,"\u0275fac",function(o){return new(o||e)(k(ce))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),Wv=["alt","control","meta","shift"],QS={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},KS={alt:t=>t.altKey,control:t=>t.ctrlKey,meta:t=>t.metaKey,shift:t=>t.shiftKey},Zv=(()=>{let e=class e extends Zo{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,o,i,s){let a=e.parseEventName(o),c=e.eventCallback(a.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Ge().onAndCancel(n,a.domEventName,c,s))}static parseEventName(n){let o=n.toLowerCase().split("."),i=o.shift();if(o.length===0||!(i==="keydown"||i==="keyup"))return null;let s=e._normalizeKey(o.pop()),a="",c=o.indexOf("code");if(c>-1&&(o.splice(c,1),a="code."),Wv.forEach(d=>{let h=o.indexOf(d);h>-1&&(o.splice(h,1),a+=d+".")}),a+=s,o.length!=0||s.length===0)return null;let l={};return l.domEventName=i,l.fullKey=a,l}static matchEventFullKeyCode(n,o){let i=QS[n.key]||n.key,s="";return o.indexOf("code.")>-1&&(i=n.code,s="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),Wv.forEach(a=>{if(a!==i){let c=KS[a];c(n)&&(s+=a+".")}}),s+=i,s===o)}static eventCallback(n,o,i){return s=>{e.matchEventFullKeyCode(s,n)&&i.runGuarded(()=>o(s))}}static _normalizeKey(n){return n==="esc"?"escape":n}};u(e,"\u0275fac",function(o){return new(o||e)(k(ce))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();function XS(){La.makeCurrent()}function JS(){return new It}function eT(){return dm(document),document}var tT=[{provide:rn,useValue:Qd},{provide:pd,useValue:XS,multi:!0},{provide:ce,useFactory:eT}],nT=zd(xv,"browser",tT);var rT=[{provide:Vo,useClass:Va},{provide:Vd,useClass:ya,deps:[m,Da,Vo]},{provide:ya,useClass:ya,deps:[m,Da,Vo]}],oT=[{provide:ta,useValue:"root"},{provide:It,useFactory:JS},{provide:ja,useClass:qv,multi:!0,deps:[ce]},{provide:ja,useClass:Zv,multi:!0,deps:[ce]},sf,rf,nf,{provide:Tr,useExisting:sf},{provide:Un,useClass:YS},[]],iT=(()=>{let e=class e{constructor(){}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275mod",Re({type:e})),u(e,"\u0275inj",_e({providers:[...oT,...rT],imports:[Wo,Av]}));let t=e;return t})();var Br=class{},Xo=class{},ln=class t{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(e){e?typeof e=="string"?this.lazyInit=()=>{this.headers=new Map,e.split(`
`).forEach(r=>{let n=r.indexOf(":");if(n>0){let o=r.slice(0,n),i=r.slice(n+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&e instanceof Headers?(this.headers=new Map,e.forEach((r,n)=>{this.addHeaderEntry(n,r)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(e).forEach(([r,n])=>{this.setHeaderEntries(r,n)})}:this.headers=new Map}has(e){return this.init(),this.headers.has(e.toLowerCase())}get(e){this.init();let r=this.headers.get(e.toLowerCase());return r&&r.length>0?r[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(e){return this.init(),this.headers.get(e.toLowerCase())||null}append(e,r){return this.clone({name:e,value:r,op:"a"})}set(e,r){return this.clone({name:e,value:r,op:"s"})}delete(e,r){return this.clone({name:e,value:r,op:"d"})}maybeSetNormalizedName(e,r){this.normalizedNames.has(r)||this.normalizedNames.set(r,e)}init(){this.lazyInit&&(this.lazyInit instanceof t?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(e=>this.applyUpdate(e)),this.lazyUpdate=null))}copyFrom(e){e.init(),Array.from(e.headers.keys()).forEach(r=>{this.headers.set(r,e.headers.get(r)),this.normalizedNames.set(r,e.normalizedNames.get(r))})}clone(e){let r=new t;return r.lazyInit=this.lazyInit&&this.lazyInit instanceof t?this.lazyInit:this,r.lazyUpdate=(this.lazyUpdate||[]).concat([e]),r}applyUpdate(e){let r=e.name.toLowerCase();switch(e.op){case"a":case"s":let n=e.value;if(typeof n=="string"&&(n=[n]),n.length===0)return;this.maybeSetNormalizedName(e.name,r);let o=(e.op==="a"?this.headers.get(r):void 0)||[];o.push(...n),this.headers.set(r,o);break;case"d":let i=e.value;if(!i)this.headers.delete(r),this.normalizedNames.delete(r);else{let s=this.headers.get(r);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(r),this.normalizedNames.delete(r)):this.headers.set(r,s)}break}}addHeaderEntry(e,r){let n=e.toLowerCase();this.maybeSetNormalizedName(e,n),this.headers.has(n)?this.headers.get(n).push(r):this.headers.set(n,[r])}setHeaderEntries(e,r){let n=(Array.isArray(r)?r:[r]).map(i=>i.toString()),o=e.toLowerCase();this.headers.set(o,n),this.maybeSetNormalizedName(e,o)}forEach(e){this.init(),Array.from(this.normalizedNames.keys()).forEach(r=>e(this.normalizedNames.get(r),this.headers.get(r)))}};var Ua=class{encodeKey(e){return Yv(e)}encodeValue(e){return Yv(e)}decodeKey(e){return decodeURIComponent(e)}decodeValue(e){return decodeURIComponent(e)}};function sT(t,e){let r=new Map;return t.length>0&&t.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[e.decodeKey(o),""]:[e.decodeKey(o.slice(0,i)),e.decodeValue(o.slice(i+1))],c=r.get(s)||[];c.push(a),r.set(s,c)}),r}var aT=/%(\d[a-f0-9])/gi,cT={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Yv(t){return encodeURIComponent(t).replace(aT,(e,r)=>{var n;return(n=cT[r])!=null?n:e})}function Ba(t){return`${t}`}var zt=class t{map;encoder;updates=null;cloneFrom=null;constructor(e={}){if(this.encoder=e.encoder||new Ua,e.fromString){if(e.fromObject)throw new R(2805,!1);this.map=sT(e.fromString,this.encoder)}else e.fromObject?(this.map=new Map,Object.keys(e.fromObject).forEach(r=>{let n=e.fromObject[r],o=Array.isArray(n)?n.map(Ba):[Ba(n)];this.map.set(r,o)})):this.map=null}has(e){return this.init(),this.map.has(e)}get(e){this.init();let r=this.map.get(e);return r?r[0]:null}getAll(e){return this.init(),this.map.get(e)||null}keys(){return this.init(),Array.from(this.map.keys())}append(e,r){return this.clone({param:e,value:r,op:"a"})}appendAll(e){let r=[];return Object.keys(e).forEach(n=>{let o=e[n];Array.isArray(o)?o.forEach(i=>{r.push({param:n,value:i,op:"a"})}):r.push({param:n,value:o,op:"a"})}),this.clone(r)}set(e,r){return this.clone({param:e,value:r,op:"s"})}delete(e,r){return this.clone({param:e,value:r,op:"d"})}toString(){return this.init(),this.keys().map(e=>{let r=this.encoder.encodeKey(e);return this.map.get(e).map(n=>r+"="+this.encoder.encodeValue(n)).join("&")}).filter(e=>e!=="").join("&")}clone(e){let r=new t({encoder:this.encoder});return r.cloneFrom=this.cloneFrom||this,r.updates=(this.updates||[]).concat(e),r}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(e=>this.map.set(e,this.cloneFrom.map.get(e))),this.updates.forEach(e=>{switch(e.op){case"a":case"s":let r=(e.op==="a"?this.map.get(e.param):void 0)||[];r.push(Ba(e.value)),this.map.set(e.param,r);break;case"d":if(e.value!==void 0){let n=this.map.get(e.param)||[],o=n.indexOf(Ba(e.value));o!==-1&&n.splice(o,1),n.length>0?this.map.set(e.param,n):this.map.delete(e.param)}else{this.map.delete(e.param);break}}}),this.cloneFrom=this.updates=null)}};var Ha=class{map=new Map;set(e,r){return this.map.set(e,r),this}get(e){return this.map.has(e)||this.map.set(e,e.defaultValue()),this.map.get(e)}delete(e){return this.map.delete(e),this}has(e){return this.map.has(e)}keys(){return this.map.keys()}};function lT(t){switch(t){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Qv(t){return typeof ArrayBuffer<"u"&&t instanceof ArrayBuffer}function Kv(t){return typeof Blob<"u"&&t instanceof Blob}function Xv(t){return typeof FormData<"u"&&t instanceof FormData}function uT(t){return typeof URLSearchParams<"u"&&t instanceof URLSearchParams}var Jv="Content-Type",ey="Accept",ny="X-Request-URL",ry="text/plain",oy="application/json",dT=`${oy}, ${ry}, */*`,Vr=class t{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(e,r,n,o){var s,a;this.url=r,this.method=e.toUpperCase();let i;if(lT(this.method)||o?(this.body=n!==void 0?n:null,i=o):i=n,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),(s=this.headers)!=null||(this.headers=new ln),(a=this.context)!=null||(this.context=new Ha),!this.params)this.params=new zt,this.urlWithParams=r;else{let c=this.params.toString();if(c.length===0)this.urlWithParams=r;else{let l=r.indexOf("?"),d=l===-1?"?":l<r.length-1?"&":"";this.urlWithParams=r+d+c}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Qv(this.body)||Kv(this.body)||Xv(this.body)||uT(this.body)?this.body:this.body instanceof zt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||Xv(this.body)?null:Kv(this.body)?this.body.type||null:Qv(this.body)?null:typeof this.body=="string"?ry:this.body instanceof zt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?oy:null}clone(e={}){var g,p,v,y;let r=e.method||this.method,n=e.url||this.url,o=e.responseType||this.responseType,i=(g=e.transferCache)!=null?g:this.transferCache,s=e.body!==void 0?e.body:this.body,a=(p=e.withCredentials)!=null?p:this.withCredentials,c=(v=e.reportProgress)!=null?v:this.reportProgress,l=e.headers||this.headers,d=e.params||this.params,h=(y=e.context)!=null?y:this.context;return e.setHeaders!==void 0&&(l=Object.keys(e.setHeaders).reduce((x,O)=>x.set(O,e.setHeaders[O]),l)),e.setParams&&(d=Object.keys(e.setParams).reduce((x,O)=>x.set(O,e.setParams[O]),d)),new t(r,n,s,{params:d,headers:l,context:h,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},Hn=function(t){return t[t.Sent=0]="Sent",t[t.UploadProgress=1]="UploadProgress",t[t.ResponseHeader=2]="ResponseHeader",t[t.DownloadProgress=3]="DownloadProgress",t[t.Response=4]="Response",t[t.User=5]="User",t}(Hn||{}),Ur=class{headers;status;statusText;url;ok;type;constructor(e,r=200,n="OK"){this.headers=e.headers||new ln,this.status=e.status!==void 0?e.status:r,this.statusText=e.statusText||n,this.url=e.url||null,this.ok=this.status>=200&&this.status<300}},$a=class t extends Ur{constructor(e={}){super(e)}type=Hn.ResponseHeader;clone(e={}){return new t({headers:e.headers||this.headers,status:e.status!==void 0?e.status:this.status,statusText:e.statusText||this.statusText,url:e.url||this.url||void 0})}},Jo=class t extends Ur{body;constructor(e={}){super(e),this.body=e.body!==void 0?e.body:null}type=Hn.Response;clone(e={}){return new t({body:e.body!==void 0?e.body:this.body,headers:e.headers||this.headers,status:e.status!==void 0?e.status:this.status,statusText:e.statusText||this.statusText,url:e.url||this.url||void 0})}},ei=class extends Ur{name="HttpErrorResponse";message;error;ok=!1;constructor(e){super(e,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${e.url||"(unknown url)"}`:this.message=`Http failure response for ${e.url||"(unknown url)"}: ${e.status} ${e.statusText}`,this.error=e.error||null}},fT=200,hT=204;function af(t,e){return{body:e,headers:t.headers,context:t.context,observe:t.observe,params:t.params,reportProgress:t.reportProgress,responseType:t.responseType,withCredentials:t.withCredentials,transferCache:t.transferCache}}var iy=(()=>{let e=class e{handler;constructor(n){this.handler=n}request(n,o,i={}){let s;if(n instanceof Vr)s=n;else{let l;i.headers instanceof ln?l=i.headers:l=new ln(i.headers);let d;i.params&&(i.params instanceof zt?d=i.params:d=new zt({fromObject:i.params})),s=new Vr(n,o,i.body!==void 0?i.body:null,{headers:l,context:i.context,params:d,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials,transferCache:i.transferCache})}let a=F(s).pipe(gt(l=>this.handler.handle(l)));if(n instanceof Vr||i.observe==="events")return a;let c=a.pipe(ye(l=>l instanceof Jo));switch(i.observe||"body"){case"body":switch(s.responseType){case"arraybuffer":return c.pipe($(l=>{if(l.body!==null&&!(l.body instanceof ArrayBuffer))throw new R(2806,!1);return l.body}));case"blob":return c.pipe($(l=>{if(l.body!==null&&!(l.body instanceof Blob))throw new R(2807,!1);return l.body}));case"text":return c.pipe($(l=>{if(l.body!==null&&typeof l.body!="string")throw new R(2808,!1);return l.body}));case"json":default:return c.pipe($(l=>l.body))}case"response":return c;default:throw new R(2809,!1)}}delete(n,o={}){return this.request("DELETE",n,o)}get(n,o={}){return this.request("GET",n,o)}head(n,o={}){return this.request("HEAD",n,o)}jsonp(n,o){return this.request("JSONP",n,{params:new zt().append(o,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,o={}){return this.request("OPTIONS",n,o)}patch(n,o,i={}){return this.request("PATCH",n,af(i,o))}post(n,o,i={}){return this.request("POST",n,af(i,o))}put(n,o,i={}){return this.request("PUT",n,af(i,o))}};u(e,"\u0275fac",function(o){return new(o||e)(k(Br))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();var pT=new N("");function sy(t,e){return e(t)}function gT(t,e){return(r,n)=>e.intercept(r,{handle:o=>t(o,n)})}function mT(t,e,r){return(n,o)=>je(r,()=>e(n,i=>t(i,o)))}var ay=new N(""),lf=new N(""),cy=new N(""),uf=new N("",{providedIn:"root",factory:()=>!0});function vT(){let t=null;return(e,r)=>{var i;t===null&&(t=((i=I(ay,{optional:!0}))!=null?i:[]).reduceRight(gT,sy));let n=I(Ut);if(I(uf)){let s=n.add();return t(e,r).pipe(Xt(()=>n.remove(s)))}else return t(e,r)}}var za=(()=>{let e=class e extends Br{backend;injector;chain=null;pendingTasks=I(Ut);contributeToStability=I(uf);constructor(n,o){super(),this.backend=n,this.injector=o}handle(n){if(this.chain===null){let o=Array.from(new Set([...this.injector.get(lf),...this.injector.get(cy,[])]));this.chain=o.reduceRight((i,s)=>mT(i,s,this.injector),sy)}if(this.contributeToStability){let o=this.pendingTasks.add();return this.chain(n,i=>this.backend.handle(i)).pipe(Xt(()=>this.pendingTasks.remove(o)))}else return this.chain(n,o=>this.backend.handle(o))}};u(e,"\u0275fac",function(o){return new(o||e)(k(Xo),k(ae))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();var yT=/^\)\]\}',?\n/,DT=RegExp(`^${ny}:`,"m");function IT(t){return"responseURL"in t&&t.responseURL?t.responseURL:DT.test(t.getAllResponseHeaders())?t.getResponseHeader(ny):null}var cf=(()=>{let e=class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new R(-2800,!1);let o=this.xhrFactory;return(o.\u0275loadImpl?oe(o.\u0275loadImpl()):F(null)).pipe(De(()=>new q(s=>{let a=o.build();if(a.open(n.method,n.urlWithParams),n.withCredentials&&(a.withCredentials=!0),n.headers.forEach((x,O)=>a.setRequestHeader(x,O.join(","))),n.headers.has(ey)||a.setRequestHeader(ey,dT),!n.headers.has(Jv)){let x=n.detectContentTypeHeader();x!==null&&a.setRequestHeader(Jv,x)}if(n.responseType){let x=n.responseType.toLowerCase();a.responseType=x!=="json"?x:"text"}let c=n.serializeBody(),l=null,d=()=>{if(l!==null)return l;let x=a.statusText||"OK",O=new ln(a.getAllResponseHeaders()),X=IT(a)||n.url;return l=new $a({headers:O,status:a.status,statusText:x,url:X}),l},h=()=>{let{headers:x,status:O,statusText:X,url:Ue}=d(),he=null;O!==hT&&(he=typeof a.response>"u"?a.responseText:a.response),O===0&&(O=he?fT:0);let Ot=O>=200&&O<300;if(n.responseType==="json"&&typeof he=="string"){let Ni=he;he=he.replace(yT,"");try{he=he!==""?JSON.parse(he):null}catch(Kn){he=Ni,Ot&&(Ot=!1,he={error:Kn,text:he})}}Ot?(s.next(new Jo({body:he,headers:x,status:O,statusText:X,url:Ue||void 0})),s.complete()):s.error(new ei({error:he,headers:x,status:O,statusText:X,url:Ue||void 0}))},g=x=>{let{url:O}=d(),X=new ei({error:x,status:a.status||0,statusText:a.statusText||"Unknown Error",url:O||void 0});s.error(X)},p=!1,v=x=>{p||(s.next(d()),p=!0);let O={type:Hn.DownloadProgress,loaded:x.loaded};x.lengthComputable&&(O.total=x.total),n.responseType==="text"&&a.responseText&&(O.partialText=a.responseText),s.next(O)},y=x=>{let O={type:Hn.UploadProgress,loaded:x.loaded};x.lengthComputable&&(O.total=x.total),s.next(O)};return a.addEventListener("load",h),a.addEventListener("error",g),a.addEventListener("timeout",g),a.addEventListener("abort",g),n.reportProgress&&(a.addEventListener("progress",v),c!==null&&a.upload&&a.upload.addEventListener("progress",y)),a.send(c),s.next({type:Hn.Sent}),()=>{a.removeEventListener("error",g),a.removeEventListener("abort",g),a.removeEventListener("load",h),a.removeEventListener("timeout",g),n.reportProgress&&(a.removeEventListener("progress",v),c!==null&&a.upload&&a.upload.removeEventListener("progress",y)),a.readyState!==a.DONE&&a.abort()}})))}};u(e,"\u0275fac",function(o){return new(o||e)(k(Un))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),ly=new N(""),CT="XSRF-TOKEN",bT=new N("",{providedIn:"root",factory:()=>CT}),ET="X-XSRF-TOKEN",wT=new N("",{providedIn:"root",factory:()=>ET}),ti=class{},MT=(()=>{let e=class e{doc;platform;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,o,i){this.doc=n,this.platform=o,this.cookieName=i}getToken(){if(this.platform==="server")return null;let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=qo(n,this.cookieName),this.lastCookieString=n),this.lastToken}};u(e,"\u0275fac",function(o){return new(o||e)(k(ce),k(rn),k(bT))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();function ST(t,e){let r=t.url.toLowerCase();if(!I(ly)||t.method==="GET"||t.method==="HEAD"||r.startsWith("http://")||r.startsWith("https://"))return e(t);let n=I(ti).getToken(),o=I(wT);return n!=null&&!t.headers.has(o)&&(t=t.clone({headers:t.headers.set(o,n)})),e(t)}var df=function(t){return t[t.Interceptors=0]="Interceptors",t[t.LegacyInterceptors=1]="LegacyInterceptors",t[t.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",t[t.NoXsrfProtection=3]="NoXsrfProtection",t[t.JsonpSupport=4]="JsonpSupport",t[t.RequestsMadeViaParent=5]="RequestsMadeViaParent",t[t.Fetch=6]="Fetch",t}(df||{});function TT(t,e){return{\u0275kind:t,\u0275providers:e}}function uy(...t){let e=[iy,cf,za,{provide:Br,useExisting:za},{provide:Xo,useFactory:()=>{var r;return(r=I(pT,{optional:!0}))!=null?r:I(cf)}},{provide:lf,useValue:ST,multi:!0},{provide:ly,useValue:!0},{provide:ti,useClass:MT}];for(let r of t)e.push(...r.\u0275providers);return ea(e)}var ty=new N("");function dy(){return TT(df.LegacyInterceptors,[{provide:ty,useFactory:vT},{provide:lf,useExisting:ty,multi:!0}])}var _T=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275mod",Re({type:e})),u(e,"\u0275inj",_e({providers:[uy(dy())]}));let t=e;return t})();var fy=(()=>{let e=class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}};u(e,"\u0275fac",function(o){return new(o||e)(k(ce))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();var j="primary",pi=Symbol("RouteTitle"),mf=class{params;constructor(e){this.params=e||{}}has(e){return Object.prototype.hasOwnProperty.call(this.params,e)}get(e){if(this.has(e)){let r=this.params[e];return Array.isArray(r)?r[0]:r}return null}getAll(e){if(this.has(e)){let r=this.params[e];return Array.isArray(r)?r:[r]}return[]}get keys(){return Object.keys(this.params)}};function Gn(t){return new mf(t)}function Iy(t,e,r){let n=r.path.split("/");if(n.length>t.length||r.pathMatch==="full"&&(e.hasChildren()||n.length<t.length))return null;let o={};for(let i=0;i<n.length;i++){let s=n[i],a=t[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:t.slice(0,n.length),posParams:o}}function AT(t,e){if(t.length!==e.length)return!1;for(let r=0;r<t.length;++r)if(!Tt(t[r],e[r]))return!1;return!0}function Tt(t,e){let r=t?vf(t):void 0,n=e?vf(e):void 0;if(!r||!n||r.length!=n.length)return!1;let o;for(let i=0;i<r.length;i++)if(o=r[i],!Cy(t[o],e[o]))return!1;return!0}function vf(t){return[...Object.keys(t),...Object.getOwnPropertySymbols(t)]}function Cy(t,e){if(Array.isArray(t)&&Array.isArray(e)){if(t.length!==e.length)return!1;let r=[...t].sort(),n=[...e].sort();return r.every((o,i)=>n[i]===o)}else return t===e}function by(t){return t.length>0?t[t.length-1]:null}function hn(t){return ll(t)?t:Bn(t)?oe(Promise.resolve(t)):F(t)}var RT={exact:wy,subset:My},Ey={exact:NT,subset:OT,ignored:()=>!0};function hy(t,e,r){return RT[r.paths](t.root,e.root,r.matrixParams)&&Ey[r.queryParams](t.queryParams,e.queryParams)&&!(r.fragment==="exact"&&t.fragment!==e.fragment)}function NT(t,e){return Tt(t,e)}function wy(t,e,r){if(!$n(t.segments,e.segments)||!qa(t.segments,e.segments,r)||t.numberOfChildren!==e.numberOfChildren)return!1;for(let n in e.children)if(!t.children[n]||!wy(t.children[n],e.children[n],r))return!1;return!0}function OT(t,e){return Object.keys(e).length<=Object.keys(t).length&&Object.keys(e).every(r=>Cy(t[r],e[r]))}function My(t,e,r){return Sy(t,e,e.segments,r)}function Sy(t,e,r,n){if(t.segments.length>r.length){let o=t.segments.slice(0,r.length);return!(!$n(o,r)||e.hasChildren()||!qa(o,r,n))}else if(t.segments.length===r.length){if(!$n(t.segments,r)||!qa(t.segments,r,n))return!1;for(let o in e.children)if(!t.children[o]||!My(t.children[o],e.children[o],n))return!1;return!0}else{let o=r.slice(0,t.segments.length),i=r.slice(t.segments.length);return!$n(t.segments,o)||!qa(t.segments,o,n)||!t.children[j]?!1:Sy(t.children[j],e,i,n)}}function qa(t,e,r){return e.every((n,o)=>Ey[r](t[o].parameters,n.parameters))}var xt=class{root;queryParams;fragment;_queryParamMap;constructor(e=new K([],{}),r={},n=null){this.root=e,this.queryParams=r,this.fragment=n}get queryParamMap(){var e;return(e=this._queryParamMap)!=null||(this._queryParamMap=Gn(this.queryParams)),this._queryParamMap}toString(){return PT.serialize(this)}},K=class{segments;children;parent=null;constructor(e,r){this.segments=e,this.children=r,Object.values(r).forEach(n=>n.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Za(this)}},un=class{path;parameters;_parameterMap;constructor(e,r){this.path=e,this.parameters=r}get parameterMap(){var e;return(e=this._parameterMap)!=null||(this._parameterMap=Gn(this.parameters)),this._parameterMap}toString(){return _y(this)}};function kT(t,e){return $n(t,e)&&t.every((r,n)=>Tt(r.parameters,e[n].parameters))}function $n(t,e){return t.length!==e.length?!1:t.every((r,n)=>r.path===e[n].path)}function FT(t,e){let r=[];return Object.entries(t.children).forEach(([n,o])=>{n===j&&(r=r.concat(e(o,n)))}),Object.entries(t.children).forEach(([n,o])=>{n!==j&&(r=r.concat(e(o,n)))}),r}var Gt=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>new dn,providedIn:"root"}));let t=e;return t})(),dn=class{parse(e){let r=new Df(e);return new xt(r.parseRootSegment(),r.parseQueryParams(),r.parseFragment())}serialize(e){let r=`/${ni(e.root,!0)}`,n=VT(e.queryParams),o=typeof e.fragment=="string"?`#${jT(e.fragment)}`:"";return`${r}${n}${o}`}},PT=new dn;function Za(t){return t.segments.map(e=>_y(e)).join("/")}function ni(t,e){if(!t.hasChildren())return Za(t);if(e){let r=t.children[j]?ni(t.children[j],!1):"",n=[];return Object.entries(t.children).forEach(([o,i])=>{o!==j&&n.push(`${o}:${ni(i,!1)}`)}),n.length>0?`${r}(${n.join("//")})`:r}else{let r=FT(t,(n,o)=>o===j?[ni(t.children[j],!1)]:[`${o}:${ni(n,!1)}`]);return Object.keys(t.children).length===1&&t.children[j]!=null?`${Za(t)}/${r[0]}`:`${Za(t)}/(${r.join("//")})`}}function Ty(t){return encodeURIComponent(t).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Ga(t){return Ty(t).replace(/%3B/gi,";")}function jT(t){return encodeURI(t)}function yf(t){return Ty(t).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Ya(t){return decodeURIComponent(t)}function py(t){return Ya(t.replace(/\+/g,"%20"))}function _y(t){return`${yf(t.path)}${LT(t.parameters)}`}function LT(t){return Object.entries(t).map(([e,r])=>`;${yf(e)}=${yf(r)}`).join("")}function VT(t){let e=Object.entries(t).map(([r,n])=>Array.isArray(n)?n.map(o=>`${Ga(r)}=${Ga(o)}`).join("&"):`${Ga(r)}=${Ga(n)}`).filter(r=>r);return e.length?`?${e.join("&")}`:""}var BT=/^[^\/()?;#]+/;function ff(t){let e=t.match(BT);return e?e[0]:""}var UT=/^[^\/()?;=#]+/;function HT(t){let e=t.match(UT);return e?e[0]:""}var $T=/^[^=?&#]+/;function zT(t){let e=t.match($T);return e?e[0]:""}var GT=/^[^&#]+/;function WT(t){let e=t.match(GT);return e?e[0]:""}var Df=class{url;remaining;constructor(e){this.url=e,this.remaining=e}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new K([],{}):new K([],this.parseChildren())}parseQueryParams(){let e={};if(this.consumeOptional("?"))do this.parseQueryParam(e);while(this.consumeOptional("&"));return e}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let e=[];for(this.peekStartsWith("(")||e.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),e.push(this.parseSegment());let r={};this.peekStartsWith("/(")&&(this.capture("/"),r=this.parseParens(!0));let n={};return this.peekStartsWith("(")&&(n=this.parseParens(!1)),(e.length>0||Object.keys(r).length>0)&&(n[j]=new K(e,r)),n}parseSegment(){let e=ff(this.remaining);if(e===""&&this.peekStartsWith(";"))throw new R(4009,!1);return this.capture(e),new un(Ya(e),this.parseMatrixParams())}parseMatrixParams(){let e={};for(;this.consumeOptional(";");)this.parseParam(e);return e}parseParam(e){let r=HT(this.remaining);if(!r)return;this.capture(r);let n="";if(this.consumeOptional("=")){let o=ff(this.remaining);o&&(n=o,this.capture(n))}e[Ya(r)]=Ya(n)}parseQueryParam(e){let r=zT(this.remaining);if(!r)return;this.capture(r);let n="";if(this.consumeOptional("=")){let s=WT(this.remaining);s&&(n=s,this.capture(n))}let o=py(r),i=py(n);if(e.hasOwnProperty(o)){let s=e[o];Array.isArray(s)||(s=[s],e[o]=s),s.push(i)}else e[o]=i}parseParens(e){let r={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let n=ff(this.remaining),o=this.remaining[n.length];if(o!=="/"&&o!==")"&&o!==";")throw new R(4010,!1);let i;n.indexOf(":")>-1?(i=n.slice(0,n.indexOf(":")),this.capture(i),this.capture(":")):e&&(i=j);let s=this.parseChildren();r[i]=Object.keys(s).length===1?s[j]:new K([],s),this.consumeOptional("//")}return r}peekStartsWith(e){return this.remaining.startsWith(e)}consumeOptional(e){return this.peekStartsWith(e)?(this.remaining=this.remaining.substring(e.length),!0):!1}capture(e){if(!this.consumeOptional(e))throw new R(4011,!1)}};function xy(t){return t.segments.length>0?new K([],{[j]:t}):t}function Ay(t){let e={};for(let[n,o]of Object.entries(t.children)){let i=Ay(o);if(n===j&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))e[s]=a;else(i.segments.length>0||i.hasChildren())&&(e[n]=i)}let r=new K(t.segments,e);return qT(r)}function qT(t){if(t.numberOfChildren===1&&t.children[j]){let e=t.children[j];return new K(t.segments.concat(e.segments),e.children)}return t}function fn(t){return t instanceof xt}function Ry(t,e,r=null,n=null){let o=Ny(t);return Oy(o,e,r,n)}function Ny(t){let e;function r(i){let s={};for(let c of i.children){let l=r(c);s[c.outlet]=l}let a=new K(i.url,s);return i===t&&(e=a),a}let n=r(t.root),o=xy(n);return e!=null?e:o}function Oy(t,e,r,n){let o=t;for(;o.parent;)o=o.parent;if(e.length===0)return hf(o,o,o,r,n);let i=ZT(e);if(i.toRoot())return hf(o,o,new K([],{}),r,n);let s=YT(i,o,t),a=s.processChildren?oi(s.segmentGroup,s.index,i.commands):Fy(s.segmentGroup,s.index,i.commands);return hf(o,s.segmentGroup,a,r,n)}function Ka(t){return typeof t=="object"&&t!=null&&!t.outlets&&!t.segmentPath}function si(t){return typeof t=="object"&&t!=null&&t.outlets}function hf(t,e,r,n,o){let i={};n&&Object.entries(n).forEach(([c,l])=>{i[c]=Array.isArray(l)?l.map(d=>`${d}`):`${l}`});let s;t===e?s=r:s=ky(t,e,r);let a=xy(Ay(s));return new xt(a,i,o)}function ky(t,e,r){let n={};return Object.entries(t.children).forEach(([o,i])=>{i===e?n[o]=r:n[o]=ky(i,e,r)}),new K(t.segments,n)}var Xa=class{isAbsolute;numberOfDoubleDots;commands;constructor(e,r,n){if(this.isAbsolute=e,this.numberOfDoubleDots=r,this.commands=n,e&&n.length>0&&Ka(n[0]))throw new R(4003,!1);let o=n.find(si);if(o&&o!==by(n))throw new R(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function ZT(t){if(typeof t[0]=="string"&&t.length===1&&t[0]==="/")return new Xa(!0,0,t);let e=0,r=!1,n=t.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,l])=>{a[c]=typeof l=="string"?l.split("/"):l}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?r=!0:a===".."?e++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new Xa(r,e,n)}var zr=class{segmentGroup;processChildren;index;constructor(e,r,n){this.segmentGroup=e,this.processChildren=r,this.index=n}};function YT(t,e,r){if(t.isAbsolute)return new zr(e,!0,0);if(!r)return new zr(e,!1,NaN);if(r.parent===null)return new zr(r,!0,0);let n=Ka(t.commands[0])?0:1,o=r.segments.length-1+n;return QT(r,o,t.numberOfDoubleDots)}function QT(t,e,r){let n=t,o=e,i=r;for(;i>o;){if(i-=o,n=n.parent,!n)throw new R(4005,!1);o=n.segments.length}return new zr(n,!1,o-i)}function KT(t){return si(t[0])?t[0].outlets:{[j]:t}}function Fy(t,e,r){if(t!=null||(t=new K([],{})),t.segments.length===0&&t.hasChildren())return oi(t,e,r);let n=XT(t,e,r),o=r.slice(n.commandIndex);if(n.match&&n.pathIndex<t.segments.length){let i=new K(t.segments.slice(0,n.pathIndex),{});return i.children[j]=new K(t.segments.slice(n.pathIndex),t.children),oi(i,0,o)}else return n.match&&o.length===0?new K(t.segments,{}):n.match&&!t.hasChildren()?If(t,e,r):n.match?oi(t,0,o):If(t,e,r)}function oi(t,e,r){if(r.length===0)return new K(t.segments,{});{let n=KT(r),o={};if(Object.keys(n).some(i=>i!==j)&&t.children[j]&&t.numberOfChildren===1&&t.children[j].segments.length===0){let i=oi(t.children[j],e,r);return new K(t.segments,i.children)}return Object.entries(n).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Fy(t.children[i],e,s))}),Object.entries(t.children).forEach(([i,s])=>{n[i]===void 0&&(o[i]=s)}),new K(t.segments,o)}}function XT(t,e,r){let n=0,o=e,i={match:!1,pathIndex:0,commandIndex:0};for(;o<t.segments.length;){if(n>=r.length)return i;let s=t.segments[o],a=r[n];if(si(a))break;let c=`${a}`,l=n<r.length-1?r[n+1]:null;if(o>0&&c===void 0)break;if(c&&l&&typeof l=="object"&&l.outlets===void 0){if(!my(c,l,s))return i;n+=2}else{if(!my(c,{},s))return i;n++}o++}return{match:!0,pathIndex:o,commandIndex:n}}function If(t,e,r){let n=t.segments.slice(0,e),o=0;for(;o<r.length;){let i=r[o];if(si(i)){let c=JT(i.outlets);return new K(n,c)}if(o===0&&Ka(r[0])){let c=t.segments[e];n.push(new un(c.path,gy(r[0]))),o++;continue}let s=si(i)?i.outlets[j]:`${i}`,a=o<r.length-1?r[o+1]:null;s&&a&&Ka(a)?(n.push(new un(s,gy(a))),o+=2):(n.push(new un(s,{})),o++)}return new K(n,{})}function JT(t){let e={};return Object.entries(t).forEach(([r,n])=>{typeof n=="string"&&(n=[n]),n!==null&&(e[r]=If(new K([],{}),0,n))}),e}function gy(t){let e={};return Object.entries(t).forEach(([r,n])=>e[r]=`${n}`),e}function my(t,e,r){return t==r.path&&Tt(e,r.parameters)}var Qa="imperative",me=function(t){return t[t.NavigationStart=0]="NavigationStart",t[t.NavigationEnd=1]="NavigationEnd",t[t.NavigationCancel=2]="NavigationCancel",t[t.NavigationError=3]="NavigationError",t[t.RoutesRecognized=4]="RoutesRecognized",t[t.ResolveStart=5]="ResolveStart",t[t.ResolveEnd=6]="ResolveEnd",t[t.GuardsCheckStart=7]="GuardsCheckStart",t[t.GuardsCheckEnd=8]="GuardsCheckEnd",t[t.RouteConfigLoadStart=9]="RouteConfigLoadStart",t[t.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",t[t.ChildActivationStart=11]="ChildActivationStart",t[t.ChildActivationEnd=12]="ChildActivationEnd",t[t.ActivationStart=13]="ActivationStart",t[t.ActivationEnd=14]="ActivationEnd",t[t.Scroll=15]="Scroll",t[t.NavigationSkipped=16]="NavigationSkipped",t}(me||{}),Ze=class{id;url;constructor(e,r){this.id=e,this.url=r}},At=class extends Ze{type=me.NavigationStart;navigationTrigger;restoredState;constructor(e,r,n="imperative",o=null){super(e,r),this.navigationTrigger=n,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},nt=class extends Ze{urlAfterRedirects;type=me.NavigationEnd;constructor(e,r,n){super(e,r),this.urlAfterRedirects=n}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Be=function(t){return t[t.Redirect=0]="Redirect",t[t.SupersededByNewNavigation=1]="SupersededByNewNavigation",t[t.NoDataFromResolver=2]="NoDataFromResolver",t[t.GuardRejected=3]="GuardRejected",t}(Be||{}),Wr=function(t){return t[t.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",t[t.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",t}(Wr||{}),_t=class extends Ze{reason;code;type=me.NavigationCancel;constructor(e,r,n,o){super(e,r),this.reason=n,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Rt=class extends Ze{reason;code;type=me.NavigationSkipped;constructor(e,r,n,o){super(e,r),this.reason=n,this.code=o}},qr=class extends Ze{error;target;type=me.NavigationError;constructor(e,r,n,o){super(e,r),this.error=n,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},ai=class extends Ze{urlAfterRedirects;state;type=me.RoutesRecognized;constructor(e,r,n,o){super(e,r),this.urlAfterRedirects=n,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ja=class extends Ze{urlAfterRedirects;state;type=me.GuardsCheckStart;constructor(e,r,n,o){super(e,r),this.urlAfterRedirects=n,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ec=class extends Ze{urlAfterRedirects;state;shouldActivate;type=me.GuardsCheckEnd;constructor(e,r,n,o,i){super(e,r),this.urlAfterRedirects=n,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},tc=class extends Ze{urlAfterRedirects;state;type=me.ResolveStart;constructor(e,r,n,o){super(e,r),this.urlAfterRedirects=n,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},nc=class extends Ze{urlAfterRedirects;state;type=me.ResolveEnd;constructor(e,r,n,o){super(e,r),this.urlAfterRedirects=n,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},rc=class{route;type=me.RouteConfigLoadStart;constructor(e){this.route=e}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},oc=class{route;type=me.RouteConfigLoadEnd;constructor(e){this.route=e}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},ic=class{snapshot;type=me.ChildActivationStart;constructor(e){this.snapshot=e}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},sc=class{snapshot;type=me.ChildActivationEnd;constructor(e){this.snapshot=e}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ac=class{snapshot;type=me.ActivationStart;constructor(e){this.snapshot=e}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},cc=class{snapshot;type=me.ActivationEnd;constructor(e){this.snapshot=e}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Zr=class{routerEvent;position;anchor;type=me.Scroll;constructor(e,r,n){this.routerEvent=e,this.position=r,this.anchor=n}toString(){let e=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${e}')`}},ci=class{},Yr=class{url;navigationBehaviorOptions;constructor(e,r){this.url=e,this.navigationBehaviorOptions=r}};function e_(t,e){var r;return t.providers&&!t._injector&&(t._injector=jo(t.providers,e,`Route: ${t.path}`)),(r=t._injector)!=null?r:e}function ht(t){return t.outlet||j}function t_(t,e){let r=t.filter(n=>ht(n)===e);return r.push(...t.filter(n=>ht(n)!==e)),r}function gi(t){var e;if(!t)return null;if((e=t.routeConfig)!=null&&e._injector)return t.routeConfig._injector;for(let r=t.parent;r;r=r.parent){let n=r.routeConfig;if(n!=null&&n._loadedInjector)return n._loadedInjector;if(n!=null&&n._injector)return n._injector}return null}var lc=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){var e,r;return(r=gi((e=this.route)==null?void 0:e.snapshot))!=null?r:this.rootInjector}constructor(e){this.rootInjector=e,this.children=new Nt(this.rootInjector)}},Nt=(()=>{let e=class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,o){let i=this.getOrCreateContext(n);i.outlet=o,this.contexts.set(n,i)}onChildOutletDestroyed(n){let o=this.getContext(n);o&&(o.outlet=null,o.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let o=this.getContext(n);return o||(o=new lc(this.rootInjector),this.contexts.set(n,o)),o}getContext(n){return this.contexts.get(n)||null}};u(e,"\u0275fac",function(o){return new(o||e)(k(ae))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),uc=class{_root;constructor(e){this._root=e}get root(){return this._root.value}parent(e){let r=this.pathFromRoot(e);return r.length>1?r[r.length-2]:null}children(e){let r=Cf(e,this._root);return r?r.children.map(n=>n.value):[]}firstChild(e){let r=Cf(e,this._root);return r&&r.children.length>0?r.children[0].value:null}siblings(e){let r=bf(e,this._root);return r.length<2?[]:r[r.length-2].children.map(o=>o.value).filter(o=>o!==e)}pathFromRoot(e){return bf(e,this._root).map(r=>r.value)}};function Cf(t,e){if(t===e.value)return e;for(let r of e.children){let n=Cf(t,r);if(n)return n}return null}function bf(t,e){if(t===e.value)return[e];for(let r of e.children){let n=bf(t,r);if(n.length)return n.unshift(e),n}return[]}var qe=class{value;children;constructor(e,r){this.value=e,this.children=r}toString(){return`TreeNode(${this.value})`}};function $r(t){let e={};return t&&t.children.forEach(r=>e[r.value.outlet]=r),e}var li=class extends uc{snapshot;constructor(e,r){super(e),this.snapshot=r,Af(this,e)}toString(){return this.snapshot.toString()}};function Py(t){let e=n_(t),r=new pe([new un("",{})]),n=new pe({}),o=new pe({}),i=new pe({}),s=new pe(""),a=new Ae(r,n,i,s,o,j,t,e.root);return a.snapshot=e.root,new li(new qe(a,[]),e)}function n_(t){let e={},r={},n={},o="",i=new zn([],e,n,o,r,j,t,null,{});return new ui("",new qe(i,[]))}var Ae=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(e,r,n,o,i,s,a,c){var l,d;this.urlSubject=e,this.paramsSubject=r,this.queryParamsSubject=n,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=(d=(l=this.dataSubject)==null?void 0:l.pipe($(h=>h[pi])))!=null?d:F(void 0),this.url=e,this.params=r,this.queryParams=n,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){var e;return(e=this._paramMap)!=null||(this._paramMap=this.params.pipe($(r=>Gn(r)))),this._paramMap}get queryParamMap(){var e;return(e=this._queryParamMap)!=null||(this._queryParamMap=this.queryParams.pipe($(r=>Gn(r)))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function dc(t,e,r="emptyOnly"){var i,s;let n,{routeConfig:o}=t;return e!==null&&(r==="always"||(o==null?void 0:o.path)===""||!e.component&&!((i=e.routeConfig)!=null&&i.loadComponent))?n={params:b(b({},e.params),t.params),data:b(b({},e.data),t.data),resolve:b(b(b(b({},t.data),e.data),o==null?void 0:o.data),t._resolvedData)}:n={params:b({},t.params),data:b({},t.data),resolve:b(b({},t.data),(s=t._resolvedData)!=null?s:{})},o&&Ly(o)&&(n.resolve[pi]=o.title),n}var zn=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){var e;return(e=this.data)==null?void 0:e[pi]}constructor(e,r,n,o,i,s,a,c,l){this.url=e,this.params=r,this.queryParams=n,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=l}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){var e;return(e=this._paramMap)!=null||(this._paramMap=Gn(this.params)),this._paramMap}get queryParamMap(){var e;return(e=this._queryParamMap)!=null||(this._queryParamMap=Gn(this.queryParams)),this._queryParamMap}toString(){let e=this.url.map(n=>n.toString()).join("/"),r=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${e}', path:'${r}')`}},ui=class extends uc{url;constructor(e,r){super(r),this.url=e,Af(this,r)}toString(){return jy(this._root)}};function Af(t,e){e.value._routerState=t,e.children.forEach(r=>Af(t,r))}function jy(t){let e=t.children.length>0?` { ${t.children.map(jy).join(", ")} } `:"";return`${t.value}${e}`}function pf(t){if(t.snapshot){let e=t.snapshot,r=t._futureSnapshot;t.snapshot=r,Tt(e.queryParams,r.queryParams)||t.queryParamsSubject.next(r.queryParams),e.fragment!==r.fragment&&t.fragmentSubject.next(r.fragment),Tt(e.params,r.params)||t.paramsSubject.next(r.params),AT(e.url,r.url)||t.urlSubject.next(r.url),Tt(e.data,r.data)||t.dataSubject.next(r.data)}else t.snapshot=t._futureSnapshot,t.dataSubject.next(t._futureSnapshot.data)}function Ef(t,e){let r=Tt(t.params,e.params)&&kT(t.url,e.url),n=!t.parent!=!e.parent;return r&&!n&&(!t.parent||Ef(t.parent,e.parent))}function Ly(t){return typeof t.title=="string"||t.title===null}var Vy=new N(""),Rf=(()=>{let e=class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=j;activateEvents=new se;deactivateEvents=new se;attachEvents=new se;detachEvents=new se;routerOutletData=rm(void 0);parentContexts=I(Nt);location=I(ze);changeDetector=I(C);inputBinder=I(mi,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:o,previousValue:i}=n.name;if(o)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){var n;this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),(n=this.inputBinder)==null||n.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){var o;return((o=this.parentContexts.getContext(n))==null?void 0:o.outlet)===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n!=null&&n.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new R(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new R(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new R(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,o){var i;this.activated=n,this._activatedRoute=o,this.location.insert(n.hostView),(i=this.inputBinder)==null||i.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,o){var d;if(this.isActivated)throw new R(4013,!1);this._activatedRoute=n;let i=this.location,a=n.snapshot.component,c=this.parentContexts.getOrCreateContext(this.name).children,l=new wf(n,c,i.injector,this.routerOutletData);this.activated=i.createComponent(a,{index:i.length,injector:l,environmentInjector:o}),this.changeDetector.markForCheck(),(d=this.inputBinder)==null||d.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275dir",U({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[tt]}));let t=e;return t})(),wf=class{route;childContexts;parent;outletData;constructor(e,r,n,o){this.route=e,this.childContexts=r,this.parent=n,this.outletData=o}get(e,r){return e===Ae?this.route:e===Nt?this.childContexts:e===Vy?this.outletData:this.parent.get(e,r)}},mi=new N(""),Nf=(()=>{let e=class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){var o;(o=this.outletDataSubscriptions.get(n))==null||o.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:o}=n,i=bn([o.queryParams,o.params,o.data]).pipe(De(([s,a,c],l)=>(c=b(b(b({},s),a),c),l===0?F(c):Promise.resolve(c)))).subscribe(s=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==o||o.component===null){this.unsubscribeFromRouteData(n);return}let a=Sa(o.component);if(!a){this.unsubscribeFromRouteData(n);return}for(let{templateName:c}of a.inputs)n.activatedComponentRef.setInput(c,s[c])});this.outletDataSubscriptions.set(n,i)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();function r_(t,e,r){let n=di(t,e._root,r?r._root:void 0);return new li(n,e)}function di(t,e,r){if(r&&t.shouldReuseRoute(e.value,r.value.snapshot)){let n=r.value;n._futureSnapshot=e.value;let o=o_(t,e,r);return new qe(n,o)}else{if(t.shouldAttach(e.value)){let i=t.retrieve(e.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=e.value,s.children=e.children.map(a=>di(t,a)),s}}let n=i_(e.value),o=e.children.map(i=>di(t,i));return new qe(n,o)}}function o_(t,e,r){return e.children.map(n=>{for(let o of r.children)if(t.shouldReuseRoute(n.value,o.value.snapshot))return di(t,n,o);return di(t,n)})}function i_(t){return new Ae(new pe(t.url),new pe(t.params),new pe(t.queryParams),new pe(t.fragment),new pe(t.data),t.outlet,t.component,t)}var Qr=class{redirectTo;navigationBehaviorOptions;constructor(e,r){this.redirectTo=e,this.navigationBehaviorOptions=r}},By="ngNavigationCancelingError";function fc(t,e){let{redirectTo:r,navigationBehaviorOptions:n}=fn(e)?{redirectTo:e,navigationBehaviorOptions:void 0}:e,o=Uy(!1,Be.Redirect);return o.url=r,o.navigationBehaviorOptions=n,o}function Uy(t,e){let r=new Error(`NavigationCancelingError: ${t||""}`);return r[By]=!0,r.cancellationCode=e,r}function s_(t){return Hy(t)&&fn(t.url)}function Hy(t){return!!t&&t[By]}var a_=(t,e,r,n)=>$(o=>(new Mf(e,o.targetRouterState,o.currentRouterState,r,n).activate(t),o)),Mf=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(e,r,n,o,i){this.routeReuseStrategy=e,this.futureState=r,this.currState=n,this.forwardEvent=o,this.inputBindingEnabled=i}activate(e){let r=this.futureState._root,n=this.currState?this.currState._root:null;this.deactivateChildRoutes(r,n,e),pf(this.futureState.root),this.activateChildRoutes(r,n,e)}deactivateChildRoutes(e,r,n){let o=$r(r);e.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],n),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,n)})}deactivateRoutes(e,r,n){let o=e.value,i=r?r.value:null;if(o===i)if(o.component){let s=n.getContext(o.outlet);s&&this.deactivateChildRoutes(e,r,s.children)}else this.deactivateChildRoutes(e,r,n);else i&&this.deactivateRouteAndItsChildren(r,n)}deactivateRouteAndItsChildren(e,r){e.value.component&&this.routeReuseStrategy.shouldDetach(e.value.snapshot)?this.detachAndStoreRouteSubtree(e,r):this.deactivateRouteAndOutlet(e,r)}detachAndStoreRouteSubtree(e,r){let n=r.getContext(e.value.outlet),o=n&&e.value.component?n.children:r,i=$r(e);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(n&&n.outlet){let s=n.outlet.detach(),a=n.children.onOutletDeactivated();this.routeReuseStrategy.store(e.value.snapshot,{componentRef:s,route:e,contexts:a})}}deactivateRouteAndOutlet(e,r){let n=r.getContext(e.value.outlet),o=n&&e.value.component?n.children:r,i=$r(e);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);n&&(n.outlet&&(n.outlet.deactivate(),n.children.onOutletDeactivated()),n.attachRef=null,n.route=null)}activateChildRoutes(e,r,n){let o=$r(r);e.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],n),this.forwardEvent(new cc(i.value.snapshot))}),e.children.length&&this.forwardEvent(new sc(e.value.snapshot))}activateRoutes(e,r,n){let o=e.value,i=r?r.value:null;if(pf(o),o===i)if(o.component){let s=n.getOrCreateContext(o.outlet);this.activateChildRoutes(e,r,s.children)}else this.activateChildRoutes(e,r,n);else if(o.component){let s=n.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),pf(a.route.value),this.activateChildRoutes(e,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(e,null,s.children)}else this.activateChildRoutes(e,null,n)}},hc=class{path;route;constructor(e){this.path=e,this.route=this.path[this.path.length-1]}},Gr=class{component;route;constructor(e,r){this.component=e,this.route=r}};function c_(t,e,r){let n=t._root,o=e?e._root:null;return ri(n,o,r,[n.value])}function l_(t){let e=t.routeConfig?t.routeConfig.canActivateChild:null;return!e||e.length===0?null:{node:t,guards:e}}function Xr(t,e){let r=Symbol(),n=e.get(t,r);return n===r?typeof t=="function"&&!Jp(t)?t:e.get(t):n}function ri(t,e,r,n,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=$r(e);return t.children.forEach(s=>{u_(s,i[s.value.outlet],r,n.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>ii(a,r.getContext(s),o)),o}function u_(t,e,r,n,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=t.value,s=e?e.value:null,a=r?r.getContext(t.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=d_(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new hc(n)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?ri(t,e,a?a.children:null,n,o):ri(t,e,r,n,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Gr(a.outlet.component,s))}else s&&ii(e,a,o),o.canActivateChecks.push(new hc(n)),i.component?ri(t,null,a?a.children:null,n,o):ri(t,null,r,n,o);return o}function d_(t,e,r){if(typeof r=="function")return r(t,e);switch(r){case"pathParamsChange":return!$n(t.url,e.url);case"pathParamsOrQueryParamsChange":return!$n(t.url,e.url)||!Tt(t.queryParams,e.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Ef(t,e)||!Tt(t.queryParams,e.queryParams);case"paramsChange":default:return!Ef(t,e)}}function ii(t,e,r){let n=$r(t),o=t.value;Object.entries(n).forEach(([i,s])=>{o.component?e?ii(s,e.children.getContext(i),r):ii(s,null,r):ii(s,e,r)}),o.component?e&&e.outlet&&e.outlet.isActivated?r.canDeactivateChecks.push(new Gr(e.outlet.component,o)):r.canDeactivateChecks.push(new Gr(null,o)):r.canDeactivateChecks.push(new Gr(null,o))}function vi(t){return typeof t=="function"}function f_(t){return typeof t=="boolean"}function h_(t){return t&&vi(t.canLoad)}function p_(t){return t&&vi(t.canActivate)}function g_(t){return t&&vi(t.canActivateChild)}function m_(t){return t&&vi(t.canDeactivate)}function v_(t){return t&&vi(t.canMatch)}function $y(t){return t instanceof rt||(t==null?void 0:t.name)==="EmptyError"}var Wa=Symbol("INITIAL_VALUE");function Kr(){return De(t=>bn(t.map(e=>e.pipe(kt(1),pl(Wa)))).pipe($(e=>{for(let r of e)if(r!==!0){if(r===Wa)return Wa;if(r===!1||y_(r))return r}return!0}),ye(e=>e!==Wa),kt(1)))}function y_(t){return fn(t)||t instanceof Qr}function D_(t,e){return le(r=>{let{targetSnapshot:n,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=r;return s.length===0&&i.length===0?F(L(b({},r),{guardsResult:!0})):I_(s,n,o,t).pipe(le(a=>a&&f_(a)?C_(n,i,t,e):F(a)),$(a=>L(b({},r),{guardsResult:a})))})}function I_(t,e,r,n){return oe(t).pipe(le(o=>S_(o.component,o.route,r,e,n)),Ft(o=>o!==!0,!0))}function C_(t,e,r,n){return oe(e).pipe(gt(o=>lr(E_(o.route.parent,n),b_(o.route,n),M_(t,o.path,r),w_(t,o.route,r))),Ft(o=>o!==!0,!0))}function b_(t,e){return t!==null&&e&&e(new ac(t)),F(!0)}function E_(t,e){return t!==null&&e&&e(new ic(t)),F(!0)}function w_(t,e,r){let n=e.routeConfig?e.routeConfig.canActivate:null;if(!n||n.length===0)return F(!0);let o=n.map(i=>is(()=>{var l;let s=(l=gi(e))!=null?l:r,a=Xr(i,s),c=p_(a)?a.canActivate(e,t):je(s,()=>a(e,t));return hn(c).pipe(Ft())}));return F(o).pipe(Kr())}function M_(t,e,r){let n=e[e.length-1],i=e.slice(0,e.length-1).reverse().map(s=>l_(s)).filter(s=>s!==null).map(s=>is(()=>{let a=s.guards.map(c=>{var g;let l=(g=gi(s.node))!=null?g:r,d=Xr(c,l),h=g_(d)?d.canActivateChild(n,t):je(l,()=>d(n,t));return hn(h).pipe(Ft())});return F(a).pipe(Kr())}));return F(i).pipe(Kr())}function S_(t,e,r,n,o){let i=e&&e.routeConfig?e.routeConfig.canDeactivate:null;if(!i||i.length===0)return F(!0);let s=i.map(a=>{var h;let c=(h=gi(e))!=null?h:o,l=Xr(a,c),d=m_(l)?l.canDeactivate(t,e,r,n):je(c,()=>l(t,e,r,n));return hn(d).pipe(Ft())});return F(s).pipe(Kr())}function T_(t,e,r,n){let o=e.canLoad;if(o===void 0||o.length===0)return F(!0);let i=o.map(s=>{let a=Xr(s,t),c=h_(a)?a.canLoad(e,r):je(t,()=>a(e,r));return hn(c)});return F(i).pipe(Kr(),zy(n))}function zy(t){return ol(Ee(e=>{if(typeof e!="boolean")throw fc(t,e)}),$(e=>e===!0))}function __(t,e,r,n){let o=e.canMatch;if(!o||o.length===0)return F(!0);let i=o.map(s=>{let a=Xr(s,t),c=v_(a)?a.canMatch(e,r):je(t,()=>a(e,r));return hn(c)});return F(i).pipe(Kr(),zy(n))}var fi=class{segmentGroup;constructor(e){this.segmentGroup=e||null}},hi=class extends Error{urlTree;constructor(e){super(),this.urlTree=e}};function Hr(t){return sr(new fi(t))}function x_(t){return sr(new R(4e3,!1))}function A_(t){return sr(Uy(!1,Be.GuardRejected))}var Sf=class{urlSerializer;urlTree;constructor(e,r){this.urlSerializer=e,this.urlTree=r}lineralizeSegments(e,r){let n=[],o=r.root;for(;;){if(n=n.concat(o.segments),o.numberOfChildren===0)return F(n);if(o.numberOfChildren>1||!o.children[j])return x_(`${e.redirectTo}`);o=o.children[j]}}applyRedirectCommands(e,r,n,o,i){if(typeof r!="string"){let a=r,{queryParams:c,fragment:l,routeConfig:d,url:h,outlet:g,params:p,data:v,title:y}=o,x=je(i,()=>a({params:p,data:v,queryParams:c,fragment:l,routeConfig:d,url:h,outlet:g,title:y}));if(x instanceof xt)throw new hi(x);r=x}let s=this.applyRedirectCreateUrlTree(r,this.urlSerializer.parse(r),e,n);if(r[0]==="/")throw new hi(s);return s}applyRedirectCreateUrlTree(e,r,n,o){let i=this.createSegmentGroup(e,r.root,n,o);return new xt(i,this.createQueryParams(r.queryParams,this.urlTree.queryParams),r.fragment)}createQueryParams(e,r){let n={};return Object.entries(e).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);n[o]=r[a]}else n[o]=i}),n}createSegmentGroup(e,r,n,o){let i=this.createSegments(e,r.segments,n,o),s={};return Object.entries(r.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(e,c,n,o)}),new K(i,s)}createSegments(e,r,n,o){return r.map(i=>i.path[0]===":"?this.findPosParam(e,i,o):this.findOrReturn(i,n))}findPosParam(e,r,n){let o=n[r.path.substring(1)];if(!o)throw new R(4001,!1);return o}findOrReturn(e,r){let n=0;for(let o of r){if(o.path===e.path)return r.splice(n),o;n++}return e}},Tf={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function R_(t,e,r,n,o){let i=Gy(t,e,r);return i.matched?(n=e_(e,n),__(n,e,r,o).pipe($(s=>s===!0?i:b({},Tf)))):F(i)}function Gy(t,e,r){var a,c;if(e.path==="**")return N_(r);if(e.path==="")return e.pathMatch==="full"&&(t.hasChildren()||r.length>0)?b({},Tf):{matched:!0,consumedSegments:[],remainingSegments:r,parameters:{},positionalParamSegments:{}};let o=(e.matcher||Iy)(r,t,e);if(!o)return b({},Tf);let i={};Object.entries((a=o.posParams)!=null?a:{}).forEach(([l,d])=>{i[l]=d.path});let s=o.consumed.length>0?b(b({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:r.slice(o.consumed.length),parameters:s,positionalParamSegments:(c=o.posParams)!=null?c:{}}}function N_(t){return{matched:!0,parameters:t.length>0?by(t).parameters:{},consumedSegments:t,remainingSegments:[],positionalParamSegments:{}}}function vy(t,e,r,n){return r.length>0&&F_(t,r,n)?{segmentGroup:new K(e,k_(n,new K(r,t.children))),slicedSegments:[]}:r.length===0&&P_(t,r,n)?{segmentGroup:new K(t.segments,O_(t,r,n,t.children)),slicedSegments:r}:{segmentGroup:new K(t.segments,t.children),slicedSegments:r}}function O_(t,e,r,n){let o={};for(let i of r)if(gc(t,e,i)&&!n[ht(i)]){let s=new K([],{});o[ht(i)]=s}return b(b({},n),o)}function k_(t,e){let r={};r[j]=e;for(let n of t)if(n.path===""&&ht(n)!==j){let o=new K([],{});r[ht(n)]=o}return r}function F_(t,e,r){return r.some(n=>gc(t,e,n)&&ht(n)!==j)}function P_(t,e,r){return r.some(n=>gc(t,e,n))}function gc(t,e,r){return(t.hasChildren()||e.length>0)&&r.pathMatch==="full"?!1:r.path===""}function j_(t,e,r){return e.length===0&&!t.children[r]}var _f=class{};function L_(t,e,r,n,o,i,s="emptyOnly"){return new xf(t,e,r,n,o,s,i).recognize()}var V_=31,xf=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(e,r,n,o,i,s,a){this.injector=e,this.configLoader=r,this.rootComponentType=n,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Sf(this.urlSerializer,this.urlTree)}noMatchError(e){return new R(4002,`'${e.segmentGroup}'`)}recognize(){let e=vy(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(e).pipe($(({children:r,rootSnapshot:n})=>{let o=new qe(n,r),i=new ui("",o),s=Ry(n,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(e){let r=new zn([],Object.freeze({}),Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),j,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,e,j,r).pipe($(n=>({children:n,rootSnapshot:r})),pt(n=>{if(n instanceof hi)return this.urlTree=n.urlTree,this.match(n.urlTree.root);throw n instanceof fi?this.noMatchError(n):n}))}processSegmentGroup(e,r,n,o,i){return n.segments.length===0&&n.hasChildren()?this.processChildren(e,r,n,i):this.processSegment(e,r,n,n.segments,o,!0,i).pipe($(s=>s instanceof qe?[s]:[]))}processChildren(e,r,n,o){let i=[];for(let s of Object.keys(n.children))s==="primary"?i.unshift(s):i.push(s);return oe(i).pipe(gt(s=>{let a=n.children[s],c=t_(r,s);return this.processSegmentGroup(e,c,a,s,o)}),hl((s,a)=>(s.push(...a),s)),Kt(null),fl(),le(s=>{if(s===null)return Hr(n);let a=Wy(s);return B_(a),F(a)}))}processSegment(e,r,n,o,i,s,a){return oe(r).pipe(gt(c=>{var l;return this.processSegmentAgainstRoute((l=c._injector)!=null?l:e,r,c,n,o,i,s,a).pipe(pt(d=>{if(d instanceof fi)return F(null);throw d}))}),Ft(c=>!!c),pt(c=>{if($y(c))return j_(n,o,i)?F(new _f):Hr(n);throw c}))}processSegmentAgainstRoute(e,r,n,o,i,s,a,c){return ht(n)!==s&&(s===j||!gc(o,i,n))?Hr(o):n.redirectTo===void 0?this.matchSegmentAgainstRoute(e,o,n,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(e,o,r,n,i,s,c):Hr(o)}expandSegmentAgainstRouteUsingRedirect(e,r,n,o,i,s,a){var x,O;let{matched:c,parameters:l,consumedSegments:d,positionalParamSegments:h,remainingSegments:g}=Gy(r,o,i);if(!c)return Hr(r);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>V_&&(this.allowRedirects=!1));let p=new zn(i,l,Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,yy(o),ht(o),(O=(x=o.component)!=null?x:o._loadedComponent)!=null?O:null,o,Dy(o)),v=dc(p,a,this.paramsInheritanceStrategy);p.params=Object.freeze(v.params),p.data=Object.freeze(v.data);let y=this.applyRedirects.applyRedirectCommands(d,o.redirectTo,h,p,e);return this.applyRedirects.lineralizeSegments(o,y).pipe(le(X=>this.processSegment(e,n,r,X.concat(g),s,!1,a)))}matchSegmentAgainstRoute(e,r,n,o,i,s){let a=R_(r,n,o,e,this.urlSerializer);return n.path==="**"&&(r.children={}),a.pipe(De(c=>{var l;return c.matched?(e=(l=n._injector)!=null?l:e,this.getChildConfig(e,n,o).pipe(De(({routes:d})=>{var he,Ot,Ni;let h=(he=n._loadedInjector)!=null?he:e,{parameters:g,consumedSegments:p,remainingSegments:v}=c,y=new zn(p,g,Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,yy(n),ht(n),(Ni=(Ot=n.component)!=null?Ot:n._loadedComponent)!=null?Ni:null,n,Dy(n)),x=dc(y,s,this.paramsInheritanceStrategy);y.params=Object.freeze(x.params),y.data=Object.freeze(x.data);let{segmentGroup:O,slicedSegments:X}=vy(r,p,v,d);if(X.length===0&&O.hasChildren())return this.processChildren(h,d,O,y).pipe($(Kn=>new qe(y,Kn)));if(d.length===0&&X.length===0)return F(new qe(y,[]));let Ue=ht(n)===i;return this.processSegment(h,d,O,X,Ue?j:i,!0,y).pipe($(Kn=>new qe(y,Kn instanceof qe?[Kn]:[])))}))):Hr(r)}))}getChildConfig(e,r,n){return r.children?F({routes:r.children,injector:e}):r.loadChildren?r._loadedRoutes!==void 0?F({routes:r._loadedRoutes,injector:r._loadedInjector}):T_(e,r,n,this.urlSerializer).pipe(le(o=>o?this.configLoader.loadChildren(e,r).pipe(Ee(i=>{r._loadedRoutes=i.routes,r._loadedInjector=i.injector})):A_(r))):F({routes:[],injector:e})}};function B_(t){t.sort((e,r)=>e.value.outlet===j?-1:r.value.outlet===j?1:e.value.outlet.localeCompare(r.value.outlet))}function U_(t){let e=t.value.routeConfig;return e&&e.path===""}function Wy(t){let e=[],r=new Set;for(let n of t){if(!U_(n)){e.push(n);continue}let o=e.find(i=>n.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...n.children),r.add(o)):e.push(n)}for(let n of r){let o=Wy(n.children);e.push(new qe(n.value,o))}return e.filter(n=>!r.has(n))}function yy(t){return t.data||{}}function Dy(t){return t.resolve||{}}function H_(t,e,r,n,o,i){return le(s=>L_(t,e,r,n,s.extractedUrl,o,i).pipe($(({state:a,tree:c})=>L(b({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function $_(t,e){return le(r=>{let{targetSnapshot:n,guards:{canActivateChecks:o}}=r;if(!o.length)return F(r);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let l of qy(c))s.add(l);let a=0;return oe(s).pipe(gt(c=>i.has(c)?z_(c,n,t,e):(c.data=dc(c,c.parent,t).resolve,F(void 0))),Ee(()=>a++),ur(1),le(c=>a===s.size?F(r):Oe))})}function qy(t){let e=t.children.map(r=>qy(r)).flat();return[t,...e]}function z_(t,e,r,n){let o=t.routeConfig,i=t._resolve;return(o==null?void 0:o.title)!==void 0&&!Ly(o)&&(i[pi]=o.title),G_(i,t,e,n).pipe($(s=>(t._resolvedData=s,t.data=dc(t,t.parent,r).resolve,null)))}function G_(t,e,r,n){let o=vf(t);if(o.length===0)return F({});let i={};return oe(o).pipe(le(s=>W_(t[s],e,r,n).pipe(Ft(),Ee(a=>{if(a instanceof Qr)throw fc(new dn,a);i[s]=a}))),ur(1),$(()=>i),pt(s=>$y(s)?Oe:sr(s)))}function W_(t,e,r,n){var a;let o=(a=gi(e))!=null?a:n,i=Xr(t,o),s=i.resolve?i.resolve(e,r):je(o,()=>i(e,r));return hn(s)}function gf(t){return De(e=>{let r=t(e);return r?oe(r).pipe($(()=>e)):F(e)})}var Of=(()=>{let e=class e{buildTitle(n){var s;let o,i=n.root;for(;i!==void 0;)o=(s=this.getResolvedTitleForRoute(i))!=null?s:o,i=i.children.find(a=>a.outlet===j);return o}getResolvedTitleForRoute(n){return n.data[pi]}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>I(Zy),providedIn:"root"}));let t=e;return t})(),Zy=(()=>{let e=class e extends Of{title;constructor(n){super(),this.title=n}updateTitle(n){let o=this.buildTitle(n);o!==void 0&&this.title.setTitle(o)}};u(e,"\u0275fac",function(o){return new(o||e)(k(fy))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),Wn=new N("",{providedIn:"root",factory:()=>({})}),kf=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275cmp",E({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(o,i){o&1&&Hd(0,"router-outlet")},dependencies:[Rf],encapsulation:2}));let t=e;return t})();function Ff(t){let e=t.children&&t.children.map(Ff),r=e?L(b({},t),{children:e}):b({},t);return!r.component&&!r.loadComponent&&(e||r.loadChildren)&&r.outlet&&r.outlet!==j&&(r.component=kf),r}var Jr=new N(""),mc=(()=>{let e=class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=I(Ma);loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return F(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let o=hn(n.loadComponent()).pipe($(Qy),Ee(s=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=s}),Xt(()=>{this.componentLoaders.delete(n)})),i=new or(o,()=>new ee).pipe(rr());return this.componentLoaders.set(n,i),i}loadChildren(n,o){if(this.childrenLoaders.get(o))return this.childrenLoaders.get(o);if(o._loadedRoutes)return F({routes:o._loadedRoutes,injector:o._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(o);let s=Yy(o,this.compiler,n,this.onLoadEndListener).pipe(Xt(()=>{this.childrenLoaders.delete(o)})),a=new or(s,()=>new ee).pipe(rr());return this.childrenLoaders.set(o,a),a}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();function Yy(t,e,r,n){return hn(t.loadChildren()).pipe($(Qy),le(o=>o instanceof Ld||Array.isArray(o)?F(o):oe(e.compileModuleAsync(o))),$(o=>{n&&n(t);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(r).injector,s=i.get(Jr,[],{optional:!0,self:!0}).flat()),{routes:s.map(Ff),injector:i}}))}function q_(t){return t&&typeof t=="object"&&"default"in t}function Qy(t){return q_(t)?t.default:t}var vc=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>I(Z_),providedIn:"root"}));let t=e;return t})(),Z_=(()=>{let e=class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,o){return n}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),Pf=new N(""),jf=new N("");function Ky(t,e,r){let n=t.get(jf),o=t.get(ce);return t.get(m).runOutsideAngular(()=>{if(!o.startViewTransition||n.skipNextTransition)return n.skipNextTransition=!1,new Promise(l=>setTimeout(l));let i,s=new Promise(l=>{i=l}),a=o.startViewTransition(()=>(i(),Y_(t))),{onViewTransitionCreated:c}=n;return c&&je(t,()=>c({transition:a,from:e,to:r})),s})}function Y_(t){return new Promise(e=>{vd({read:()=>setTimeout(e)},{injector:t})})}var Lf=new N(""),yc=(()=>{let e=class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new ee;transitionAbortSubject=new ee;configLoader=I(mc);environmentInjector=I(ae);destroyRef=I(Ro);urlSerializer=I(Gt);rootContexts=I(Nt);location=I(We);inputBindingEnabled=I(mi,{optional:!0})!==null;titleStrategy=I(Of);options=I(Wn,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=I(vc);createViewTransition=I(Pf,{optional:!0});navigationErrorHandler=I(Lf,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>F(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=i=>this.events.next(new rc(i)),o=i=>this.events.next(new oc(i));this.configLoader.onLoadEndListener=o,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){var n;(n=this.transitions)==null||n.complete()}handleNavigationRequest(n){var i;let o=++this.navigationId;(i=this.transitions)==null||i.next(L(b({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:o}))}setupNavigations(n){return this.transitions=new pe(null),this.transitions.pipe(ye(o=>o!==null),De(o=>{let i=!1,s=!1;return F(o).pipe(De(a=>{var d;if(this.navigationId>o.id)return this.cancelNavigationTransition(o,"",Be.SupersededByNewNavigation),Oe;this.currentTransition=o,this.currentNavigation={id:a.id,initialUrl:a.rawUrl,extractedUrl:a.extractedUrl,targetBrowserUrl:typeof a.extras.browserUrl=="string"?this.urlSerializer.parse(a.extras.browserUrl):a.extras.browserUrl,trigger:a.source,extras:a.extras,previousNavigation:this.lastSuccessfulNavigation?L(b({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let c=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),l=(d=a.extras.onSameUrlNavigation)!=null?d:n.onSameUrlNavigation;if(!c&&l!=="reload"){let h="";return this.events.next(new Rt(a.id,this.urlSerializer.serialize(a.rawUrl),h,Wr.IgnoredSameUrlNavigation)),a.resolve(!1),Oe}if(this.urlHandlingStrategy.shouldProcessUrl(a.rawUrl))return F(a).pipe(De(h=>(this.events.next(new At(h.id,this.urlSerializer.serialize(h.extractedUrl),h.source,h.restoredState)),h.id!==this.navigationId?Oe:Promise.resolve(h))),H_(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),Ee(h=>{o.targetSnapshot=h.targetSnapshot,o.urlAfterRedirects=h.urlAfterRedirects,this.currentNavigation=L(b({},this.currentNavigation),{finalUrl:h.urlAfterRedirects});let g=new ai(h.id,this.urlSerializer.serialize(h.extractedUrl),this.urlSerializer.serialize(h.urlAfterRedirects),h.targetSnapshot);this.events.next(g)}));if(c&&this.urlHandlingStrategy.shouldProcessUrl(a.currentRawUrl)){let{id:h,extractedUrl:g,source:p,restoredState:v,extras:y}=a,x=new At(h,this.urlSerializer.serialize(g),p,v);this.events.next(x);let O=Py(this.rootComponentType).snapshot;return this.currentTransition=o=L(b({},a),{targetSnapshot:O,urlAfterRedirects:g,extras:L(b({},y),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=g,F(o)}else{let h="";return this.events.next(new Rt(a.id,this.urlSerializer.serialize(a.extractedUrl),h,Wr.IgnoredByUrlHandlingStrategy)),a.resolve(!1),Oe}}),Ee(a=>{let c=new Ja(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),$(a=>(this.currentTransition=o=L(b({},a),{guards:c_(a.targetSnapshot,a.currentSnapshot,this.rootContexts)}),o)),D_(this.environmentInjector,a=>this.events.next(a)),Ee(a=>{if(o.guardsResult=a.guardsResult,a.guardsResult&&typeof a.guardsResult!="boolean")throw fc(this.urlSerializer,a.guardsResult);let c=new ec(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot,!!a.guardsResult);this.events.next(c)}),ye(a=>a.guardsResult?!0:(this.cancelNavigationTransition(a,"",Be.GuardRejected),!1)),gf(a=>{if(a.guards.canActivateChecks.length!==0)return F(a).pipe(Ee(c=>{let l=new tc(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(l)}),De(c=>{let l=!1;return F(c).pipe($_(this.paramsInheritanceStrategy,this.environmentInjector),Ee({next:()=>l=!0,complete:()=>{l||this.cancelNavigationTransition(c,"",Be.NoDataFromResolver)}}))}),Ee(c=>{let l=new nc(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(l)}))}),gf(a=>{let c=l=>{var h;let d=[];(h=l.routeConfig)!=null&&h.loadComponent&&!l.routeConfig._loadedComponent&&d.push(this.configLoader.loadComponent(l.routeConfig).pipe(Ee(g=>{l.component=g}),$(()=>{})));for(let g of l.children)d.push(...c(g));return d};return bn(c(a.targetSnapshot.root)).pipe(Kt(null),kt(1))}),gf(()=>this.afterPreactivation()),De(()=>{var d;let{currentSnapshot:a,targetSnapshot:c}=o,l=(d=this.createViewTransition)==null?void 0:d.call(this,this.environmentInjector,a.root,c.root);return l?oe(l).pipe($(()=>o)):F(o)}),$(a=>{let c=r_(n.routeReuseStrategy,a.targetSnapshot,a.currentRouterState);return this.currentTransition=o=L(b({},a),{targetRouterState:c}),this.currentNavigation.targetRouterState=c,o}),Ee(()=>{this.events.next(new ci)}),a_(this.rootContexts,n.routeReuseStrategy,a=>this.events.next(a),this.inputBindingEnabled),kt(1),Ee({next:a=>{var c;i=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new nt(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects))),(c=this.titleStrategy)==null||c.updateTitle(a.targetRouterState.snapshot),a.resolve(!0)},complete:()=>{i=!0}}),gl(this.transitionAbortSubject.pipe(Ee(a=>{throw a}))),Xt(()=>{var a;!i&&!s&&this.cancelNavigationTransition(o,"",Be.SupersededByNewNavigation),((a=this.currentTransition)==null?void 0:a.id)===o.id&&(this.currentNavigation=null,this.currentTransition=null)}),pt(a=>{var c;if(this.destroyed)return o.resolve(!1),Oe;if(s=!0,Hy(a))this.events.next(new _t(o.id,this.urlSerializer.serialize(o.extractedUrl),a.message,a.cancellationCode)),s_(a)?this.events.next(new Yr(a.url,a.navigationBehaviorOptions)):o.resolve(!1);else{let l=new qr(o.id,this.urlSerializer.serialize(o.extractedUrl),a,(c=o.targetSnapshot)!=null?c:void 0);try{let d=je(this.environmentInjector,()=>{var h;return(h=this.navigationErrorHandler)==null?void 0:h.call(this,l)});if(d instanceof Qr){let{message:h,cancellationCode:g}=fc(this.urlSerializer,d);this.events.next(new _t(o.id,this.urlSerializer.serialize(o.extractedUrl),h,g)),this.events.next(new Yr(d.redirectTo,d.navigationBehaviorOptions))}else throw this.events.next(l),a}catch(d){this.options.resolveNavigationPromiseOnError?o.resolve(!1):o.reject(d)}}return Oe}))}))}cancelNavigationTransition(n,o,i){let s=new _t(n.id,this.urlSerializer.serialize(n.extractedUrl),o,i);this.events.next(s),n.resolve(!1)}isUpdatingInternalState(){var n,o;return((n=this.currentTransition)==null?void 0:n.extractedUrl.toString())!==((o=this.currentTransition)==null?void 0:o.currentUrlTree.toString())}isUpdatedBrowserUrl(){var i,s,a,c;let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),o=(a=(i=this.currentNavigation)==null?void 0:i.targetBrowserUrl)!=null?a:(s=this.currentNavigation)==null?void 0:s.extractedUrl;return n.toString()!==(o==null?void 0:o.toString())&&!((c=this.currentNavigation)!=null&&c.extras.skipLocationChange)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();function Q_(t){return t!==Qa}var Xy=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>I(K_),providedIn:"root"}));let t=e;return t})(),pc=class{shouldDetach(e){return!1}store(e,r){}shouldAttach(e){return!1}retrieve(e){return null}shouldReuseRoute(e,r){return e.routeConfig===r.routeConfig}},K_=(()=>{let e=class e extends pc{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),Jy=(()=>{let e=class e{urlSerializer=I(Gt);options=I(Wn,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=I(We);urlHandlingStrategy=I(vc);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new xt;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:o,targetBrowserUrl:i}){let s=n!==void 0?this.urlHandlingStrategy.merge(n,o):o,a=i!=null?i:s;return a instanceof xt?this.urlSerializer.serialize(a):a}commitTransition({targetRouterState:n,finalUrl:o,initialUrl:i}){o&&n?(this.currentUrlTree=o,this.rawUrlTree=this.urlHandlingStrategy.merge(o,i),this.routerState=n):this.rawUrlTree=i}routerState=Py(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n!=null?n:this.rawUrlTree)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:()=>I(X_),providedIn:"root"}));let t=e;return t})(),X_=(()=>{let e=class e extends Jy{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){var n,o;return this.canceledNavigationResolution!=="computed"?this.currentPageId:(o=(n=this.restoredState())==null?void 0:n.\u0275routerPageId)!=null?o:this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(o=>{o.type==="popstate"&&setTimeout(()=>{n(o.url,o.state,"popstate")})})}handleRouterEvent(n,o){n instanceof At?this.updateStateMemento():n instanceof Rt?this.commitTransition(o):n instanceof ai?this.urlUpdateStrategy==="eager"&&(o.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(o),o)):n instanceof ci?(this.commitTransition(o),this.urlUpdateStrategy==="deferred"&&!o.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(o),o)):n instanceof _t&&(n.code===Be.GuardRejected||n.code===Be.NoDataFromResolver)?this.restoreHistory(o):n instanceof qr?this.restoreHistory(o,!0):n instanceof nt&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:o,id:i}){let{replaceUrl:s,state:a}=o;if(this.location.isCurrentPathEqualTo(n)||s){let c=this.browserPageId,l=b(b({},a),this.generateNgRouterState(i,c));this.location.replaceState(n,"",l)}else{let c=b(b({},a),this.generateNgRouterState(i,this.browserPageId+1));this.location.go(n,"",c)}}restoreHistory(n,o=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,s=this.currentPageId-i;s!==0?this.location.historyGo(s):this.getCurrentUrlTree()===n.finalUrl&&s===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(o&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,o){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:o}:{navigationId:n}}};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();function Dc(t,e){t.events.pipe(ye(r=>r instanceof nt||r instanceof _t||r instanceof qr||r instanceof Rt),$(r=>r instanceof nt||r instanceof Rt?0:(r instanceof _t?r.code===Be.Redirect||r.code===Be.SupersededByNewNavigation:!1)?2:1),ye(r=>r!==2),kt(1)).subscribe(()=>{e()})}var J_={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},ex={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Ce=(()=>{var e,r;let n=class n{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=I(pv);stateManager=I(Jy);options=I(Wn,{optional:!0})||{};pendingTasks=I(Ut);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=I(yc);urlSerializer=I(Gt);location=I(We);urlHandlingStrategy=I(vc);_events=new ee;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=I(Xy);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=(r=(e=I(Jr,{optional:!0}))==null?void 0:e.flat())!=null?r:[];componentInputBindingEnabled=!!I(mi,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:i=>{this.console.warn(i)}}),this.subscribeToNavigationEvents()}eventsSubscription=new ue;subscribeToNavigationEvents(){let i=this.navigationTransitions.events.subscribe(s=>{try{let a=this.navigationTransitions.currentTransition,c=this.navigationTransitions.currentNavigation;if(a!==null&&c!==null){if(this.stateManager.handleRouterEvent(s,c),s instanceof _t&&s.code!==Be.Redirect&&s.code!==Be.SupersededByNewNavigation)this.navigated=!0;else if(s instanceof nt)this.navigated=!0;else if(s instanceof Yr){let l=s.navigationBehaviorOptions,d=this.urlHandlingStrategy.merge(s.url,a.currentRawUrl),h=b({browserUrl:a.extras.browserUrl,info:a.extras.info,skipLocationChange:a.extras.skipLocationChange,replaceUrl:a.extras.replaceUrl||this.urlUpdateStrategy==="eager"||Q_(a.source)},l);this.scheduleNavigation(d,Qa,null,h,{resolve:a.resolve,reject:a.reject,promise:a.promise})}}nx(s)&&this._events.next(s)}catch(a){this.navigationTransitions.transitionAbortSubject.next(a)}});this.eventsSubscription.add(i)}resetRootComponentType(i){this.routerState.root.component=i,this.navigationTransitions.rootComponentType=i}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Qa,this.stateManager.restoredState())}setUpLocationChangeListener(){var i;(i=this.nonRouterCurrentEntryChangeSubscription)!=null||(this.nonRouterCurrentEntryChangeSubscription=this.stateManager.registerNonRouterCurrentEntryChangeListener((s,a,c)=>{this.navigateToSyncWithBrowser(s,c,a)}))}navigateToSyncWithBrowser(i,s,a){let c={replaceUrl:!0},l=a!=null&&a.navigationId?a:null;if(a){let h=b({},a);delete h.navigationId,delete h.\u0275routerPageId,Object.keys(h).length!==0&&(c.state=h)}let d=this.parseUrl(i);this.scheduleNavigation(d,s,l,c)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(i){this.config=i.map(Ff),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(i,s={}){let{relativeTo:a,queryParams:c,fragment:l,queryParamsHandling:d,preserveFragment:h}=s,g=h?this.currentUrlTree.fragment:l,p=null;switch(d!=null?d:this.options.defaultQueryParamsHandling){case"merge":p=b(b({},this.currentUrlTree.queryParams),c);break;case"preserve":p=this.currentUrlTree.queryParams;break;default:p=c||null}p!==null&&(p=this.removeEmptyProps(p));let v;try{let y=a?a.snapshot:this.routerState.snapshot.root;v=Ny(y)}catch{(typeof i[0]!="string"||i[0][0]!=="/")&&(i=[]),v=this.currentUrlTree.root}return Oy(v,i,p,g!=null?g:null)}navigateByUrl(i,s={skipLocationChange:!1}){let a=fn(i)?i:this.parseUrl(i),c=this.urlHandlingStrategy.merge(a,this.rawUrlTree);return this.scheduleNavigation(c,Qa,null,s)}navigate(i,s={skipLocationChange:!1}){return tx(i),this.navigateByUrl(this.createUrlTree(i,s),s)}serializeUrl(i){return this.urlSerializer.serialize(i)}parseUrl(i){try{return this.urlSerializer.parse(i)}catch{return this.urlSerializer.parse("/")}}isActive(i,s){let a;if(s===!0?a=b({},J_):s===!1?a=b({},ex):a=s,fn(i))return hy(this.currentUrlTree,i,a);let c=this.parseUrl(i);return hy(this.currentUrlTree,c,a)}removeEmptyProps(i){return Object.entries(i).reduce((s,[a,c])=>(c!=null&&(s[a]=c),s),{})}scheduleNavigation(i,s,a,c,l){if(this.disposed)return Promise.resolve(!1);let d,h,g;l?(d=l.resolve,h=l.reject,g=l.promise):g=new Promise((v,y)=>{d=v,h=y});let p=this.pendingTasks.add();return Dc(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(p))}),this.navigationTransitions.handleNavigationRequest({source:s,restoredState:a,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:i,extras:c,resolve:d,reject:h,promise:g,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),g.catch(v=>Promise.reject(v))}};u(n,"\u0275fac",function(s){return new(s||n)}),u(n,"\u0275prov",A({token:n,factory:n.\u0275fac,providedIn:"root"}));let t=n;return t})();function tx(t){for(let e=0;e<t.length;e++)if(t[e]==null)throw new R(4008,!1)}function nx(t){return!(t instanceof ci)&&!(t instanceof Yr)}var Di=(()=>{let e=class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new ee;constructor(n,o,i,s,a,c){var d;this.router=n,this.route=o,this.tabIndexAttribute=i,this.renderer=s,this.el=a,this.locationStrategy=c;let l=(d=a.nativeElement.tagName)==null?void 0:d.toLowerCase();this.isAnchorElement=l==="a"||l==="area",this.isAnchorElement?this.subscription=n.events.subscribe(h=>{h instanceof nt&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(n){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",n)}ngOnChanges(n){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(n){n==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(fn(n)?this.routerLinkInput=n:this.routerLinkInput=Array.isArray(n)?n:[n],this.setTabIndexIfNotOnNativeEl("0"))}onClick(n,o,i,s,a){let c=this.urlTree;if(c===null||this.isAnchorElement&&(n!==0||o||i||s||a||typeof this.target=="string"&&this.target!="_self"))return!0;let l={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(c,l),!this.isAnchorElement}ngOnDestroy(){var n;(n=this.subscription)==null||n.unsubscribe()}updateHref(){var i;let n=this.urlTree;this.href=n!==null&&this.locationStrategy?(i=this.locationStrategy)==null?void 0:i.prepareExternalUrl(this.router.serializeUrl(n)):null;let o=this.href===null?null:Mm(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",o)}applyAttributeValue(n,o){let i=this.renderer,s=this.el.nativeElement;o!==null?i.setAttribute(s,n,o):i.removeAttribute(s,n)}get urlTree(){return this.routerLinkInput===null?null:fn(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}};u(e,"\u0275fac",function(o){return new(o||e)(f(Ce),f(Ae),Mt("tabindex"),f(on),f(D),f(Ve))}),u(e,"\u0275dir",U({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(o,i){o&1&&Ie("click",function(a){return i.onClick(a.button,a.ctrlKey,a.shiftKey,a.altKey,a.metaKey)}),o&2&&ct("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",cn],skipLocationChange:[2,"skipLocationChange","skipLocationChange",cn],replaceUrl:[2,"replaceUrl","replaceUrl",cn],routerLink:"routerLink"},features:[tt]}));let t=e;return t})();var yi=class{},rx=(()=>{let e=class e{preload(n,o){return o().pipe(pt(()=>F(null)))}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();var eD=(()=>{let e=class e{router;injector;preloadingStrategy;loader;subscription;constructor(n,o,i,s,a){this.router=n,this.injector=i,this.preloadingStrategy=s,this.loader=a}setUpPreloading(){this.subscription=this.router.events.pipe(ye(n=>n instanceof nt),gt(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,o){var s,a,c;let i=[];for(let l of o){l.providers&&!l._injector&&(l._injector=jo(l.providers,n,`Route: ${l.path}`));let d=(s=l._injector)!=null?s:n,h=(a=l._loadedInjector)!=null?a:d;(l.loadChildren&&!l._loadedRoutes&&l.canLoad===void 0||l.loadComponent&&!l._loadedComponent)&&i.push(this.preloadConfig(d,l)),(l.children||l._loadedRoutes)&&i.push(this.processRoutes(h,(c=l.children)!=null?c:l._loadedRoutes))}return oe(i).pipe(cr())}preloadConfig(n,o){return this.preloadingStrategy.preload(o,()=>{let i;o.loadChildren&&o.canLoad===void 0?i=this.loader.loadChildren(n,o):i=F(null);let s=i.pipe(le(a=>{var c;return a===null?F(void 0):(o._loadedRoutes=a.routes,o._loadedInjector=a.injector,this.processRoutes((c=a.injector)!=null?c:n,a.routes))}));if(o.loadComponent&&!o._loadedComponent){let a=this.loader.loadComponent(o);return oe([s,a]).pipe(cr())}else return s})}};u(e,"\u0275fac",function(o){return new(o||e)(k(Ce),k(Ma),k(ae),k(yi),k(mc))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),tD=new N(""),ox=(()=>{let e=class e{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(n,o,i,s,a={}){this.urlSerializer=n,this.transitions=o,this.viewportScroller=i,this.zone=s,this.options=a,a.scrollPositionRestoration||(a.scrollPositionRestoration="disabled"),a.anchorScrolling||(a.anchorScrolling="disabled")}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof At?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof nt?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof Rt&&n.code===Wr.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof Zr&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,o){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new Zr(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,o))})},0)})}ngOnDestroy(){var n,o;(n=this.routerEventsSubscription)==null||n.unsubscribe(),(o=this.scrollEventsSubscription)==null||o.unsubscribe()}};u(e,"\u0275fac",function(o){tv()}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();function ix(t){return t.routerState.root}function Ii(t,e){return{\u0275kind:t,\u0275providers:e}}function sx(){let t=I(te);return e=>{var i,s;let r=t.get(Et);if(e!==r.components[0])return;let n=t.get(Ce),o=t.get(nD);t.get(Bf)===1&&n.initialNavigation(),(i=t.get(iD,null,z.Optional))==null||i.setUpPreloading(),(s=t.get(tD,null,z.Optional))==null||s.init(),n.resetRootComponentType(r.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var nD=new N("",{factory:()=>new ee}),Bf=new N("",{providedIn:"root",factory:()=>1});function rD(){let t=[{provide:Bf,useValue:0},Bd(()=>{let e=I(te);return e.get(qd,Promise.resolve()).then(()=>new Promise(n=>{let o=e.get(Ce),i=e.get(nD);Dc(o,()=>{n(!0)}),e.get(yc).afterPreactivation=()=>(n(!0),i.closed?F(void 0):i),o.initialNavigation()}))})];return Ii(2,t)}function oD(){let t=[Bd(()=>{I(Ce).setUpLocationChangeListener()}),{provide:Bf,useValue:2}];return Ii(3,t)}var iD=new N("");function sD(t){return Ii(0,[{provide:iD,useExisting:eD},{provide:yi,useExisting:t}])}function aD(){return Ii(8,[Nf,{provide:mi,useExisting:Nf}])}function cD(t){Oo("NgRouterViewTransitions");let e=[{provide:Pf,useValue:Ky},{provide:jf,useValue:b({skipNextTransition:!!(t!=null&&t.skipInitialTransition)},t)}];return Ii(9,e)}var lD=[We,{provide:Gt,useClass:dn},Ce,Nt,{provide:Ae,useFactory:ix,deps:[Ce]},mc,[]],ax=(()=>{let e=class e{constructor(){}static forRoot(n,o){return{ngModule:e,providers:[lD,[],{provide:Jr,multi:!0,useValue:n},[],o!=null&&o.errorHandler?{provide:Lf,useValue:o.errorHandler}:[],{provide:Wn,useValue:o||{}},o!=null&&o.useHash?lx():ux(),cx(),o!=null&&o.preloadingStrategy?sD(o.preloadingStrategy).\u0275providers:[],o!=null&&o.initialNavigation?dx(o):[],o!=null&&o.bindToComponentInputs?aD().\u0275providers:[],o!=null&&o.enableViewTransitions?cD().\u0275providers:[],fx()]}}static forChild(n){return{ngModule:e,providers:[{provide:Jr,multi:!0,useValue:n}]}}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275mod",Re({type:e})),u(e,"\u0275inj",_e({}));let t=e;return t})();function cx(){return{provide:tD,useFactory:()=>{let t=I(Bv),e=I(m),r=I(Wn),n=I(yc),o=I(Gt);return r.scrollOffset&&t.setOffset(r.scrollOffset),new ox(o,n,t,e,r)}}}function lx(){return{provide:Ve,useClass:Yd}}function ux(){return{provide:Ve,useClass:Aa}}function dx(t){return[t.initialNavigation==="disabled"?oD().\u0275providers:[],t.initialNavigation==="enabledBlocking"?rD().\u0275providers:[]]}var Vf=new N("");function fx(){return[{provide:Vf,useFactory:sx},{provide:Ud,multi:!0,useExisting:Vf}]}var DD=(()=>{let e=class e{_renderer;_elementRef;onChange=n=>{};onTouched=()=>{};constructor(n,o){this._renderer=n,this._elementRef=o}setProperty(n,o){this._renderer.setProperty(this._elementRef.nativeElement,n,o)}registerOnTouched(n){this.onTouched=n}registerOnChange(n){this.onChange=n}setDisabledState(n){this.setProperty("disabled",n)}};u(e,"\u0275fac",function(o){return new(o||e)(f(on),f(D))}),u(e,"\u0275dir",U({type:e}));let t=e;return t})(),hx=(()=>{let e=class e extends DD{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",U({type:e,features:[ne]}));let t=e;return t})(),Zn=new N("");var px={provide:Zn,useExisting:$e(()=>ID),multi:!0};function gx(){let t=Ge()?Ge().getUserAgent():"";return/android (\d+)/.test(t.toLowerCase())}var mx=new N(""),ID=(()=>{let e=class e extends DD{_compositionMode;_composing=!1;constructor(n,o,i){super(n,o),this._compositionMode=i,this._compositionMode==null&&(this._compositionMode=!gx())}writeValue(n){let o=n==null?"":n;this.setProperty("value",o)}_handleInput(n){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(n)}_compositionStart(){this._composing=!0}_compositionEnd(n){this._composing=!1,this._compositionMode&&this.onChange(n)}};u(e,"\u0275fac",function(o){return new(o||e)(f(on),f(D),f(mx,8))}),u(e,"\u0275dir",U({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(o,i){o&1&&Ie("input",function(a){return i._handleInput(a.target.value)})("blur",function(){return i.onTouched()})("compositionstart",function(){return i._compositionStart()})("compositionend",function(a){return i._compositionEnd(a.target.value)})},standalone:!1,features:[Ne([px]),ne]}));let t=e;return t})();function vx(t){return t==null||CD(t)===0}function CD(t){return t==null?null:Array.isArray(t)||typeof t=="string"?t.length:t instanceof Set?t.size:null}var Wt=new N(""),bD=new N("");function yx(t){return e=>{if(e.value==null||t==null)return null;let r=parseFloat(e.value);return!isNaN(r)&&r<t?{min:{min:t,actual:e.value}}:null}}function Dx(t){return e=>{if(e.value==null||t==null)return null;let r=parseFloat(e.value);return!isNaN(r)&&r>t?{max:{max:t,actual:e.value}}:null}}function Ix(t){return vx(t.value)?{required:!0}:null}function Cx(t){return e=>{var n,o;let r=(o=(n=e.value)==null?void 0:n.length)!=null?o:CD(e.value);return r!==null&&r>t?{maxlength:{requiredLength:t,actualLength:r}}:null}}function dD(t){return null}function ED(t){return t!=null}function wD(t){return Bn(t)?oe(t):t}function MD(t){let e={};return t.forEach(r=>{e=r!=null?b(b({},e),r):e}),Object.keys(e).length===0?null:e}function SD(t,e){return e.map(r=>r(t))}function bx(t){return!t.validate}function TD(t){return t.map(e=>bx(e)?e:r=>e.validate(r))}function Ex(t){if(!t)return null;let e=t.filter(ED);return e.length==0?null:function(r){return MD(SD(r,e))}}function Hf(t){return t!=null?Ex(TD(t)):null}function wx(t){if(!t)return null;let e=t.filter(ED);return e.length==0?null:function(r){let n=SD(r,e).map(wD);return ul(n).pipe($(MD))}}function $f(t){return t!=null?wx(TD(t)):null}function fD(t,e){return t===null?[e]:Array.isArray(t)?[...t,e]:[t,e]}function Mx(t){return t._rawValidators}function Sx(t){return t._rawAsyncValidators}function Uf(t){return t?Array.isArray(t)?t:[t]:[]}function Cc(t,e){return Array.isArray(t)?t.includes(e):t===e}function hD(t,e){let r=Uf(e);return Uf(t).forEach(o=>{Cc(r,o)||r.push(o)}),r}function pD(t,e){return Uf(e).filter(r=>!Cc(t,r))}var bc=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(e){this._rawValidators=e||[],this._composedValidatorFn=Hf(this._rawValidators)}_setAsyncValidators(e){this._rawAsyncValidators=e||[],this._composedAsyncValidatorFn=$f(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(e){this._onDestroyCallbacks.push(e)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(e=>e()),this._onDestroyCallbacks=[]}reset(e=void 0){this.control&&this.control.reset(e)}hasError(e,r){return this.control?this.control.hasError(e,r):!1}getError(e,r){return this.control?this.control.getError(e,r):null}},no=class extends bc{name;get formDirective(){return null}get path(){return null}},qn=class extends bc{_parent=null;name=null;valueAccessor=null},Ec=class{_cd;constructor(e){this._cd=e}get isTouched(){var e,r,n,o,i;return(n=(r=(e=this._cd)==null?void 0:e.control)==null?void 0:r._touched)==null||n.call(r),!!((i=(o=this._cd)==null?void 0:o.control)!=null&&i.touched)}get isUntouched(){var e,r;return!!((r=(e=this._cd)==null?void 0:e.control)!=null&&r.untouched)}get isPristine(){var e,r,n,o,i;return(n=(r=(e=this._cd)==null?void 0:e.control)==null?void 0:r._pristine)==null||n.call(r),!!((i=(o=this._cd)==null?void 0:o.control)!=null&&i.pristine)}get isDirty(){var e,r;return!!((r=(e=this._cd)==null?void 0:e.control)!=null&&r.dirty)}get isValid(){var e,r,n,o,i;return(n=(r=(e=this._cd)==null?void 0:e.control)==null?void 0:r._status)==null||n.call(r),!!((i=(o=this._cd)==null?void 0:o.control)!=null&&i.valid)}get isInvalid(){var e,r;return!!((r=(e=this._cd)==null?void 0:e.control)!=null&&r.invalid)}get isPending(){var e,r;return!!((r=(e=this._cd)==null?void 0:e.control)!=null&&r.pending)}get isSubmitted(){var e,r,n;return(r=(e=this._cd)==null?void 0:e._submitted)==null||r.call(e),!!((n=this._cd)!=null&&n.submitted)}},Tx={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},OH=L(b({},Tx),{"[class.ng-submitted]":"isSubmitted"}),kH=(()=>{let e=class e extends Ec{constructor(n){super(n)}};u(e,"\u0275fac",function(o){return new(o||e)(f(qn,2))}),u(e,"\u0275dir",U({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(o,i){o&2&&Ca("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)},standalone:!1,features:[ne]}));let t=e;return t})(),FH=(()=>{let e=class e extends Ec{constructor(n){super(n)}};u(e,"\u0275fac",function(o){return new(o||e)(f(no,10))}),u(e,"\u0275dir",U({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(o,i){o&2&&Ca("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)("ng-submitted",i.isSubmitted)},standalone:!1,features:[ne]}));let t=e;return t})();var Ci="VALID",Ic="INVALID",eo="PENDING",bi="DISABLED",ro=class{},wc=class extends ro{value;source;constructor(e,r){super(),this.value=e,this.source=r}},wi=class extends ro{pristine;source;constructor(e,r){super(),this.pristine=e,this.source=r}},Mi=class extends ro{touched;source;constructor(e,r){super(),this.touched=e,this.source=r}},to=class extends ro{status;source;constructor(e,r){super(),this.status=e,this.source=r}};function _D(t){return(Tc(t)?t.validators:t)||null}function _x(t){return Array.isArray(t)?Hf(t):t||null}function xD(t,e){return(Tc(e)?e.asyncValidators:t)||null}function xx(t){return Array.isArray(t)?$f(t):t||null}function Tc(t){return t!=null&&!Array.isArray(t)&&typeof t=="object"}function Ax(t,e,r){let n=t.controls;if(!(e?Object.keys(n):n).length)throw new R(1e3,"");if(!n[r])throw new R(1001,"")}function Rx(t,e,r){t._forEachChild((n,o)=>{if(r[o]===void 0)throw new R(1002,"")})}var Mc=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(e,r){this._assignValidators(e),this._assignAsyncValidators(r)}get validator(){return this._composedValidatorFn}set validator(e){this._rawValidators=this._composedValidatorFn=e}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(e){this._rawAsyncValidators=this._composedAsyncValidatorFn=e}get parent(){return this._parent}get status(){return $t(this.statusReactive)}set status(e){$t(()=>this.statusReactive.set(e))}_status=Ho(()=>this.statusReactive());statusReactive=No(void 0);get valid(){return this.status===Ci}get invalid(){return this.status===Ic}get pending(){return this.status==eo}get disabled(){return this.status===bi}get enabled(){return this.status!==bi}errors;get pristine(){return $t(this.pristineReactive)}set pristine(e){$t(()=>this.pristineReactive.set(e))}_pristine=Ho(()=>this.pristineReactive());pristineReactive=No(!0);get dirty(){return!this.pristine}get touched(){return $t(this.touchedReactive)}set touched(e){$t(()=>this.touchedReactive.set(e))}_touched=Ho(()=>this.touchedReactive());touchedReactive=No(!1);get untouched(){return!this.touched}_events=new ee;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(e){this._assignValidators(e)}setAsyncValidators(e){this._assignAsyncValidators(e)}addValidators(e){this.setValidators(hD(e,this._rawValidators))}addAsyncValidators(e){this.setAsyncValidators(hD(e,this._rawAsyncValidators))}removeValidators(e){this.setValidators(pD(e,this._rawValidators))}removeAsyncValidators(e){this.setAsyncValidators(pD(e,this._rawAsyncValidators))}hasValidator(e){return Cc(this._rawValidators,e)}hasAsyncValidator(e){return Cc(this._rawAsyncValidators,e)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(e={}){var o;let r=this.touched===!1;this.touched=!0;let n=(o=e.sourceControl)!=null?o:this;this._parent&&!e.onlySelf&&this._parent.markAsTouched(L(b({},e),{sourceControl:n})),r&&e.emitEvent!==!1&&this._events.next(new Mi(!0,n))}markAllAsTouched(e={}){this.markAsTouched({onlySelf:!0,emitEvent:e.emitEvent,sourceControl:this}),this._forEachChild(r=>r.markAllAsTouched(e))}markAsUntouched(e={}){var o;let r=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let n=(o=e.sourceControl)!=null?o:this;this._forEachChild(i=>{i.markAsUntouched({onlySelf:!0,emitEvent:e.emitEvent,sourceControl:n})}),this._parent&&!e.onlySelf&&this._parent._updateTouched(e,n),r&&e.emitEvent!==!1&&this._events.next(new Mi(!1,n))}markAsDirty(e={}){var o;let r=this.pristine===!0;this.pristine=!1;let n=(o=e.sourceControl)!=null?o:this;this._parent&&!e.onlySelf&&this._parent.markAsDirty(L(b({},e),{sourceControl:n})),r&&e.emitEvent!==!1&&this._events.next(new wi(!1,n))}markAsPristine(e={}){var o;let r=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let n=(o=e.sourceControl)!=null?o:this;this._forEachChild(i=>{i.markAsPristine({onlySelf:!0,emitEvent:e.emitEvent})}),this._parent&&!e.onlySelf&&this._parent._updatePristine(e,n),r&&e.emitEvent!==!1&&this._events.next(new wi(!0,n))}markAsPending(e={}){var n;this.status=eo;let r=(n=e.sourceControl)!=null?n:this;e.emitEvent!==!1&&(this._events.next(new to(this.status,r)),this.statusChanges.emit(this.status)),this._parent&&!e.onlySelf&&this._parent.markAsPending(L(b({},e),{sourceControl:r}))}disable(e={}){var o;let r=this._parentMarkedDirty(e.onlySelf);this.status=bi,this.errors=null,this._forEachChild(i=>{i.disable(L(b({},e),{onlySelf:!0}))}),this._updateValue();let n=(o=e.sourceControl)!=null?o:this;e.emitEvent!==!1&&(this._events.next(new wc(this.value,n)),this._events.next(new to(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(L(b({},e),{skipPristineCheck:r}),this),this._onDisabledChange.forEach(i=>i(!0))}enable(e={}){let r=this._parentMarkedDirty(e.onlySelf);this.status=Ci,this._forEachChild(n=>{n.enable(L(b({},e),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:e.emitEvent}),this._updateAncestors(L(b({},e),{skipPristineCheck:r}),this),this._onDisabledChange.forEach(n=>n(!1))}_updateAncestors(e,r){this._parent&&!e.onlySelf&&(this._parent.updateValueAndValidity(e),e.skipPristineCheck||this._parent._updatePristine({},r),this._parent._updateTouched({},r))}setParent(e){this._parent=e}getRawValue(){return this.value}updateValueAndValidity(e={}){var n;if(this._setInitialStatus(),this._updateValue(),this.enabled){let o=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Ci||this.status===eo)&&this._runAsyncValidator(o,e.emitEvent)}let r=(n=e.sourceControl)!=null?n:this;e.emitEvent!==!1&&(this._events.next(new wc(this.value,r)),this._events.next(new to(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!e.onlySelf&&this._parent.updateValueAndValidity(L(b({},e),{sourceControl:r}))}_updateTreeValidity(e={emitEvent:!0}){this._forEachChild(r=>r._updateTreeValidity(e)),this.updateValueAndValidity({onlySelf:!0,emitEvent:e.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?bi:Ci}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(e,r){if(this.asyncValidator){this.status=eo,this._hasOwnPendingAsyncValidator={emitEvent:r!==!1};let n=wD(this.asyncValidator(this));this._asyncValidationSubscription=n.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:r,shouldHaveEmitted:e})})}}_cancelExistingSubscription(){var e,r;if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let n=(r=(e=this._hasOwnPendingAsyncValidator)==null?void 0:e.emitEvent)!=null?r:!1;return this._hasOwnPendingAsyncValidator=null,n}return!1}setErrors(e,r={}){this.errors=e,this._updateControlsErrors(r.emitEvent!==!1,this,r.shouldHaveEmitted)}get(e){let r=e;return r==null||(Array.isArray(r)||(r=r.split(".")),r.length===0)?null:r.reduce((n,o)=>n&&n._find(o),this)}getError(e,r){let n=r?this.get(r):this;return n&&n.errors?n.errors[e]:null}hasError(e,r){return!!this.getError(e,r)}get root(){let e=this;for(;e._parent;)e=e._parent;return e}_updateControlsErrors(e,r,n){this.status=this._calculateStatus(),e&&this.statusChanges.emit(this.status),(e||n)&&this._events.next(new to(this.status,r)),this._parent&&this._parent._updateControlsErrors(e,r,n)}_initObservables(){this.valueChanges=new se,this.statusChanges=new se}_calculateStatus(){return this._allControlsDisabled()?bi:this.errors?Ic:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(eo)?eo:this._anyControlsHaveStatus(Ic)?Ic:Ci}_anyControlsHaveStatus(e){return this._anyControls(r=>r.status===e)}_anyControlsDirty(){return this._anyControls(e=>e.dirty)}_anyControlsTouched(){return this._anyControls(e=>e.touched)}_updatePristine(e,r){let n=!this._anyControlsDirty(),o=this.pristine!==n;this.pristine=n,this._parent&&!e.onlySelf&&this._parent._updatePristine(e,r),o&&this._events.next(new wi(this.pristine,r))}_updateTouched(e={},r){this.touched=this._anyControlsTouched(),this._events.next(new Mi(this.touched,r)),this._parent&&!e.onlySelf&&this._parent._updateTouched(e,r)}_onDisabledChange=[];_registerOnCollectionChange(e){this._onCollectionChange=e}_setUpdateStrategy(e){Tc(e)&&e.updateOn!=null&&(this._updateOn=e.updateOn)}_parentMarkedDirty(e){let r=this._parent&&this._parent.dirty;return!e&&!!r&&!this._parent._anyControlsDirty()}_find(e){return null}_assignValidators(e){this._rawValidators=Array.isArray(e)?e.slice():e,this._composedValidatorFn=_x(this._rawValidators)}_assignAsyncValidators(e){this._rawAsyncValidators=Array.isArray(e)?e.slice():e,this._composedAsyncValidatorFn=xx(this._rawAsyncValidators)}},Sc=class extends Mc{constructor(e,r,n){super(_D(r),xD(n,r)),this.controls=e,this._initObservables(),this._setUpdateStrategy(r),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(e,r){return this.controls[e]?this.controls[e]:(this.controls[e]=r,r.setParent(this),r._registerOnCollectionChange(this._onCollectionChange),r)}addControl(e,r,n={}){this.registerControl(e,r),this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}removeControl(e,r={}){this.controls[e]&&this.controls[e]._registerOnCollectionChange(()=>{}),delete this.controls[e],this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}setControl(e,r,n={}){this.controls[e]&&this.controls[e]._registerOnCollectionChange(()=>{}),delete this.controls[e],r&&this.registerControl(e,r),this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}contains(e){return this.controls.hasOwnProperty(e)&&this.controls[e].enabled}setValue(e,r={}){Rx(this,!0,e),Object.keys(e).forEach(n=>{Ax(this,!0,n),this.controls[n].setValue(e[n],{onlySelf:!0,emitEvent:r.emitEvent})}),this.updateValueAndValidity(r)}patchValue(e,r={}){e!=null&&(Object.keys(e).forEach(n=>{let o=this.controls[n];o&&o.patchValue(e[n],{onlySelf:!0,emitEvent:r.emitEvent})}),this.updateValueAndValidity(r))}reset(e={},r={}){this._forEachChild((n,o)=>{n.reset(e?e[o]:null,{onlySelf:!0,emitEvent:r.emitEvent})}),this._updatePristine(r,this),this._updateTouched(r,this),this.updateValueAndValidity(r)}getRawValue(){return this._reduceChildren({},(e,r,n)=>(e[n]=r.getRawValue(),e))}_syncPendingControls(){let e=this._reduceChildren(!1,(r,n)=>n._syncPendingControls()?!0:r);return e&&this.updateValueAndValidity({onlySelf:!0}),e}_forEachChild(e){Object.keys(this.controls).forEach(r=>{let n=this.controls[r];n&&e(n,r)})}_setUpControls(){this._forEachChild(e=>{e.setParent(this),e._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(e){for(let[r,n]of Object.entries(this.controls))if(this.contains(r)&&e(n))return!0;return!1}_reduceValue(){let e={};return this._reduceChildren(e,(r,n,o)=>((n.enabled||this.disabled)&&(r[o]=n.value),r))}_reduceChildren(e,r){let n=e;return this._forEachChild((o,i)=>{n=r(n,o,i)}),n}_allControlsDisabled(){for(let e of Object.keys(this.controls))if(this.controls[e].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(e){return this.controls.hasOwnProperty(e)?this.controls[e]:null}};var zf=new N("",{providedIn:"root",factory:()=>Gf}),Gf="always";function Nx(t,e){return[...e.path,t]}function AD(t,e,r=Gf){var n,o;RD(t,e),e.valueAccessor.writeValue(t.value),(t.disabled||r==="always")&&((o=(n=e.valueAccessor).setDisabledState)==null||o.call(n,t.disabled)),kx(t,e),Px(t,e),Fx(t,e),Ox(t,e)}function gD(t,e){t.forEach(r=>{r.registerOnValidatorChange&&r.registerOnValidatorChange(e)})}function Ox(t,e){if(e.valueAccessor.setDisabledState){let r=n=>{e.valueAccessor.setDisabledState(n)};t.registerOnDisabledChange(r),e._registerOnDestroy(()=>{t._unregisterOnDisabledChange(r)})}}function RD(t,e){let r=Mx(t);e.validator!==null?t.setValidators(fD(r,e.validator)):typeof r=="function"&&t.setValidators([r]);let n=Sx(t);e.asyncValidator!==null?t.setAsyncValidators(fD(n,e.asyncValidator)):typeof n=="function"&&t.setAsyncValidators([n]);let o=()=>t.updateValueAndValidity();gD(e._rawValidators,o),gD(e._rawAsyncValidators,o)}function kx(t,e){e.valueAccessor.registerOnChange(r=>{t._pendingValue=r,t._pendingChange=!0,t._pendingDirty=!0,t.updateOn==="change"&&ND(t,e)})}function Fx(t,e){e.valueAccessor.registerOnTouched(()=>{t._pendingTouched=!0,t.updateOn==="blur"&&t._pendingChange&&ND(t,e),t.updateOn!=="submit"&&t.markAsTouched()})}function ND(t,e){t._pendingDirty&&t.markAsDirty(),t.setValue(t._pendingValue,{emitModelToViewChange:!1}),e.viewToModelUpdate(t._pendingValue),t._pendingChange=!1}function Px(t,e){let r=(n,o)=>{e.valueAccessor.writeValue(n),o&&e.viewToModelUpdate(n)};t.registerOnChange(r),e._registerOnDestroy(()=>{t._unregisterOnChange(r)})}function jx(t,e){t==null,RD(t,e)}function Lx(t,e){if(!t.hasOwnProperty("model"))return!1;let r=t.model;return r.isFirstChange()?!0:!Object.is(e,r.currentValue)}function Vx(t){return Object.getPrototypeOf(t.constructor)===hx}function Bx(t,e){t._syncPendingControls(),e.forEach(r=>{let n=r.control;n.updateOn==="submit"&&n._pendingChange&&(r.viewToModelUpdate(n._pendingValue),n._pendingChange=!1)})}function Ux(t,e){if(!e)return null;Array.isArray(e);let r,n,o;return e.forEach(i=>{i.constructor===ID?r=i:Vx(i)?n=i:o=i}),o||n||r||null}var Hx={provide:no,useExisting:$e(()=>$x)},Ei=Promise.resolve(),$x=(()=>{let e=class e extends no{callSetDisabledState;get submitted(){return $t(this.submittedReactive)}_submitted=Ho(()=>this.submittedReactive());submittedReactive=No(!1);_directives=new Set;form;ngSubmit=new se;options;constructor(n,o,i){super(),this.callSetDisabledState=i,this.form=new Sc({},Hf(n),$f(o))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(n){Ei.then(()=>{let o=this._findContainer(n.path);n.control=o.registerControl(n.name,n.control),AD(n.control,n,this.callSetDisabledState),n.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(n)})}getControl(n){return this.form.get(n.path)}removeControl(n){Ei.then(()=>{let o=this._findContainer(n.path);o&&o.removeControl(n.name),this._directives.delete(n)})}addFormGroup(n){Ei.then(()=>{let o=this._findContainer(n.path),i=new Sc({});jx(i,n),o.registerControl(n.name,i),i.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(n){Ei.then(()=>{let o=this._findContainer(n.path);o&&o.removeControl(n.name)})}getFormGroup(n){return this.form.get(n.path)}updateModel(n,o){Ei.then(()=>{this.form.get(n.path).setValue(o)})}setValue(n){this.control.setValue(n)}onSubmit(n){var o;return this.submittedReactive.set(!0),Bx(this.form,this._directives),this.ngSubmit.emit(n),((o=n==null?void 0:n.target)==null?void 0:o.method)==="dialog"}onReset(){this.resetForm()}resetForm(n=void 0){this.form.reset(n),this.submittedReactive.set(!1)}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(n){return n.pop(),n.length?this.form.get(n):this.form}};u(e,"\u0275fac",function(o){return new(o||e)(f(Wt,10),f(bD,10),f(zf,8))}),u(e,"\u0275dir",U({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(o,i){o&1&&Ie("submit",function(a){return i.onSubmit(a)})("reset",function(){return i.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[Ne([Hx]),ne]}));let t=e;return t})();function mD(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}function vD(t){return typeof t=="object"&&t!==null&&Object.keys(t).length===2&&"value"in t&&"disabled"in t}var zx=class extends Mc{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(e=null,r,n){super(_D(r),xD(n,r)),this._applyFormState(e),this._setUpdateStrategy(r),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Tc(r)&&(r.nonNullable||r.initialValueIsDefault)&&(vD(e)?this.defaultValue=e.value:this.defaultValue=e)}setValue(e,r={}){this.value=this._pendingValue=e,this._onChange.length&&r.emitModelToViewChange!==!1&&this._onChange.forEach(n=>n(this.value,r.emitViewToModelChange!==!1)),this.updateValueAndValidity(r)}patchValue(e,r={}){this.setValue(e,r)}reset(e=this.defaultValue,r={}){this._applyFormState(e),this.markAsPristine(r),this.markAsUntouched(r),this.setValue(this.value,r),this._pendingChange=!1}_updateValue(){}_anyControls(e){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(e){this._onChange.push(e)}_unregisterOnChange(e){mD(this._onChange,e)}registerOnDisabledChange(e){this._onDisabledChange.push(e)}_unregisterOnDisabledChange(e){mD(this._onDisabledChange,e)}_forEachChild(e){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(e){vD(e)?(this.value=this._pendingValue=e.value,e.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=e}};var Gx={provide:qn,useExisting:$e(()=>Wx)},yD=Promise.resolve(),Wx=(()=>{let e=class e extends qn{_changeDetectorRef;callSetDisabledState;control=new zx;_registered=!1;viewModel;name="";isDisabled;model;options;update=new se;constructor(n,o,i,s,a,c){super(),this._changeDetectorRef=a,this.callSetDisabledState=c,this._parent=n,this._setValidators(o),this._setAsyncValidators(i),this.valueAccessor=Ux(this,s)}ngOnChanges(n){if(this._checkForErrors(),!this._registered||"name"in n){if(this._registered&&(this._checkName(),this.formDirective)){let o=n.name.previousValue;this.formDirective.removeControl({name:o,path:this._getPath(o)})}this._setUpControl()}"isDisabled"in n&&this._updateDisabled(n),Lx(n,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(n){this.viewModel=n,this.update.emit(n)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){AD(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(n){yD.then(()=>{var o;this.control.setValue(n,{emitViewToModelChange:!1}),(o=this._changeDetectorRef)==null||o.markForCheck()})}_updateDisabled(n){let o=n.isDisabled.currentValue,i=o!==0&&cn(o);yD.then(()=>{var s;i&&!this.control.disabled?this.control.disable():!i&&this.control.disabled&&this.control.enable(),(s=this._changeDetectorRef)==null||s.markForCheck()})}_getPath(n){return this._parent?Nx(n,this._parent):[n]}};u(e,"ngAcceptInputType_isDisabled"),u(e,"\u0275fac",function(o){return new(o||e)(f(no,9),f(Wt,10),f(bD,10),f(Zn,10),f(C,8),f(zf,8))}),u(e,"\u0275dir",U({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[Ne([Gx]),ne,tt]}));let t=e;return t})();var jH=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275dir",U({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1}));let t=e;return t})();function qx(t){return typeof t=="number"?t:parseInt(t,10)}function OD(t){return typeof t=="number"?t:parseFloat(t)}var _c=(()=>{let e=class e{_validator=dD;_onChange;_enabled;ngOnChanges(n){if(this.inputName in n){let o=this.normalizeInput(n[this.inputName].currentValue);this._enabled=this.enabled(o),this._validator=this._enabled?this.createValidator(o):dD,this._onChange&&this._onChange()}}validate(n){return this._validator(n)}registerOnValidatorChange(n){this._onChange=n}enabled(n){return n!=null}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275dir",U({type:e,features:[tt]}));let t=e;return t})(),Zx={provide:Wt,useExisting:$e(()=>Wf),multi:!0},Wf=(()=>{let e=class e extends _c{max;inputName="max";normalizeInput=n=>OD(n);createValidator=n=>Dx(n)};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",U({type:e,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(o,i){o&2&&ct("max",i._enabled?i.max:null)},inputs:{max:"max"},standalone:!1,features:[Ne([Zx]),ne]}));let t=e;return t})(),Yx={provide:Wt,useExisting:$e(()=>qf),multi:!0},qf=(()=>{let e=class e extends _c{min;inputName="min";normalizeInput=n=>OD(n);createValidator=n=>yx(n)};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",U({type:e,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(o,i){o&2&&ct("min",i._enabled?i.min:null)},inputs:{min:"min"},standalone:!1,features:[Ne([Yx]),ne]}));let t=e;return t})(),Qx={provide:Wt,useExisting:$e(()=>Kx),multi:!0};var Kx=(()=>{let e=class e extends _c{required;inputName="required";normalizeInput=cn;createValidator=n=>Ix;enabled(n){return n}};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",U({type:e,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(o,i){o&2&&ct("required",i._enabled?"":null)},inputs:{required:"required"},standalone:!1,features:[Ne([Qx]),ne]}));let t=e;return t})();var Xx={provide:Wt,useExisting:$e(()=>Jx),multi:!0},Jx=(()=>{let e=class e extends _c{maxlength;inputName="maxlength";normalizeInput=n=>qx(n);createValidator=n=>Cx(n)};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",U({type:e,selectors:[["","maxlength","","formControlName",""],["","maxlength","","formControl",""],["","maxlength","","ngModel",""]],hostVars:1,hostBindings:function(o,i){o&2&&ct("maxlength",i._enabled?i.maxlength:null)},inputs:{maxlength:"maxlength"},standalone:!1,features:[Ne([Xx]),ne]}));let t=e;return t})();var eA=(()=>{let e=class e{};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275mod",Re({type:e})),u(e,"\u0275inj",_e({}));let t=e;return t})();var LH=(()=>{let e=class e{static withConfig(n){var o;return{ngModule:e,providers:[{provide:zf,useValue:(o=n.callSetDisabledState)!=null?o:Gf}]}}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275mod",Re({type:e})),u(e,"\u0275inj",_e({imports:[eA]}));let t=e;return t})();var xc=t=>tA(t),Si=(t,e)=>(typeof t=="string"&&(e=t,t=void 0),xc(t).includes(e)),tA=(t=window)=>{if(typeof t>"u")return[];t.Ionic=t.Ionic||{};let e=t.Ionic.platforms;return e==null&&(e=t.Ionic.platforms=nA(t),e.forEach(r=>t.document.documentElement.classList.add(`plt-${r}`))),e},nA=t=>{let e=Xn.get("platform");return Object.keys(kD).filter(r=>{let n=e==null?void 0:e[r];return typeof n=="function"?n(t):kD[r](t)})},rA=t=>Ac(t)&&!PD(t),Zf=t=>!!(Yn(t,/iPad/i)||Yn(t,/Macintosh/i)&&Ac(t)),oA=t=>Yn(t,/iPhone/i),iA=t=>Yn(t,/iPhone|iPod/i)||Zf(t),FD=t=>Yn(t,/android|sink/i),sA=t=>FD(t)&&!Yn(t,/mobile/i),aA=t=>{let e=t.innerWidth,r=t.innerHeight,n=Math.min(e,r),o=Math.max(e,r);return n>390&&n<520&&o>620&&o<800},cA=t=>{let e=t.innerWidth,r=t.innerHeight,n=Math.min(e,r),o=Math.max(e,r);return Zf(t)||sA(t)||n>460&&n<820&&o>780&&o<1400},Ac=t=>fA(t,"(any-pointer:coarse)"),lA=t=>!Ac(t),PD=t=>jD(t)||LD(t),jD=t=>!!(t.cordova||t.phonegap||t.PhoneGap),LD=t=>{let e=t.Capacitor;return!!(e!=null&&e.isNative||e!=null&&e.isNativePlatform&&e.isNativePlatform())},uA=t=>Yn(t,/electron/i),dA=t=>{var e;return!!(!((e=t.matchMedia)===null||e===void 0)&&e.call(t,"(display-mode: standalone)").matches||t.navigator.standalone)},Yn=(t,e)=>e.test(t.navigator.userAgent),fA=(t,e)=>{var r;return(r=t.matchMedia)===null||r===void 0?void 0:r.call(t,e).matches},kD={ipad:Zf,iphone:oA,ios:iA,android:FD,phablet:aA,tablet:cA,cordova:jD,capacitor:LD,electron:uA,pwa:dA,mobile:Ac,mobileweb:rA,desktop:lA,hybrid:PD},hA,Yf=t=>t&&fh(t)||hA;var YH=t=>{try{if(t instanceof Xf)return t.value;if(!pA()||typeof t!="string"||t==="")return t;if(t.includes("onload="))return"";let e=document.createDocumentFragment(),r=document.createElement("div");e.appendChild(r),r.innerHTML=t,mA.forEach(s=>{let a=e.querySelectorAll(s);for(let c=a.length-1;c>=0;c--){let l=a[c];l.parentNode?l.parentNode.removeChild(l):e.removeChild(l);let d=Kf(l);for(let h=0;h<d.length;h++)Qf(d[h])}});let n=Kf(e);for(let s=0;s<n.length;s++)Qf(n[s]);let o=document.createElement("div");o.appendChild(e);let i=o.querySelector("div");return i!==null?i.innerHTML:o.innerHTML}catch(e){return Oi("sanitizeDOMString",e),""}},Qf=t=>{if(t.nodeType&&t.nodeType!==1)return;if(typeof NamedNodeMap<"u"&&!(t.attributes instanceof NamedNodeMap)){t.remove();return}for(let r=t.attributes.length-1;r>=0;r--){let n=t.attributes.item(r),o=n.name;if(!gA.includes(o.toLowerCase())){t.removeAttribute(o);continue}let i=n.value,s=t[o];(i!=null&&i.toLowerCase().includes("javascript:")||s!=null&&s.toLowerCase().includes("javascript:"))&&t.removeAttribute(o)}let e=Kf(t);for(let r=0;r<e.length;r++)Qf(e[r])},Kf=t=>t.children!=null?t.children:t.childNodes,pA=()=>{var t;let e=window,r=(t=e==null?void 0:e.Ionic)===null||t===void 0?void 0:t.config;return r?r.get?r.get("sanitizerEnabled",!0):r.sanitizerEnabled===!0||r.sanitizerEnabled===void 0:!0},gA=["class","id","href","src","name","slot"],mA=["script","style","iframe","meta","link","object","embed"],Xf=class{constructor(e){this.value=e}};var QH=!1;var XH=(t,e)=>typeof t=="string"&&t.length>0?Object.assign({"ion-color":!0,[`ion-color-${t}`]:!0},e):e,vA=t=>t!==void 0?(Array.isArray(t)?t:t.split(" ")).filter(r=>r!=null).map(r=>r.trim()).filter(r=>r!==""):[],JH=t=>{let e={};return vA(t).forEach(r=>e[r]=!0),e};var VD=()=>{let t,e;return{attachViewToDom:(c,l,...d)=>ve(void 0,[c,l,...d],function*(o,i,s={},a=[]){var h,g;t=o;let p;if(i){let y=typeof i=="string"?(h=t.ownerDocument)===null||h===void 0?void 0:h.createElement(i):i;a.forEach(x=>y.classList.add(x)),Object.assign(y,s),t.appendChild(y),p=y,yield new Promise(x=>vn(y,x))}else if(t.children.length>0&&(t.tagName==="ION-MODAL"||t.tagName==="ION-POPOVER")&&!(p=t.children[0]).classList.contains("ion-delegate-host")){let x=(g=t.ownerDocument)===null||g===void 0?void 0:g.createElement("div");x.classList.add("ion-delegate-host"),a.forEach(O=>x.classList.add(O)),x.append(...t.children),t.appendChild(x),p=x}let v=document.querySelector("ion-app")||document.body;return e=document.createComment("ionic teleport"),t.parentNode.insertBefore(e,t),v.appendChild(t),p!=null?p:t}),removeViewFromDom:()=>(t&&e&&(e.parentNode.insertBefore(t,e),e.remove()),Promise.resolve())}};var _i='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',BD=(t,e)=>{let r=t.querySelector(_i);zD(r,e!=null?e:t)},UD=(t,e)=>{let r=Array.from(t.querySelectorAll(_i)),n=r.length>0?r[r.length-1]:null;zD(n,e!=null?e:t)},zD=(t,e)=>{let r=t,n=t==null?void 0:t.shadowRoot;if(n&&(r=n.querySelector(_i)||t),r){let o=r.closest("ion-radio-group");o?o.setFocus():Lc(r)}else e.focus()},Jf=0,yA=0,Rc=new WeakMap,DA=t=>({create(r){return CA(t,r)},dismiss(r,n,o){return MA(document,r,n,t,o)},getTop(){return ve(this,null,function*(){return Ti(document,t)})}});var IA=DA("ion-loading");var f$=t=>{typeof document<"u"&&wA(document);let e=Jf++;t.overlayIndex=e},h$=t=>(t.hasAttribute("id")||(t.id=`ion-overlay-${++yA}`),t.id),CA=(t,e)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(t).then(()=>{let r=document.createElement(t);return r.classList.add("overlay-hidden"),Object.assign(r,Object.assign(Object.assign({},e),{hasController:!0})),WD(document).appendChild(r),new Promise(n=>vn(r,n))}):Promise.resolve(),bA=t=>t.classList.contains("overlay-hidden"),HD=(t,e)=>{let r=t,n=t==null?void 0:t.shadowRoot;n&&(r=n.querySelector(_i)||t),r?Lc(r):e.focus()},EA=(t,e)=>{let r=Ti(e,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),n=t.target;if(!r||!n||r.classList.contains(NA))return;let o=()=>{if(r===n)r.lastFocus=void 0;else if(n.tagName==="ION-TOAST")HD(r.lastFocus,r);else{let s=gh(r);if(!s.contains(n))return;let a=s.querySelector(".ion-overlay-wrapper");if(!a)return;if(a.contains(n)||n===s.querySelector("ion-backdrop"))r.lastFocus=n;else{let c=r.lastFocus;BD(a,r),c===e.activeElement&&UD(a,r),r.lastFocus=e.activeElement}}},i=()=>{if(r.contains(n))r.lastFocus=n;else if(n.tagName==="ION-TOAST")HD(r.lastFocus,r);else{let s=r.lastFocus;BD(r),s===e.activeElement&&UD(r),r.lastFocus=e.activeElement}};r.shadowRoot?i():o()},wA=t=>{Jf===0&&(Jf=1,t.addEventListener("focus",e=>{EA(e,t)},!0),t.addEventListener("ionBackButton",e=>{let r=Ti(t);r!=null&&r.backdropDismiss&&e.detail.register(vh,()=>{r.dismiss(void 0,$D)})}),mh()||t.addEventListener("keydown",e=>{if(e.key==="Escape"){let r=Ti(t);r!=null&&r.backdropDismiss&&r.dismiss(void 0,$D)}}))},MA=(t,e,r,n,o)=>{let i=Ti(t,n,o);return i?i.dismiss(e,r):Promise.reject("overlay does not exist")},SA=(t,e)=>(e===void 0&&(e="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(t.querySelectorAll(e)).filter(r=>r.overlayIndex>0)),Nc=(t,e)=>SA(t,e).filter(r=>!bA(r)),Ti=(t,e,r)=>{let n=Nc(t,e);return r===void 0?n[n.length-1]:n.find(o=>o.id===r)},GD=(t=!1)=>{let r=WD(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");r&&(t?r.setAttribute("aria-hidden","true"):r.removeAttribute("aria-hidden"))},p$=(t,e,r,n,o)=>ve(void 0,null,function*(){var i,s;if(t.presented)return;t.el.tagName!=="ION-TOAST"&&(GD(!0),document.body.classList.add(zc)),AA(t.el),ZD(t.el),t.presented=!0,t.willPresent.emit(),(i=t.willPresentShorthand)===null||i===void 0||i.emit();let a=Yf(t),c=t.enterAnimation?t.enterAnimation:Xn.get(e,a==="ios"?r:n);(yield qD(t,c,t.el,o))&&(t.didPresent.emit(),(s=t.didPresentShorthand)===null||s===void 0||s.emit()),t.el.tagName!=="ION-TOAST"&&TA(t.el),t.keyboardClose&&(document.activeElement===null||!t.el.contains(document.activeElement))&&t.el.focus(),t.el.removeAttribute("aria-hidden")}),TA=t=>ve(void 0,null,function*(){let e=document.activeElement;if(!e)return;let r=e==null?void 0:e.shadowRoot;r&&(e=r.querySelector(_i)||e),yield t.onDidDismiss(),(document.activeElement===null||document.activeElement===document.body)&&e.focus()}),g$=(t,e,r,n,o,i,s)=>ve(void 0,null,function*(){var a,c;if(!t.presented)return!1;let d=(qt!==void 0?Nc(qt):[]).filter(g=>g.tagName!=="ION-TOAST");d.length===1&&d[0].id===t.el.id&&(GD(!1),document.body.classList.remove(zc)),t.presented=!1;try{ZD(t.el),t.el.style.setProperty("pointer-events","none"),t.willDismiss.emit({data:e,role:r}),(a=t.willDismissShorthand)===null||a===void 0||a.emit({data:e,role:r});let g=Yf(t),p=t.leaveAnimation?t.leaveAnimation:Xn.get(n,g==="ios"?o:i);r!==xA&&(yield qD(t,p,t.el,s)),t.didDismiss.emit({data:e,role:r}),(c=t.didDismissShorthand)===null||c===void 0||c.emit({data:e,role:r}),(Rc.get(t)||[]).forEach(y=>y.destroy()),Rc.delete(t),t.el.classList.add("overlay-hidden"),t.el.style.removeProperty("pointer-events"),t.el.lastFocus!==void 0&&(t.el.lastFocus=void 0)}catch(g){Oi(`[${t.el.tagName.toLowerCase()}] - `,g)}return t.el.remove(),RA(),!0}),WD=t=>t.querySelector("ion-app")||t.body,qD=(t,e,r,n)=>ve(void 0,null,function*(){r.classList.remove("overlay-hidden");let o=t.el,i=e(o,n);(!t.animated||!Xn.getBoolean("animated",!0))&&i.duration(0),t.keyboardClose&&i.beforeAddWrite(()=>{let a=r.ownerDocument.activeElement;a!=null&&a.matches("input,ion-input, ion-textarea")&&a.blur()});let s=Rc.get(t)||[];return Rc.set(t,[...s,i]),yield i.play(),!0}),m$=(t,e)=>{let r,n=new Promise(o=>r=o);return _A(t,e,o=>{r(o.detail)}),n},_A=(t,e,r)=>{let n=o=>{ph(t,e,n),r(o)};hh(t,e,n)};var $D="backdrop",xA="gesture";var v$=t=>{let e=!1,r,n=VD(),o=(a=!1)=>{if(r&&!a)return{delegate:r,inline:e};let{el:c,hasController:l,delegate:d}=t;return e=c.parentNode!==null&&!l,r=e?d||n:d,{inline:e,delegate:r}};return{attachViewToDom:a=>ve(void 0,null,function*(){let{delegate:c}=o(!0);if(c)return yield c.attachViewToDom(t.el,a);let{hasController:l}=t;if(l&&a!==void 0)throw new Error("framework delegate is missing");return null}),removeViewFromDom:()=>{let{delegate:a}=o();a&&t.el!==void 0&&a.removeViewFromDom(t.el.parentElement,t.el)}}},y$=()=>{let t,e=()=>{t&&(t(),t=void 0)};return{addClickListener:(n,o)=>{e();let i=o!==void 0?document.getElementById(o):null;if(!i){jc(`[${n.tagName.toLowerCase()}] - A trigger element with the ID "${o}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,n);return}t=((a,c)=>{let l=()=>{c.present()};return a.addEventListener("click",l),()=>{a.removeEventListener("click",l)}})(i,n)},removeClickListener:e}},ZD=t=>{qt!==void 0&&Si("android")&&t.setAttribute("aria-hidden","true")},AA=t=>{var e;if(qt===void 0)return;let r=Nc(qt);for(let n=r.length-1;n>=0;n--){let o=r[n],i=(e=r[n+1])!==null&&e!==void 0?e:t;(i.hasAttribute("aria-hidden")||i.tagName!=="ION-TOAST")&&o.setAttribute("aria-hidden","true")}},RA=()=>{if(qt===void 0)return;let t=Nc(qt);for(let e=t.length-1;e>=0;e--){let r=t[e];if(r.removeAttribute("aria-hidden"),r.tagName!=="ION-TOAST")break}},NA="ion-disable-focus-trap";var OA=["tabsInner"];var XD=(()=>{let e=class e{doc;_readyPromise;win;backButton=new ee;keyboardDidShow=new ee;keyboardDidHide=new ee;pause=new ee;resume=new ee;resize=new ee;constructor(n,o){this.doc=n,o.run(()=>{var s;this.win=n.defaultView,this.backButton.subscribeWithPriority=function(a,c){return this.subscribe(l=>l.register(a,d=>o.run(()=>c(d))))},oo(this.pause,n,"pause",o),oo(this.resume,n,"resume",o),oo(this.backButton,n,"ionBackButton",o),oo(this.resize,this.win,"resize",o),oo(this.keyboardDidShow,this.win,"ionKeyboardDidShow",o),oo(this.keyboardDidHide,this.win,"ionKeyboardDidHide",o);let i;this._readyPromise=new Promise(a=>{i=a}),(s=this.win)!=null&&s.cordova?n.addEventListener("deviceready",()=>{i("cordova")},{once:!0}):i("dom")})}is(n){return Si(this.win,n)}platforms(){return xc(this.win)}ready(){return this._readyPromise}get isRTL(){return this.doc.dir==="rtl"}getQueryParam(n){return kA(this.win.location.href,n)}isLandscape(){return!this.isPortrait()}isPortrait(){var n,o;return(o=(n=this.win).matchMedia)==null?void 0:o.call(n,"(orientation: portrait)").matches}testUserAgent(n){let o=this.win.navigator;return!!(o!=null&&o.userAgent&&o.userAgent.indexOf(n)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}};u(e,"\u0275fac",function(o){return new(o||e)(k(ce),k(m))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),kA=(t,e)=>{e=e.replace(/[[\]\\]/g,"\\$&");let n=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(t);return n?decodeURIComponent(n[1].replace(/\+/g," ")):null},oo=(t,e,r,n)=>{e&&e.addEventListener(r,o=>{n.run(()=>{let i=o!=null?o.detail:void 0;t.next(i)})})},gn=(()=>{let e=class e{location;serializer;router;topOutlet;direction=YD;animated=QD;animationBuilder;guessDirection="forward";guessAnimation;lastNavId=-1;constructor(n,o,i,s){this.location=o,this.serializer=i,this.router=s,s&&s.events.subscribe(a=>{if(a instanceof At){let c=a.restoredState?a.restoredState.navigationId:a.id;this.guessDirection=this.guessAnimation=c<this.lastNavId?"back":"forward",this.lastNavId=this.guessDirection==="forward"?a.id:c}}),n.backButton.subscribeWithPriority(0,a=>{this.pop(),a()})}navigateForward(n,o={}){return this.setDirection("forward",o.animated,o.animationDirection,o.animation),this.navigate(n,o)}navigateBack(n,o={}){return this.setDirection("back",o.animated,o.animationDirection,o.animation),this.navigate(n,o)}navigateRoot(n,o={}){return this.setDirection("root",o.animated,o.animationDirection,o.animation),this.navigate(n,o)}back(n={animated:!0,animationDirection:"back"}){return this.setDirection("back",n.animated,n.animationDirection,n.animation),this.location.back()}pop(){return ve(this,null,function*(){let n=this.topOutlet;for(;n;){if(yield n.pop())return!0;n=n.parentOutlet}return!1})}setDirection(n,o,i,s){this.direction=n,this.animated=FA(n,o,i),this.animationBuilder=s}setTopOutlet(n){this.topOutlet=n}consumeTransition(){let n="root",o,i=this.animationBuilder;return this.direction==="auto"?(n=this.guessDirection,o=this.guessAnimation):(o=this.animated,n=this.direction),this.direction=YD,this.animated=QD,this.animationBuilder=void 0,{direction:n,animation:o,animationBuilder:i}}navigate(n,o){if(Array.isArray(n))return this.router.navigate(n,o);{let i=this.serializer.parse(n.toString());return o.queryParams!==void 0&&(i.queryParams=b({},o.queryParams)),o.fragment!==void 0&&(i.fragment=o.fragment),this.router.navigateByUrl(i,o)}}};u(e,"\u0275fac",function(o){return new(o||e)(k(XD),k(We),k(Gt),k(Ce,8))}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),FA=(t,e,r)=>{if(e!==!1){if(r!==void 0)return r;if(t==="forward"||t==="back")return t;if(t==="root"&&e===!0)return"forward"}},YD="auto",QD=void 0,Ai=(()=>{let e=class e{get(n,o){let i=eh();return i?i.get(n,o):null}getBoolean(n,o){let i=eh();return i?i.getBoolean(n,o):!1}getNumber(n,o){let i=eh();return i?i.getNumber(n,o):0}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),Oc=new N("USERCONFIG"),eh=()=>{if(typeof window<"u"){let t=window.Ionic;if(t!=null&&t.config)return t.config}return null},xi=class{data;constructor(e={}){this.data=e,console.warn("[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.")}get(e){return this.data[e]}},mn=(()=>{let e=class e{zone=I(m);applicationRef=I(Et);config=I(Oc);create(n,o,i){var s;return new nh(n,o,this.applicationRef,this.zone,i,(s=this.config.useSetInputAPI)!=null?s:!1)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),nh=class{environmentInjector;injector;applicationRef;zone;elementReferenceKey;enableSignalsSupport;elRefMap=new WeakMap;elEventsMap=new WeakMap;constructor(e,r,n,o,i,s){this.environmentInjector=e,this.injector=r,this.applicationRef=n,this.zone=o,this.elementReferenceKey=i,this.enableSignalsSupport=s}attachViewToDom(e,r,n,o){return this.zone.run(()=>new Promise(i=>{let s=b({},n);this.elementReferenceKey!==void 0&&(s[this.elementReferenceKey]=e);let a=PA(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,e,r,s,o,this.elementReferenceKey,this.enableSignalsSupport);i(a)}))}removeViewFromDom(e,r){return this.zone.run(()=>new Promise(n=>{let o=this.elRefMap.get(r);if(o){o.destroy(),this.elRefMap.delete(r);let i=this.elEventsMap.get(r);i&&(i(),this.elEventsMap.delete(r))}n()}))}},PA=(t,e,r,n,o,i,s,a,c,l,d,h)=>{let g=te.create({providers:LA(c),parent:r}),p=Rv(a,{environmentInjector:e,elementInjector:g}),v=p.instance,y=p.location.nativeElement;if(c)if(d&&v[d]!==void 0&&console.error(`[Ionic Error]: ${d} is a reserved property when using ${s.tagName.toLowerCase()}. Rename or remove the "${d}" property from ${a.name}.`),h===!0&&p.setInput!==void 0){let O=c,{modal:X,popover:Ue}=O,he=Pc(O,["modal","popover"]);for(let Ot in he)p.setInput(Ot,he[Ot]);X!==void 0&&Object.assign(v,{modal:X}),Ue!==void 0&&Object.assign(v,{popover:Ue})}else Object.assign(v,c);if(l)for(let X of l)y.classList.add(X);let x=JD(t,v,y);return s.appendChild(y),n.attachView(p.hostView),o.set(y,p),i.set(y,x),y},jA=[Vc,Bc,Uc,Hc,$c],JD=(t,e,r)=>t.run(()=>{let n=jA.filter(o=>typeof e[o]=="function").map(o=>{let i=s=>e[o](s.detail);return r.addEventListener(o,i),()=>r.removeEventListener(o,i)});return()=>n.forEach(o=>o())}),KD=new N("NavParamsToken"),LA=t=>[{provide:KD,useValue:t},{provide:xi,useFactory:VA,deps:[KD]}],VA=t=>new xi(t),BA=(t,e)=>{let r=t.prototype;e.forEach(n=>{Object.defineProperty(r,n,{get(){return this.el[n]},set(o){this.z.runOutsideAngular(()=>this.el[n]=o)}})})},UA=(t,e)=>{let r=t.prototype;e.forEach(n=>{r[n]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[n].apply(this.el,o))}})},ah=(t,e,r)=>{r.forEach(n=>t[n]=En(e,n))};function kc(t){return function(r){let{defineCustomElementFn:n,inputs:o,methods:i}=t;return n!==void 0&&n(),o&&BA(r,o),i&&UA(r,i),r}}var HA=["alignment","animated","arrow","keepContentsMounted","backdropDismiss","cssClass","dismissOnSelect","enterAnimation","event","focusTrap","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","translucent","trigger","triggerAction","reference","size","side"],$A=["present","dismiss","onDidDismiss","onWillDismiss"],eI=(()=>{var e;let t=(e=class{z;template;isCmpOpen=!1;el;constructor(n,o,i){this.z=i,this.el=o.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,n.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,n.detectChanges()}),ah(this,this.el,["ionPopoverDidPresent","ionPopoverWillPresent","ionPopoverWillDismiss","ionPopoverDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275dir",U({type:e,selectors:[["ion-popover"]],contentQueries:function(o,i,s){if(o&1&&an(s,bt,5),o&2){let a;lt(a=ut())&&(i.template=a.first)}},inputs:{alignment:"alignment",animated:"animated",arrow:"arrow",keepContentsMounted:"keepContentsMounted",backdropDismiss:"backdropDismiss",cssClass:"cssClass",dismissOnSelect:"dismissOnSelect",enterAnimation:"enterAnimation",event:"event",focusTrap:"focusTrap",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger",triggerAction:"triggerAction",reference:"reference",size:"size",side:"side"},standalone:!1})),e);return t=M([kc({inputs:HA,methods:$A})],t),t})(),zA=["animated","keepContentsMounted","backdropBreakpoint","backdropDismiss","breakpoints","canDismiss","cssClass","enterAnimation","expandToScroll","event","focusTrap","handle","handleBehavior","initialBreakpoint","isOpen","keyboardClose","leaveAnimation","mode","presentingElement","showBackdrop","translucent","trigger"],GA=["present","dismiss","onDidDismiss","onWillDismiss","setCurrentBreakpoint","getCurrentBreakpoint"],tI=(()=>{var e;let t=(e=class{z;template;isCmpOpen=!1;el;constructor(n,o,i){this.z=i,this.el=o.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,n.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,n.detectChanges()}),ah(this,this.el,["ionModalDidPresent","ionModalWillPresent","ionModalWillDismiss","ionModalDidDismiss","ionBreakpointDidChange","didPresent","willPresent","willDismiss","didDismiss"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275dir",U({type:e,selectors:[["ion-modal"]],contentQueries:function(o,i,s){if(o&1&&an(s,bt,5),o&2){let a;lt(a=ut())&&(i.template=a.first)}},inputs:{animated:"animated",keepContentsMounted:"keepContentsMounted",backdropBreakpoint:"backdropBreakpoint",backdropDismiss:"backdropDismiss",breakpoints:"breakpoints",canDismiss:"canDismiss",cssClass:"cssClass",enterAnimation:"enterAnimation",expandToScroll:"expandToScroll",event:"event",focusTrap:"focusTrap",handle:"handle",handleBehavior:"handleBehavior",initialBreakpoint:"initialBreakpoint",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",presentingElement:"presentingElement",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger"},standalone:!1})),e);return t=M([kc({inputs:zA,methods:GA})],t),t})(),WA=(t,e,r)=>r==="root"?nI(t,e):r==="forward"?qA(t,e):ZA(t,e),nI=(t,e)=>(t=t.filter(r=>r.stackId!==e.stackId),t.push(e),t),qA=(t,e)=>(t.indexOf(e)>=0?t=t.filter(n=>n.stackId!==e.stackId||n.id<=e.id):t.push(e),t),ZA=(t,e)=>t.indexOf(e)>=0?t.filter(n=>n.stackId!==e.stackId||n.id<=e.id):nI(t,e),rh=(t,e)=>{let r=t.createUrlTree(["."],{relativeTo:e});return t.serializeUrl(r)},rI=(t,e)=>e?t.stackId!==e.stackId:!0,YA=(t,e)=>{if(!t)return;let r=oI(e);for(let n=0;n<r.length;n++){if(n>=t.length)return r[n];if(r[n]!==t[n])return}},oI=t=>t.split("/").map(e=>e.trim()).filter(e=>e!==""),iI=t=>{t&&(t.ref.destroy(),t.unlistenEvents())},oh=class{containerEl;router;navCtrl;zone;location;views=[];runningTask;skipTransition=!1;tabsPrefix;activeView;nextId=0;constructor(e,r,n,o,i,s){this.containerEl=r,this.router=n,this.navCtrl=o,this.zone=i,this.location=s,this.tabsPrefix=e!==void 0?oI(e):void 0}createView(e,r){var s;let n=rh(this.router,r),o=(s=e==null?void 0:e.location)==null?void 0:s.nativeElement,i=JD(this.zone,e.instance,o);return{id:this.nextId++,stackId:YA(this.tabsPrefix,n),unlistenEvents:i,element:o,ref:e,url:n}}getExistingView(e){let r=rh(this.router,e),n=this.views.find(o=>o.url===r);return n&&n.ref.changeDetectorRef.reattach(),n}setActive(e){var v,y;let r=this.navCtrl.consumeTransition(),{direction:n,animation:o,animationBuilder:i}=r,s=this.activeView,a=rI(e,s);a&&(n="back",o=void 0);let c=this.views.slice(),l,d=this.router;d.getCurrentNavigation?l=d.getCurrentNavigation():(v=d.navigations)!=null&&v.value&&(l=d.navigations.value),(y=l==null?void 0:l.extras)!=null&&y.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);let h=this.views.includes(e),g=this.insertView(e,n);h||e.ref.changeDetectorRef.detectChanges();let p=e.animationBuilder;return i===void 0&&n==="back"&&!a&&p!==void 0&&(i=p),s&&(s.animationBuilder=i),this.zone.runOutsideAngular(()=>this.wait(()=>(s&&s.ref.changeDetectorRef.detach(),e.ref.changeDetectorRef.reattach(),this.transition(e,s,o,this.canGoBack(1),!1,i).then(()=>QA(e,g,c,this.location,this.zone)).then(()=>({enteringView:e,direction:n,animation:o,tabSwitch:a})))))}canGoBack(e,r=this.getActiveStackId()){return this.getStack(r).length>e}pop(e,r=this.getActiveStackId()){return this.zone.run(()=>{var c,l;let n=this.getStack(r);if(n.length<=e)return Promise.resolve(!1);let o=n[n.length-e-1],i=o.url,s=o.savedData;if(s){let d=s.get("primary");(l=(c=d==null?void 0:d.route)==null?void 0:c._routerState)!=null&&l.snapshot.url&&(i=d.route._routerState.snapshot.url)}let{animationBuilder:a}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(i,L(b({},o.savedExtras),{animation:a})).then(()=>!0)})}startBackTransition(){let e=this.activeView;if(e){let r=this.getStack(e.stackId),n=r[r.length-2],o=n.animationBuilder;return this.wait(()=>this.transition(n,e,"back",this.canGoBack(2),!0,o))}return Promise.resolve()}endBackTransition(e){e?(this.skipTransition=!0,this.pop(1)):this.activeView&&sI(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(e){let r=this.getStack(e);return r.length>0?r[r.length-1]:void 0}getRootUrl(e){let r=this.getStack(e);return r.length>0?r[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return this.runningTask!==void 0}destroy(){this.containerEl=void 0,this.views.forEach(iI),this.activeView=void 0,this.views=[]}getStack(e){return this.views.filter(r=>r.stackId===e)}insertView(e,r){return this.activeView=e,this.views=WA(this.views,e,r),this.views.slice()}transition(e,r,n,o,i,s){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(r===e)return Promise.resolve(!1);let a=e?e.element:void 0,c=r?r.element:void 0,l=this.containerEl;return a&&a!==c&&(a.classList.add("ion-page"),a.classList.add("ion-page-invisible"),l.commit)?l.commit(a,c,{duration:n===void 0?0:void 0,direction:n,showGoBack:o,progressAnimation:i,animationBuilder:s}):Promise.resolve(!1)}wait(e){return ve(this,null,function*(){this.runningTask!==void 0&&(yield this.runningTask,this.runningTask=void 0);let r=this.runningTask=e();return r.finally(()=>this.runningTask=void 0),r})}},QA=(t,e,r,n,o)=>typeof requestAnimationFrame=="function"?new Promise(i=>{requestAnimationFrame(()=>{sI(t,e,r,n,o),i()})}):Promise.resolve(),sI=(t,e,r,n,o)=>{o.run(()=>r.filter(i=>!e.includes(i)).forEach(iI)),e.forEach(i=>{let a=n.path().split("?")[0].split("#")[0];if(i!==t&&i.url!==a){let c=i.element;c.setAttribute("aria-hidden","true"),c.classList.add("ion-page-hidden"),i.ref.changeDetectorRef.detach()}})},ch=(()=>{let e=class e{parentOutlet;nativeEl;activatedView=null;tabsPrefix;_swipeGesture;stackCtrl;proxyMap=new WeakMap;currentActivatedRoute$=new pe(null);activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=j;stackWillChange=new se;stackDidChange=new se;activateEvents=new se;deactivateEvents=new se;parentContexts=I(Nt);location=I(ze);environmentInjector=I(ae);inputBinder=I(aI,{optional:!0});supportsBindingToComponentInputs=!0;config=I(Ai);navCtrl=I(gn);set animation(n){this.nativeEl.animation=n}set animated(n){this.nativeEl.animated=n}set swipeGesture(n){this._swipeGesture=n,this.nativeEl.swipeHandler=n?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:o=>this.stackCtrl.endBackTransition(o)}:void 0}constructor(n,o,i,s,a,c,l,d){this.parentOutlet=d,this.nativeEl=s.nativeElement,this.name=n||j,this.tabsPrefix=o==="true"?rh(a,l):void 0,this.stackCtrl=new oh(this.tabsPrefix,this.nativeEl,a,this.navCtrl,c,i),this.parentContexts.onChildOutletCreated(this.name,this)}ngOnDestroy(){var n;this.stackCtrl.destroy(),(n=this.inputBinder)==null||n.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){let n=this.getContext();n!=null&&n.route&&this.activateWith(n.route,n.injector)}new Promise(n=>vn(this.nativeEl,n)).then(()=>{this._swipeGesture===void 0&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled",this.nativeEl.mode==="ios"))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(n,o){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){let o=this.getContext();this.activatedView.savedData=new Map(o.children.contexts);let i=this.activatedView.savedData.get("primary");if(i&&o.route&&(i.route=b({},o.route)),this.activatedView.savedExtras={},o.route){let s=o.route.snapshot;this.activatedView.savedExtras.queryParams=s.queryParams,this.activatedView.savedExtras.fragment=s.fragment}}let n=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,o){var c,l;if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=n;let i,s=this.stackCtrl.getExistingView(n);if(s){i=this.activated=s.ref;let d=s.savedData;if(d){let h=this.getContext();h.children.contexts=d}this.updateActivatedRouteProxy(i.instance,n)}else{let d=n._futureSnapshot,h=this.parentContexts.getOrCreateContext(this.name).children,g=new pe(null),p=this.createActivatedRouteProxy(g,n),v=new ih(p,h,this.location.injector),y=(c=d.routeConfig.component)!=null?c:d.component;i=this.activated=this.outletContent.createComponent(y,{index:this.outletContent.length,injector:v,environmentInjector:o!=null?o:this.environmentInjector}),g.next(i.instance),s=this.stackCtrl.createView(this.activated,n),this.proxyMap.set(i.instance,p),this.currentActivatedRoute$.next({component:i.instance,activatedRoute:n})}(l=this.inputBinder)==null||l.bindActivatedRouteToOutletComponent(this),this.activatedView=s,this.navCtrl.setTopOutlet(this);let a=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:s,tabSwitch:rI(s,a)}),this.stackCtrl.setActive(s).then(d=>{this.activateEvents.emit(i.instance),this.stackDidChange.emit(d)})}canGoBack(n=1,o){return this.stackCtrl.canGoBack(n,o)}pop(n=1,o){return this.stackCtrl.pop(n,o)}getLastUrl(n){let o=this.stackCtrl.getLastUrl(n);return o?o.url:void 0}getLastRouteView(n){return this.stackCtrl.getLastUrl(n)}getRootView(n){return this.stackCtrl.getRootUrl(n)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(n,o){let i=new Ae;return i._futureSnapshot=o._futureSnapshot,i._routerState=o._routerState,i.snapshot=o.snapshot,i.outlet=o.outlet,i.component=o.component,i._paramMap=this.proxyObservable(n,"paramMap"),i._queryParamMap=this.proxyObservable(n,"queryParamMap"),i.url=this.proxyObservable(n,"url"),i.params=this.proxyObservable(n,"params"),i.queryParams=this.proxyObservable(n,"queryParams"),i.fragment=this.proxyObservable(n,"fragment"),i.data=this.proxyObservable(n,"data"),i}proxyObservable(n,o){return n.pipe(ye(i=>!!i),De(i=>this.currentActivatedRoute$.pipe(ye(s=>s!==null&&s.component===i),De(s=>s&&s.activatedRoute[o]),dl())))}updateActivatedRouteProxy(n,o){let i=this.proxyMap.get(n);if(!i)throw new Error("Could not find activated route proxy for view");i._futureSnapshot=o._futureSnapshot,i._routerState=o._routerState,i.snapshot=o.snapshot,i.outlet=o.outlet,i.component=o.component,this.currentActivatedRoute$.next({component:n,activatedRoute:o})}};u(e,"\u0275fac",function(o){return new(o||e)(Mt("name"),Mt("tabs"),f(We),f(D),f(Ce),f(m),f(Ae),f(e,12))}),u(e,"\u0275dir",U({type:e,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"],standalone:!1}));let t=e;return t})(),ih=class{route;childContexts;parent;constructor(e,r,n){this.route=e,this.childContexts=r,this.parent=n}get(e,r){return e===Ae?this.route:e===Nt?this.childContexts:this.parent.get(e,r)}},aI=new N(""),KA=(()=>{let e=class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){var o;(o=this.outletDataSubscriptions.get(n))==null||o.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:o}=n,i=bn([o.queryParams,o.params,o.data]).pipe(De(([s,a,c],l)=>(c=b(b(b({},s),a),c),l===0?F(c):Promise.resolve(c)))).subscribe(s=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==o||o.component===null){this.unsubscribeFromRouteData(n);return}let a=Sa(o.component);if(!a){this.unsubscribeFromRouteData(n);return}for(let{templateName:c}of a.inputs)n.activatedComponentRef.setInput(c,s[c])});this.outletDataSubscriptions.set(n,i)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})(),cI=()=>({provide:aI,useFactory:XA,deps:[Ce]});function XA(t){return t!=null&&t.componentInputBindingEnabled?new KA:null}var JA=["color","defaultHref","disabled","icon","mode","routerAnimation","text","type"],lI=(()=>{var e;let t=(e=class{routerOutlet;navCtrl;config;r;z;el;constructor(n,o,i,s,a,c){this.routerOutlet=n,this.navCtrl=o,this.config=i,this.r=s,this.z=a,c.detach(),this.el=this.r.nativeElement}onClick(n){var i;let o=this.defaultHref||this.config.get("backButtonDefaultHref");(i=this.routerOutlet)!=null&&i.canGoBack()?(this.navCtrl.setDirection("back",void 0,void 0,this.routerAnimation),this.routerOutlet.pop(),n.preventDefault()):o!=null&&(this.navCtrl.navigateBack(o,{animation:this.routerAnimation}),n.preventDefault())}},u(e,"\u0275fac",function(o){return new(o||e)(f(ch,8),f(gn),f(Ai),f(D),f(m),f(C))}),u(e,"\u0275dir",U({type:e,hostBindings:function(o,i){o&1&&Ie("click",function(a){return i.onClick(a)})},inputs:{color:"color",defaultHref:"defaultHref",disabled:"disabled",icon:"icon",mode:"mode",routerAnimation:"routerAnimation",text:"text",type:"type"},standalone:!1})),e);return t=M([kc({inputs:JA})],t),t})(),uI=(()=>{let e=class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(n,o,i,s,a){this.locationStrategy=n,this.navCtrl=o,this.elementRef=i,this.router=s,this.routerLink=a}ngOnInit(){this.updateTargetUrlAndHref(),this.updateTabindex()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTabindex(){let n=["ION-BACK-BUTTON","ION-BREADCRUMB","ION-BUTTON","ION-CARD","ION-FAB-BUTTON","ION-ITEM","ION-ITEM-OPTION","ION-MENU-BUTTON","ION-SEGMENT-BUTTON","ION-TAB-BUTTON"],o=this.elementRef.nativeElement;n.includes(o.tagName)&&o.getAttribute("tabindex")==="0"&&o.removeAttribute("tabindex")}updateTargetUrlAndHref(){var n;if((n=this.routerLink)!=null&&n.urlTree){let o=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=o}}onClick(n){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation),n.preventDefault()}};u(e,"\u0275fac",function(o){return new(o||e)(f(Ve),f(gn),f(D),f(Ce),f(Di,8))}),u(e,"\u0275dir",U({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],hostBindings:function(o,i){o&1&&Ie("click",function(a){return i.onClick(a)})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[tt]}));let t=e;return t})(),dI=(()=>{let e=class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(n,o,i,s,a){this.locationStrategy=n,this.navCtrl=o,this.elementRef=i,this.router=s,this.routerLink=a}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){var n;if((n=this.routerLink)!=null&&n.urlTree){let o=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=o}}onClick(){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation)}};u(e,"\u0275fac",function(o){return new(o||e)(f(Ve),f(gn),f(D),f(Ce),f(Di,8))}),u(e,"\u0275dir",U({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],hostBindings:function(o,i){o&1&&Ie("click",function(){return i.onClick()})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[tt]}));let t=e;return t})(),eR=["animated","animation","root","rootParams","swipeGesture"],tR=["push","insert","insertPages","pop","popTo","popToRoot","removeIndex","setRoot","setPages","getActive","getByIndex","canGoBack","getPrevious"],fI=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i,s,a,c){this.z=a,c.detach(),this.el=n.nativeElement,n.nativeElement.delegate=s.create(o,i),ah(this,this.el,["ionNavDidChange","ionNavWillChange"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(D),f(ae),f(te),f(mn),f(m),f(C))}),u(e,"\u0275dir",U({type:e,inputs:{animated:"animated",animation:"animation",root:"root",rootParams:"rootParams",swipeGesture:"swipeGesture"},standalone:!1})),e);return t=M([kc({inputs:eR,methods:tR})],t),t})(),hI=(()=>{let e=class e{navCtrl;tabsInner;ionTabsWillChange=new se;ionTabsDidChange=new se;tabBarSlot="bottom";hasTab=!1;selectedTab;leavingTab;constructor(n){this.navCtrl=n}ngAfterViewInit(){let n=this.tabs.length>0?this.tabs.first:void 0;n&&(this.hasTab=!0,this.setActiveTab(n.tab),this.tabSwitch())}ngAfterContentInit(){this.detectSlotChanges()}ngAfterContentChecked(){this.detectSlotChanges()}onStackWillChange({enteringView:n,tabSwitch:o}){let i=n.stackId;o&&i!==void 0&&this.ionTabsWillChange.emit({tab:i})}onStackDidChange({enteringView:n,tabSwitch:o}){let i=n.stackId;o&&i!==void 0&&(this.tabBar&&(this.tabBar.selectedTab=i),this.ionTabsDidChange.emit({tab:i}))}select(n){let o=typeof n=="string",i=o?n:n.detail.tab;if(this.hasTab){this.setActiveTab(i),this.tabSwitch();return}let s=this.outlet.getActiveStackId()===i,a=`${this.outlet.tabsPrefix}/${i}`;if(o||n.stopPropagation(),s){let c=this.outlet.getActiveStackId(),l=this.outlet.getLastRouteView(c);if((l==null?void 0:l.url)===a)return;let d=this.outlet.getRootView(i),h=d&&a===d.url&&d.savedExtras;return this.navCtrl.navigateRoot(a,L(b({},h),{animated:!0,animationDirection:"back"}))}else{let c=this.outlet.getLastRouteView(i),l=(c==null?void 0:c.url)||a,d=c==null?void 0:c.savedExtras;return this.navCtrl.navigateRoot(l,L(b({},d),{animated:!0,animationDirection:"back"}))}}setActiveTab(n){let i=this.tabs.find(s=>s.tab===n);if(!i){console.error(`[Ionic Error]: Tab with id: "${n}" does not exist`);return}this.leavingTab=this.selectedTab,this.selectedTab=i,this.ionTabsWillChange.emit({tab:n}),i.el.active=!0}tabSwitch(){let{selectedTab:n,leavingTab:o}=this;this.tabBar&&n&&(this.tabBar.selectedTab=n.tab),(o==null?void 0:o.tab)!==(n==null?void 0:n.tab)&&o!=null&&o.el&&(o.el.active=!1),n&&this.ionTabsDidChange.emit({tab:n.tab})}getSelected(){var n;return this.hasTab?(n=this.selectedTab)==null?void 0:n.tab:this.outlet.getActiveStackId()}detectSlotChanges(){this.tabBars.forEach(n=>{let o=n.el.getAttribute("slot");o!==this.tabBarSlot&&(this.tabBarSlot=o,this.relocateTabBar())})}relocateTabBar(){let n=this.tabBar.el;this.tabBarSlot==="top"?this.tabsInner.nativeElement.before(n):this.tabsInner.nativeElement.after(n)}};u(e,"\u0275fac",function(o){return new(o||e)(f(gn))}),u(e,"\u0275dir",U({type:e,selectors:[["ion-tabs"]],viewQuery:function(o,i){if(o&1&&Uo(OA,7,D),o&2){let s;lt(s=ut())&&(i.tabsInner=s.first)}},hostBindings:function(o,i){o&1&&Ie("ionTabButtonClick",function(a){return i.select(a)})},outputs:{ionTabsWillChange:"ionTabsWillChange",ionTabsDidChange:"ionTabsDidChange"},standalone:!1}));let t=e;return t})(),lh=t=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(t):typeof requestAnimationFrame=="function"?requestAnimationFrame(t):setTimeout(t),Ri=(()=>{let e=class e{injector;elementRef;onChange=()=>{};onTouched=()=>{};lastValue;statusChanges;constructor(n,o){this.injector=n,this.elementRef=o}writeValue(n){this.elementRef.nativeElement.value=this.lastValue=n,Qn(this.elementRef)}handleValueChange(n,o){n===this.elementRef.nativeElement&&(o!==this.lastValue&&(this.lastValue=o,this.onChange(o)),Qn(this.elementRef))}_handleBlurEvent(n){n===this.elementRef.nativeElement?(this.onTouched(),Qn(this.elementRef)):n.closest("ion-radio-group")===this.elementRef.nativeElement&&this.onTouched()}registerOnChange(n){this.onChange=n}registerOnTouched(n){this.onTouched=n}setDisabledState(n){this.elementRef.nativeElement.disabled=n}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let n;try{n=this.injector.get(qn)}catch{}if(!n)return;n.statusChanges&&(this.statusChanges=n.statusChanges.subscribe(()=>Qn(this.elementRef)));let o=n.control;o&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(s=>{if(typeof o[s]<"u"){let a=o[s].bind(o);o[s]=(...c)=>{a(...c),Qn(this.elementRef)}}})}};u(e,"\u0275fac",function(o){return new(o||e)(f(te),f(D))}),u(e,"\u0275dir",U({type:e,hostBindings:function(o,i){o&1&&Ie("ionBlur",function(a){return i._handleBlurEvent(a.target)})},standalone:!1}));let t=e;return t})(),Qn=t=>{lh(()=>{let e=t.nativeElement,r=e.value!=null&&e.value.toString().length>0,n=nR(e);th(e,n);let o=e.closest("ion-item");o&&(r?th(o,[...n,"item-has-value"]):th(o,n))})},nR=t=>{let e=t.classList,r=[];for(let n=0;n<e.length;n++){let o=e.item(n);o!==null&&rR(o,"ng-")&&r.push(`ion-${o.substring(3)}`)}return r},th=(t,e)=>{let r=t.classList;r.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),r.add(...e)},rR=(t,e)=>t.substring(0,e.length)===e,sh=class{shouldDetach(e){return!1}shouldAttach(e){return!1}store(e,r){}retrieve(e){return null}shouldReuseRoute(e,r){if(e.routeConfig!==r.routeConfig)return!1;let n=e.params,o=r.params,i=Object.keys(n),s=Object.keys(o);if(i.length!==s.length)return!1;for(let a of i)if(o[a]!==n[a])return!1;return!0}},pn=class{ctrl;constructor(e){this.ctrl=e}create(e){return this.ctrl.create(e||{})}dismiss(e,r,n){return this.ctrl.dismiss(e,r,n)}getTop(){return this.ctrl.getTop()}};function vI(){var t=[];if(typeof window<"u"){var e=window;(!e.customElements||e.Element&&(!e.Element.prototype.closest||!e.Element.prototype.matches||!e.Element.prototype.remove||!e.Element.prototype.getRootNode))&&t.push(import("./chunk-5X4HMWFG.js"));var r=function(){try{var n=new URL("b","http://a");return n.pathname="c%20d",n.href==="http://a/c%20d"&&n.searchParams}catch{return!1}};(typeof Object.assign!="function"||!Object.entries||!Array.prototype.find||!Array.prototype.includes||!String.prototype.startsWith||!String.prototype.endsWith||e.NodeList&&!e.NodeList.prototype.forEach||!e.fetch||!r()||typeof WeakMap>"u")&&t.push(import("./chunk-2WNWRKHP.js"))}return Promise.all(t)}var yI=Gc;var DI=(t,e)=>ve(void 0,null,function*(){if(!(typeof window>"u"))return yield yI(),yh(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-input-password-toggle",[[33,"ion-input-password-toggle",{"color":[513],"showIcon":[1,"show-icon"],"hideIcon":[1,"hide-icon"],"type":[1025]},null,{"type":["onTypeChange"]}]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"contentId":[513,"content-id"],"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[16,"ionSegmentViewScroll","handleSegmentViewScroll"],[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-input",[[38,"ion-input",{"color":[513],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearInputIcon":[1,"clear-input-icon"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16],"debounce":[2],"disabled":[516],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[516],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},null,{"debounce":["debounceChanged"],"type":["onTypeChange"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64],"getLength":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},null,{"debounce":["debounceChanged"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16],"beforeLeave":[16],"beforeEnter":[16]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"step":["stepChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-segment-content",[[1,"ion-segment-content"]]],["ion-segment-view",[[33,"ion-segment-view",{"disabled":[4],"isManualScroll":[32],"setContent":[64]},[[1,"scroll","handleScroll"],[1,"touchstart","handleScrollStart"],[1,"touchend","handleTouchEnd"]]]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32],"isVisible":[64]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-select-modal",[[34,"ion-select-modal",{"header":[1],"multiple":[4],"options":[16]}]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16],"readonly":[4],"isDateEnabled":[16],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16],"multiple":[4],"highlightedDates":[16],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker-legacy",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-legacy-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"breakpoints":[16],"expandToScroll":[4,"expand-to-scroll"],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"focusTrap":[4,"focus-trap"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-picker",[[33,"ion-picker",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-picker-column",[[1,"ion-picker-column",{"disabled":[4],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"ariaLabel":[32],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64],"setFocus":[64]},null,{"aria-label":["ariaLabelChanged"],"value":["valueChange"]}]]],["ion-picker-column-option",[[33,"ion-picker-column-option",{"disabled":[4],"value":[8],"color":[513],"ariaLabel":[32]},null,{"aria-label":["onAriaLabelChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16],"leaveAnimation":[16],"component":[1],"componentProps":[16],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"focusTrap":[4,"focus-trap"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"setFocus":[64]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[33,"ion-note",{"color":[513]}],[1,"ion-skeleton-text",{"animated":[4]}],[38,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[33,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[516],"download":[1],"href":[1],"rel":[1],"lines":[1],"routerAnimation":[16],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"multipleInputs":[32],"focusable":[32]},[[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"fixedSlotPlacement":[1,"fixed-slot-placement"],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[38,"ion-buttons",{"collapse":[4]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"required":[4],"isExpanded":[32],"hasFocus":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"]}],[36,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032],"helperText":[1,"helper-text"],"errorText":[1,"error-text"],"setFocus":[64]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1],"isCircle":[32]},null,{"disabled":["disabledChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]]]'),e)});var T=["*"],gR=["outletContent"],mR=["outlet"],vR=[[["","slot","top"]],"*",[["ion-tab"]]],yR=["[slot=top]","*","ion-tab"];function DR(t,e){if(t&1){let r=Dv();jr(0,"ion-router-outlet",5,1),Ie("stackWillChange",function(o){nd(r);let i=Bo();return rd(i.onStackWillChange(o))})("stackDidChange",function(o){nd(r);let i=Bo();return rd(i.onStackDidChange(o))}),Lr()}}function IR(t,e){t&1&&w(0,2,["*ngIf","tabs.length > 0"])}function CR(t,e){if(t&1&&(jr(0,"div",1),wa(1,2),Lr()),t&2){let r=Bo();fa(),sn("ngTemplateOutlet",r.template)}}function bR(t,e){if(t&1&&wa(0,1),t&2){let r=Bo();sn("ngTemplateOutlet",r.template)}}var ER=(()=>{let e=class e extends Ri{constructor(n,o){super(n,o)}writeValue(n){this.elementRef.nativeElement.checked=this.lastValue=n,Qn(this.elementRef)}_handleIonChange(n){this.handleValueChange(n,n.checked)}};u(e,"\u0275fac",function(o){return new(o||e)(f(te),f(D))}),u(e,"\u0275dir",U({type:e,selectors:[["ion-checkbox"],["ion-toggle"]],hostBindings:function(o,i){o&1&&Ie("ionChange",function(a){return i._handleIonChange(a.target)})},standalone:!1,features:[Ne([{provide:Zn,useExisting:e,multi:!0}]),ne]}));let t=e;return t})(),wR=(()=>{let e=class e extends Ri{el;constructor(n,o){super(n,o),this.el=o}handleInputEvent(n){this.handleValueChange(n,n.value)}registerOnChange(n){this.el.nativeElement.tagName==="ION-INPUT"?super.registerOnChange(o=>{n(o===""?null:parseFloat(o))}):super.registerOnChange(n)}};u(e,"\u0275fac",function(o){return new(o||e)(f(te),f(D))}),u(e,"\u0275dir",U({type:e,selectors:[["ion-input","type","number"],["ion-range"]],hostBindings:function(o,i){o&1&&Ie("ionInput",function(a){return i.handleInputEvent(a.target)})},standalone:!1,features:[Ne([{provide:Zn,useExisting:e,multi:!0}]),ne]}));let t=e;return t})(),MR=(()=>{let e=class e extends Ri{constructor(n,o){super(n,o)}_handleChangeEvent(n){this.handleValueChange(n,n.value)}};u(e,"\u0275fac",function(o){return new(o||e)(f(te),f(D))}),u(e,"\u0275dir",U({type:e,selectors:[["ion-select"],["ion-radio-group"],["ion-segment"],["ion-datetime"]],hostBindings:function(o,i){o&1&&Ie("ionChange",function(a){return i._handleChangeEvent(a.target)})},standalone:!1,features:[Ne([{provide:Zn,useExisting:e,multi:!0}]),ne]}));let t=e;return t})(),SR=(()=>{let e=class e extends Ri{constructor(n,o){super(n,o)}_handleInputEvent(n){this.handleValueChange(n,n.value)}};u(e,"\u0275fac",function(o){return new(o||e)(f(te),f(D))}),u(e,"\u0275dir",U({type:e,selectors:[["ion-input",3,"type","number"],["ion-textarea"],["ion-searchbar"]],hostBindings:function(o,i){o&1&&Ie("ionInput",function(a){return i._handleInputEvent(a.target)})},standalone:!1,features:[Ne([{provide:Zn,useExisting:e,multi:!0}]),ne]}));let t=e;return t})(),TR=(t,e)=>{let r=t.prototype;e.forEach(n=>{Object.defineProperty(r,n,{get(){return this.el[n]},set(o){this.z.runOutsideAngular(()=>this.el[n]=o)},configurable:!0})})},_R=(t,e)=>{let r=t.prototype;e.forEach(n=>{r[n]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[n].apply(this.el,o))}})},Q=(t,e,r)=>{r.forEach(n=>t[n]=En(e,n))};function _(t){return function(r){let{defineCustomElementFn:n,inputs:o,methods:i}=t;return n!==void 0&&n(),o&&TR(r,o),i&&_R(r,i),r}}var xR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-accordion"]],inputs:{disabled:"disabled",mode:"mode",readonly:"readonly",toggleIcon:"toggleIcon",toggleIconSlot:"toggleIconSlot",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["disabled","mode","readonly","toggleIcon","toggleIconSlot","value"]})],t),t})(),AR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-accordion-group"]],inputs:{animated:"animated",disabled:"disabled",expand:"expand",mode:"mode",multiple:"multiple",readonly:"readonly",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated","disabled","expand","mode","multiple","readonly","value"]})],t),t})(),RR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionActionSheetDidPresent","ionActionSheetWillPresent","ionActionSheetWillDismiss","ionActionSheetDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-action-sheet"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),NR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionAlertDidPresent","ionAlertWillPresent","ionAlertWillDismiss","ionAlertDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-alert"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",inputs:"inputs",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","inputs","isOpen","keyboardClose","leaveAnimation","message","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),OR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-app"]],standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({methods:["setFocus"]})],t),t})(),kR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-avatar"]],standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({})],t),t})(),FR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionBackdropTap"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-backdrop"]],inputs:{stopPropagation:"stopPropagation",tappable:"tappable",visible:"visible"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["stopPropagation","tappable","visible"]})],t),t})(),PR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-badge"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode"]})],t),t})(),jR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-breadcrumb"]],inputs:{active:"active",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",separator:"separator",target:"target"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["active","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","separator","target"]})],t),t})(),LR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionCollapsedClick"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-breadcrumbs"]],inputs:{color:"color",itemsAfterCollapse:"itemsAfterCollapse",itemsBeforeCollapse:"itemsBeforeCollapse",maxItems:"maxItems",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","itemsAfterCollapse","itemsBeforeCollapse","maxItems","mode"]})],t),t})(),VR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],t),t})(),BR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-buttons"]],inputs:{collapse:"collapse"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["collapse"]})],t),t})(),UR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],t),t})(),HR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-card-content"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["mode"]})],t),t})(),$R=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode","translucent"]})],t),t})(),zR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-card-subtitle"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode"]})],t),t})(),GR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode"]})],t),t})(),WR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange","ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-checkbox"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",errorText:"errorText",helperText:"helperText",indeterminate:"indeterminate",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["alignment","checked","color","disabled","errorText","helperText","indeterminate","justify","labelPlacement","mode","name","required","value"]})],t),t})(),qR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-chip"]],inputs:{color:"color",disabled:"disabled",mode:"mode",outline:"outline"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","disabled","mode","outline"]})],t),t})(),ZR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-col"]],inputs:{offset:"offset",offsetLg:"offsetLg",offsetMd:"offsetMd",offsetSm:"offsetSm",offsetXl:"offsetXl",offsetXs:"offsetXs",pull:"pull",pullLg:"pullLg",pullMd:"pullMd",pullSm:"pullSm",pullXl:"pullXl",pullXs:"pullXs",push:"push",pushLg:"pushLg",pushMd:"pushMd",pushSm:"pushSm",pushXl:"pushXl",pushXs:"pushXs",size:"size",sizeLg:"sizeLg",sizeMd:"sizeMd",sizeSm:"sizeSm",sizeXl:"sizeXl",sizeXs:"sizeXs"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["offset","offsetLg","offsetMd","offsetSm","offsetXl","offsetXs","pull","pullLg","pullMd","pullSm","pullXl","pullXs","push","pushLg","pushMd","pushSm","pushXl","pushXs","size","sizeLg","sizeMd","sizeSm","sizeXl","sizeXs"]})],t),t})(),YR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],t),t})(),QR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionCancel","ionChange","ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-datetime"]],inputs:{cancelText:"cancelText",clearText:"clearText",color:"color",dayValues:"dayValues",disabled:"disabled",doneText:"doneText",firstDayOfWeek:"firstDayOfWeek",formatOptions:"formatOptions",highlightedDates:"highlightedDates",hourCycle:"hourCycle",hourValues:"hourValues",isDateEnabled:"isDateEnabled",locale:"locale",max:"max",min:"min",minuteValues:"minuteValues",mode:"mode",monthValues:"monthValues",multiple:"multiple",name:"name",preferWheel:"preferWheel",presentation:"presentation",readonly:"readonly",showClearButton:"showClearButton",showDefaultButtons:"showDefaultButtons",showDefaultTimeLabel:"showDefaultTimeLabel",showDefaultTitle:"showDefaultTitle",size:"size",titleSelectedDatesFormatter:"titleSelectedDatesFormatter",value:"value",yearValues:"yearValues"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["cancelText","clearText","color","dayValues","disabled","doneText","firstDayOfWeek","formatOptions","highlightedDates","hourCycle","hourValues","isDateEnabled","locale","max","min","minuteValues","mode","monthValues","multiple","name","preferWheel","presentation","readonly","showClearButton","showDefaultButtons","showDefaultTimeLabel","showDefaultTitle","size","titleSelectedDatesFormatter","value","yearValues"],methods:["confirm","reset","cancel"]})],t),t})(),KR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-datetime-button"]],inputs:{color:"color",datetime:"datetime",disabled:"disabled",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","datetime","disabled","mode"]})],t),t})(),XR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-fab"]],inputs:{activated:"activated",edge:"edge",horizontal:"horizontal",vertical:"vertical"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["activated","edge","horizontal","vertical"],methods:["close"]})],t),t})(),JR=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-fab-button"]],inputs:{activated:"activated",closeIcon:"closeIcon",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",show:"show",size:"size",target:"target",translucent:"translucent",type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["activated","closeIcon","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","show","size","target","translucent","type"]})],t),t})(),eN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-fab-list"]],inputs:{activated:"activated",side:"side"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["activated","side"]})],t),t})(),tN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-footer"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["collapse","mode","translucent"]})],t),t})(),nN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-grid"]],inputs:{fixed:"fixed"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["fixed"]})],t),t})(),rN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["collapse","mode","translucent"]})],t),t})(),oN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-icon"]],inputs:{color:"color",flipRtl:"flipRtl",icon:"icon",ios:"ios",lazy:"lazy",md:"md",mode:"mode",name:"name",sanitize:"sanitize",size:"size",src:"src"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","flipRtl","icon","ios","lazy","md","mode","name","sanitize","size","src"]})],t),t})(),iN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionImgWillLoad","ionImgDidLoad","ionError"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-img"]],inputs:{alt:"alt",src:"src"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["alt","src"]})],t),t})(),sN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionInfinite"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-infinite-scroll"]],inputs:{disabled:"disabled",position:"position",threshold:"threshold"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["disabled","position","threshold"],methods:["complete"]})],t),t})(),aN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-infinite-scroll-content"]],inputs:{loadingSpinner:"loadingSpinner",loadingText:"loadingText"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["loadingSpinner","loadingText"]})],t),t})(),cN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-input"]],inputs:{autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearInputIcon:"clearInputIcon",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearInputIcon","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","spellcheck","step","type","value"],methods:["setFocus","getInputElement"]})],t),t})(),lN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-input-password-toggle"]],inputs:{color:"color",hideIcon:"hideIcon",mode:"mode",showIcon:"showIcon"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","hideIcon","mode","showIcon"]})],t),t})(),uN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-item"]],inputs:{button:"button",color:"color",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["button","color","detail","detailIcon","disabled","download","href","lines","mode","rel","routerAnimation","routerDirection","target","type"]})],t),t})(),dN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-item-divider"]],inputs:{color:"color",mode:"mode",sticky:"sticky"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode","sticky"]})],t),t})(),fN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-item-group"]],standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({})],t),t})(),hN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-item-option"]],inputs:{color:"color",disabled:"disabled",download:"download",expandable:"expandable",href:"href",mode:"mode",rel:"rel",target:"target",type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","disabled","download","expandable","href","mode","rel","target","type"]})],t),t})(),pN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionSwipe"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-item-options"]],inputs:{side:"side"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["side"]})],t),t})(),gN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionDrag"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-item-sliding"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["disabled"],methods:["getOpenAmount","getSlidingRatio","open","close","closeOpened"]})],t),t})(),mN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode","position"]})],t),t})(),vN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],t),t})(),yN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-list-header"]],inputs:{color:"color",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","lines","mode"]})],t),t})(),DN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionLoadingDidPresent","ionLoadingWillPresent","ionLoadingWillDismiss","ionLoadingDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-loading"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",showBackdrop:"showBackdrop",spinner:"spinner",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated","backdropDismiss","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","message","mode","showBackdrop","spinner","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),IN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionWillOpen","ionWillClose","ionDidOpen","ionDidClose"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-menu"]],inputs:{contentId:"contentId",disabled:"disabled",maxEdgeStart:"maxEdgeStart",menuId:"menuId",side:"side",swipeGesture:"swipeGesture",type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["contentId","disabled","maxEdgeStart","menuId","side","swipeGesture","type"],methods:["isOpen","isActive","open","close","toggle","setOpen"]})],t),t})(),CN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-menu-button"]],inputs:{autoHide:"autoHide",color:"color",disabled:"disabled",menu:"menu",mode:"mode",type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["autoHide","color","disabled","menu","mode","type"]})],t),t})(),bN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-menu-toggle"]],inputs:{autoHide:"autoHide",menu:"menu"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["autoHide","menu"]})],t),t})(),EN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-nav-link"]],inputs:{component:"component",componentProps:"componentProps",routerAnimation:"routerAnimation",routerDirection:"routerDirection"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["component","componentProps","routerAnimation","routerDirection"]})],t),t})(),wN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-note"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode"]})],t),t})(),MN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-picker"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["mode"]})],t),t})(),SN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-picker-column"]],inputs:{color:"color",disabled:"disabled",mode:"mode",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","disabled","mode","value"],methods:["setFocus"]})],t),t})(),TN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-picker-column-option"]],inputs:{color:"color",disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","disabled","value"]})],t),t})(),_N=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionPickerDidPresent","ionPickerWillPresent","ionPickerWillDismiss","ionPickerDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-picker-legacy"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",columns:"columns",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",trigger:"trigger"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated","backdropDismiss","buttons","columns","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss","getColumn"]})],t),t})(),xN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-progress-bar"]],inputs:{buffer:"buffer",color:"color",mode:"mode",reversed:"reversed",type:"type",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["buffer","color","mode","reversed","type","value"]})],t),t})(),AN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-radio"]],inputs:{alignment:"alignment",color:"color",disabled:"disabled",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["alignment","color","disabled","justify","labelPlacement","mode","name","value"]})],t),t})(),RN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-radio-group"]],inputs:{allowEmptySelection:"allowEmptySelection",compareWith:"compareWith",errorText:"errorText",helperText:"helperText",name:"name",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["allowEmptySelection","compareWith","errorText","helperText","name","value"]})],t),t})(),NN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange","ionInput","ionFocus","ionBlur","ionKnobMoveStart","ionKnobMoveEnd"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-range"]],inputs:{activeBarStart:"activeBarStart",color:"color",debounce:"debounce",disabled:"disabled",dualKnobs:"dualKnobs",label:"label",labelPlacement:"labelPlacement",max:"max",min:"min",mode:"mode",name:"name",pin:"pin",pinFormatter:"pinFormatter",snaps:"snaps",step:"step",ticks:"ticks",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["activeBarStart","color","debounce","disabled","dualKnobs","label","labelPlacement","max","min","mode","name","pin","pinFormatter","snaps","step","ticks","value"]})],t),t})(),ON=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionRefresh","ionPull","ionStart"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-refresher"]],inputs:{closeDuration:"closeDuration",disabled:"disabled",mode:"mode",pullFactor:"pullFactor",pullMax:"pullMax",pullMin:"pullMin",snapbackDuration:"snapbackDuration"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["closeDuration","disabled","mode","pullFactor","pullMax","pullMin","snapbackDuration"],methods:["complete","cancel","getProgress"]})],t),t})(),kN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-refresher-content"]],inputs:{pullingIcon:"pullingIcon",pullingText:"pullingText",refreshingSpinner:"refreshingSpinner",refreshingText:"refreshingText"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["pullingIcon","pullingText","refreshingSpinner","refreshingText"]})],t),t})(),FN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-reorder"]],standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({})],t),t})(),PN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionItemReorder"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-reorder-group"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["disabled"],methods:["complete"]})],t),t})(),jN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-ripple-effect"]],inputs:{type:"type"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["type"],methods:["addRipple"]})],t),t})(),LN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-row"]],standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({})],t),t})(),VN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionInput","ionChange","ionCancel","ionClear","ionBlur","ionFocus"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-searchbar"]],inputs:{animated:"animated",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",cancelButtonIcon:"cancelButtonIcon",cancelButtonText:"cancelButtonText",clearIcon:"clearIcon",color:"color",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",inputmode:"inputmode",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",searchIcon:"searchIcon",showCancelButton:"showCancelButton",showClearButton:"showClearButton",spellcheck:"spellcheck",type:"type",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated","autocapitalize","autocomplete","autocorrect","cancelButtonIcon","cancelButtonText","clearIcon","color","debounce","disabled","enterkeyhint","inputmode","maxlength","minlength","mode","name","placeholder","searchIcon","showCancelButton","showClearButton","spellcheck","type","value"],methods:["setFocus","getInputElement"]})],t),t})(),BN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-segment"]],inputs:{color:"color",disabled:"disabled",mode:"mode",scrollable:"scrollable",selectOnFocus:"selectOnFocus",swipeGesture:"swipeGesture",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","disabled","mode","scrollable","selectOnFocus","swipeGesture","value"]})],t),t})(),UN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-segment-button"]],inputs:{contentId:"contentId",disabled:"disabled",layout:"layout",mode:"mode",type:"type",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["contentId","disabled","layout","mode","type","value"]})],t),t})(),HN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-segment-content"]],standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({})],t),t})(),$N=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionSegmentViewScroll"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-segment-view"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["disabled"]})],t),t})(),zN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange","ionCancel","ionDismiss","ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-select"]],inputs:{cancelText:"cancelText",color:"color",compareWith:"compareWith",disabled:"disabled",errorText:"errorText",expandedIcon:"expandedIcon",fill:"fill",helperText:"helperText",interface:"interface",interfaceOptions:"interfaceOptions",justify:"justify",label:"label",labelPlacement:"labelPlacement",mode:"mode",multiple:"multiple",name:"name",okText:"okText",placeholder:"placeholder",required:"required",selectedText:"selectedText",shape:"shape",toggleIcon:"toggleIcon",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["cancelText","color","compareWith","disabled","errorText","expandedIcon","fill","helperText","interface","interfaceOptions","justify","label","labelPlacement","mode","multiple","name","okText","placeholder","required","selectedText","shape","toggleIcon","value"],methods:["open"]})],t),t})(),GN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-select-modal"]],inputs:{header:"header",multiple:"multiple",options:"options"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["header","multiple","options"]})],t),t})(),WN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-select-option"]],inputs:{disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["disabled","value"]})],t),t})(),qN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-skeleton-text"]],inputs:{animated:"animated"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated"]})],t),t})(),ZN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-spinner"]],inputs:{color:"color",duration:"duration",name:"name",paused:"paused"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","duration","name","paused"]})],t),t})(),YN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionSplitPaneVisible"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-split-pane"]],inputs:{contentId:"contentId",disabled:"disabled",when:"when"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["contentId","disabled","when"]})],t),t})(),II=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-tab"]],inputs:{component:"component",tab:"tab"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["component","tab"],methods:["setActive"]})],t),t})(),uh=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-tab-bar"]],inputs:{color:"color",mode:"mode",selectedTab:"selectedTab",translucent:"translucent"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode","selectedTab","translucent"]})],t),t})(),QN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-tab-button"]],inputs:{disabled:"disabled",download:"download",href:"href",layout:"layout",mode:"mode",rel:"rel",selected:"selected",tab:"tab",target:"target"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["disabled","download","href","layout","mode","rel","selected","tab","target"]})],t),t})(),KN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-text"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode"]})],t),t})(),XN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange","ionInput","ionBlur","ionFocus"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-textarea"]],inputs:{autoGrow:"autoGrow",autocapitalize:"autocapitalize",autofocus:"autofocus",clearOnEdit:"clearOnEdit",color:"color",cols:"cols",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",readonly:"readonly",required:"required",rows:"rows",shape:"shape",spellcheck:"spellcheck",value:"value",wrap:"wrap"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["autoGrow","autocapitalize","autofocus","clearOnEdit","color","cols","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","maxlength","minlength","mode","name","placeholder","readonly","required","rows","shape","spellcheck","value","wrap"],methods:["setFocus","getInputElement"]})],t),t})(),JN=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-thumbnail"]],standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({})],t),t})(),eO=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","size"]})],t),t})(),tO=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionToastDidPresent","ionToastWillPresent","ionToastWillDismiss","ionToastDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-toast"]],inputs:{animated:"animated",buttons:"buttons",color:"color",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",icon:"icon",isOpen:"isOpen",keyboardClose:"keyboardClose",layout:"layout",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",position:"position",positionAnchor:"positionAnchor",swipeGesture:"swipeGesture",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["animated","buttons","color","cssClass","duration","enterAnimation","header","htmlAttributes","icon","isOpen","keyboardClose","layout","leaveAnimation","message","mode","position","positionAnchor","swipeGesture","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],t),t})(),nO=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Q(this,this.el,["ionChange","ionFocus","ionBlur"])}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-toggle"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",enableOnOffLabels:"enableOnOffLabels",errorText:"errorText",helperText:"helperText",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["alignment","checked","color","disabled","enableOnOffLabels","errorText","helperText","justify","labelPlacement","mode","name","required","value"]})],t),t})(),rO=(()=>{var e;let t=(e=class{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}},u(e,"\u0275fac",function(o){return new(o||e)(f(C),f(D),f(m))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0})),e);return t=M([_({inputs:["color","mode"]})],t),t})(),Fc=(()=>{let e=class e extends ch{parentOutlet;outletContent;constructor(n,o,i,s,a,c,l,d){super(n,o,i,s,a,c,l,d),this.parentOutlet=d}};u(e,"\u0275fac",function(o){return new(o||e)(Mt("name"),Mt("tabs"),f(We),f(D),f(Ce),f(m),f(Ae),f(e,12))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-router-outlet"]],viewQuery:function(o,i){if(o&1&&Uo(gR,7,ze),o&2){let s;lt(s=ut())&&(i.outletContent=s.first)}},standalone:!1,features:[ne],ngContentSelectors:T,decls:3,vars:0,consts:[["outletContent",""]],template:function(o,i){o&1&&(S(),ba(0,null,0),w(2),Ea())},encapsulation:2}));let t=e;return t})(),oO=(()=>{let e=class e extends hI{outlet;tabBar;tabBars;tabs};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275cmp",E({type:e,selectors:[["ion-tabs"]],contentQueries:function(o,i,s){if(o&1&&(an(s,uh,5),an(s,uh,4),an(s,II,4)),o&2){let a;lt(a=ut())&&(i.tabBar=a.first),lt(a=ut())&&(i.tabBars=a),lt(a=ut())&&(i.tabs=a)}},viewQuery:function(o,i){if(o&1&&Uo(mR,5,Fc),o&2){let s;lt(s=ut())&&(i.outlet=s.first)}},standalone:!1,features:[ne],ngContentSelectors:yR,decls:6,vars:2,consts:[["tabsInner",""],["outlet",""],[1,"tabs-inner"],["tabs","true",3,"stackWillChange","stackDidChange",4,"ngIf"],[4,"ngIf"],["tabs","true",3,"stackWillChange","stackDidChange"]],template:function(o,i){o&1&&(S(vR),w(0),jr(1,"div",2,0),Lo(3,DR,2,0,"ion-router-outlet",3)(4,IR,1,0,"ng-content",4),Lr(),w(5,1)),o&2&&(fa(3),sn("ngIf",i.tabs.length===0),fa(),sn("ngIf",i.tabs.length>0))},dependencies:[Go,Fc],styles:["[_nghost-%COMP%]{display:flex;position:absolute;inset:0;flex-direction:column;width:100%;height:100%;contain:layout size style}.tabs-inner[_ngcontent-%COMP%]{position:relative;flex:1;contain:layout size style}"]}));let t=e;return t})(),iO=(()=>{let e=class e extends lI{constructor(n,o,i,s,a,c){super(n,o,i,s,a,c)}};u(e,"\u0275fac",function(o){return new(o||e)(f(Fc,8),f(gn),f(Ai),f(D),f(m),f(C))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-back-button"]],standalone:!1,features:[ne],ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0}));let t=e;return t})(),sO=(()=>{let e=class e extends fI{constructor(n,o,i,s,a,c){super(n,o,i,s,a,c)}};u(e,"\u0275fac",function(o){return new(o||e)(f(D),f(ae),f(te),f(mn),f(m),f(C))}),u(e,"\u0275cmp",E({type:e,selectors:[["ion-nav"]],standalone:!1,features:[ne],ngContentSelectors:T,decls:1,vars:0,template:function(o,i){o&1&&(S(),w(0))},encapsulation:2,changeDetection:0}));let t=e;return t})(),aO=(()=>{let e=class e extends uI{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",U({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],standalone:!1,features:[ne]}));let t=e;return t})(),cO=(()=>{let e=class e extends dI{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",U({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],standalone:!1,features:[ne]}));let t=e;return t})(),lO=(()=>{let e=class e extends tI{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275cmp",E({type:e,selectors:[["ion-modal"]],standalone:!1,features:[ne],decls:1,vars:1,consts:[["class","ion-delegate-host ion-page",4,"ngIf"],[1,"ion-delegate-host","ion-page"],[3,"ngTemplateOutlet"]],template:function(o,i){o&1&&Lo(0,CR,2,1,"div",0),o&2&&sn("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[Go,Oa],encapsulation:2,changeDetection:0}));let t=e;return t})(),uO=(()=>{let e=class e extends eI{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275cmp",E({type:e,selectors:[["ion-popover"]],standalone:!1,features:[ne],decls:1,vars:1,consts:[[3,"ngTemplateOutlet",4,"ngIf"],[3,"ngTemplateOutlet"]],template:function(o,i){o&1&&Lo(0,bR,1,1,"ng-container",0),o&2&&sn("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[Go,Oa],encapsulation:2,changeDetection:0}));let t=e;return t})(),dO={provide:Wt,useExisting:$e(()=>CI),multi:!0},CI=(()=>{let e=class e extends Wf{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",U({type:e,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(o,i){o&2&&ct("max",i._enabled?i.max:null)},standalone:!1,features:[Ne([dO]),ne]}));let t=e;return t})(),fO={provide:Wt,useExisting:$e(()=>bI),multi:!0},bI=(()=>{let e=class e extends qf{};u(e,"\u0275fac",(()=>{let n;return function(i){return(n||(n=we(e)))(i||e)}})()),u(e,"\u0275dir",U({type:e,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(o,i){o&2&&ct("min",i._enabled?i.min:null)},standalone:!1,features:[Ne([fO]),ne]}));let t=e;return t})(),u3=(()=>{let e=class e extends pn{constructor(){super(Wc)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();var d3=(()=>{let e=class e extends pn{constructor(){super(qc)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})();var hO=(()=>{let e=class e extends pn{angularDelegate=I(mn);injector=I(te);environmentInjector=I(ae);constructor(){super(Zc)}create(n){return super.create(L(b({},n),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac}));let t=e;return t})();var dh=class extends pn{angularDelegate=I(mn);injector=I(te);environmentInjector=I(ae);constructor(){super(Yc)}create(e){return super.create(L(b({},e),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},f3=(()=>{let e=class e extends pn{constructor(){super(Qc)}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275prov",A({token:e,factory:e.\u0275fac,providedIn:"root"}));let t=e;return t})(),pO=(t,e,r)=>()=>{let n=e.defaultView;if(n&&typeof window<"u"){Kc(L(b({},t),{_zoneGate:i=>r.run(i)}));let o="__zone_symbol__addEventListener"in e.body?"__zone_symbol__addEventListener":"addEventListener";return vI().then(()=>DI(n,{exclude:["ion-tabs"],syncQueue:!0,raf:lh,jmp:i=>r.runOutsideAngular(i),ael(i,s,a,c){i[o](s,a,c)},rel(i,s,a,c){i.removeEventListener(s,a,c)}}))}},gO=[xR,AR,RR,NR,OR,kR,FR,PR,jR,LR,VR,BR,UR,HR,$R,zR,GR,WR,qR,ZR,YR,QR,KR,XR,JR,eN,tN,nN,rN,oN,iN,sN,aN,cN,lN,uN,dN,fN,hN,pN,gN,mN,vN,yN,DN,IN,CN,bN,EN,wN,MN,SN,TN,_N,xN,AN,RN,NN,ON,kN,FN,PN,jN,LN,VN,BN,UN,HN,$N,zN,GN,WN,qN,ZN,YN,II,uh,QN,KN,XN,JN,eO,tO,nO,rO],h3=[...gO,lO,uO,ER,wR,MR,SR,oO,Fc,iO,sO,aO,cO,bI,CI],p3=(()=>{let e=class e{static forRoot(n={}){return{ngModule:e,providers:[{provide:Oc,useValue:n},{provide:Ia,useFactory:pO,multi:!0,deps:[Oc,ce,m]},mn,cI()]}}};u(e,"\u0275fac",function(o){return new(o||e)}),u(e,"\u0275mod",Re({type:e})),u(e,"\u0275inj",_e({providers:[hO,dh],imports:[Wo]}));let t=e;return t})();export{q as a,ee as b,pe as c,s1 as d,M as e,sr as f,SI as g,LI as h,En as i,xI as j,pt as k,nC as l,A as m,_e as n,k as o,I as p,nd as q,rd as r,we as s,yV as t,se as u,It as v,DV as w,Zb as x,fa as y,f as z,E as A,Re as B,Lo as C,sn as D,Ca as E,jr as F,Lr as G,Hd as H,Dv as I,Ie as J,Bo as K,iS as L,xV as M,aS as N,bv as O,cS as P,lS as Q,AV as R,uS as S,RV as T,jS as U,Lv as V,Go as W,Wo as X,nT as Y,iT as Z,ln as _,iy as $,ay as aa,_T as ba,Ae as ca,Xy as da,Ce as ea,rx as fa,ax as ga,kH as ha,FH as ia,$x as ja,Wx as ka,jH as la,Kx as ma,Jx as na,LH as oa,Yf as pa,YH as qa,QH as ra,XH as sa,JH as ta,IA as ua,f$ as va,h$ as wa,p$ as xa,g$ as ya,m$ as za,$D as Aa,v$ as Ba,y$ as Ca,XD as Da,sh as Ea,pn as Fa,ER as Ga,wR as Ha,MR as Ia,SR as Ja,NR as Ka,OR as La,PR as Ma,VR as Na,BR as Oa,UR as Pa,HR as Qa,$R as Ra,zR as Sa,GR as Ta,WR as Ua,qR as Va,YR as Wa,XR as Xa,JR as Ya,eN as Za,tN as _a,rN as $a,oN as ab,cN as bb,uN as cb,dN as db,fN as eb,mN as fb,vN as gb,wN as hb,xN as ib,ON as jb,kN as kb,VN as lb,BN as mb,UN as nb,zN as ob,WN as pb,ZN as qb,uh as rb,QN as sb,KN as tb,eO as ub,rO as vb,Fc as wb,oO as xb,lO as yb,u3 as zb,d3 as Ab,hO as Bb,f3 as Cb,p3 as Db};
