1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.ionic.starter"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:80:5-67
13-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:80:22-64
14    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
14-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:81:5-79
14-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:81:22-76
15    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
15-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:82:5-81
15-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:82:22-78
16    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
16-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:83:5-79
16-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:83:22-76
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:84:5-66
17-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:84:22-63
18
19    <!-- FCM Permissions -->
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:87:5-68
20-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:87:22-65
21    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
21-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:88:5-77
21-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:88:22-74
22
23    <!-- Emergency Alert Permissions -->
24    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
24-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:91:5-78
24-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:91:22-75
25    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
25-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:92:5-81
25-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:92:22-78
26    <uses-permission android:name="android.permission.TURN_SCREEN_ON" />
26-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:93:5-73
26-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:93:22-70
27    <uses-permission android:name="android.permission.SHOW_WHEN_LOCKED" />
27-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:94:5-75
27-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:94:22-72
28    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
28-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:95:5-75
28-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:95:22-72
29
30    <!-- Background Mode Permissions -->
31    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
31-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:98:5-77
31-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:98:22-74
32    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
32-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:99:5-95
32-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:99:22-92
33    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
33-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:100:5-81
33-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:100:22-78
34    <!-- Required by older versions of Google Play services to create IID tokens -->
35    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
35-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:26:5-82
35-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:26:22-79
36    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
36-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
36-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
37    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
37-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
37-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
38    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
38-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
38-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
39    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
39-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
39-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
40
41    <permission
41-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
42        android:name="io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
42-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
43        android:protectionLevel="signature" />
43-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
44
45    <uses-permission android:name="io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
45-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
45-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
46
47    <application
47-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:4:5-76:19
48        android:allowBackup="true"
48-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:5:9-35
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2fd6a0672b5d2b7edb19f06631768576\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
50        android:debuggable="true"
51        android:extractNativeLibs="false"
52        android:icon="@mipmap/ic_launcher"
52-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:6:9-43
53        android:label="@string/app_name"
53-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:7:9-41
54        android:networkSecurityConfig="@xml/network_security_config"
54-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:12:9-69
55        android:roundIcon="@mipmap/ic_launcher_round"
55-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:8:9-54
56        android:supportsRtl="true"
56-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:9:9-35
57        android:theme="@style/AppTheme"
57-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:10:9-40
58        android:usesCleartextTraffic="true" >
58-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:11:9-44
59        <activity
59-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:14:9-33:20
60            android:name="io.ionic.starter.MainActivity"
60-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:16:13-41
61            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
61-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:15:13-140
62            android:exported="true"
62-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:20:13-36
63            android:label="@string/title_activity_main"
63-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:17:13-56
64            android:launchMode="singleTask"
64-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:19:13-44
65            android:theme="@style/AppTheme.NoActionBarLaunch" >
65-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:18:13-62
66            <intent-filter>
66-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:22:13-25:29
67                <action android:name="android.intent.action.MAIN" />
67-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:23:17-69
67-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:23:25-66
68
69                <category android:name="android.intent.category.LAUNCHER" />
69-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:24:17-77
69-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:24:27-74
70            </intent-filter>
71
72            <!-- Add FCM intent filter to handle notification clicks -->
73            <intent-filter>
73-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:28:13-31:29
74                <action android:name="FLUTTER_NOTIFICATION_CLICK" />
74-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:29:17-69
74-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:29:25-66
75
76                <category android:name="android.intent.category.DEFAULT" />
76-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:30:17-76
76-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:30:27-73
77            </intent-filter>
78        </activity>
79
80        <!-- Emergency Alert Activity -->
81        <activity
81-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:36:9-43:43
82            android:name="io.ionic.starter.EmergencyAlertActivity"
82-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:37:13-51
83            android:excludeFromRecents="true"
83-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:40:13-46
84            android:exported="false"
84-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:41:13-37
85            android:launchMode="singleTop"
85-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:39:13-43
86            android:showWhenLocked="true"
86-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:42:13-42
87            android:theme="@android:style/Theme.NoTitleBar.Fullscreen"
87-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:38:13-71
88            android:turnScreenOn="true" />
88-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:43:13-40
89
90        <!-- FCM Configuration -->
91        <meta-data
91-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:46:9-48:60
92            android:name="com.google.firebase.messaging.default_notification_icon"
92-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:47:13-83
93            android:resource="@drawable/ic_notification" />
93-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:48:13-57
94        <meta-data
94-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:50:9-52:53
95            android:name="com.google.firebase.messaging.default_notification_color"
95-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:51:13-84
96            android:resource="@color/colorAccent" />
96-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:52:13-50
97        <meta-data
97-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:54:9-56:53
98            android:name="com.google.firebase.messaging.default_notification_channel_id"
98-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:55:13-89
99            android:value="general-notifications" />
99-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:56:13-50
100
101        <!-- Emergency FCM Service -->
102        <service
102-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:59:9-65:19
103            android:name="io.ionic.starter.EmergencyFCMService"
103-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:60:13-48
104            android:exported="false" >
104-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:61:13-37
105            <intent-filter>
105-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:62:13-64:29
106                <action android:name="com.google.firebase.MESSAGING_EVENT" />
106-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:63:17-78
106-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:63:25-75
107            </intent-filter>
108        </service>
109
110        <provider
111            android:name="androidx.core.content.FileProvider"
111-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:68:13-62
112            android:authorities="io.ionic.starter.fileprovider"
112-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:69:13-64
113            android:exported="false"
113-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:70:13-37
114            android:grantUriPermissions="true" >
114-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:71:13-47
115            <meta-data
115-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:72:13-74:64
116                android:name="android.support.FILE_PROVIDER_PATHS"
116-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:73:17-67
117                android:resource="@xml/file_paths" />
117-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:74:17-51
118        </provider>
119
120        <service
120-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-16:19
121            android:name="io.capawesome.capacitorjs.plugins.firebase.messaging.MessagingService"
121-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-97
122            android:exported="false" >
122-->[:capacitor-firebase-messaging] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor-firebase\messaging\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
123            <intent-filter>
123-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:62:13-64:29
124                <action android:name="com.google.firebase.MESSAGING_EVENT" />
124-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:63:17-78
124-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:63:25-75
125            </intent-filter>
126        </service>
127
128        <receiver android:name="com.capacitorjs.plugins.localnotifications.TimedNotificationPublisher" />
128-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-106
128-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:19-103
129        <receiver android:name="com.capacitorjs.plugins.localnotifications.NotificationDismissReceiver" />
129-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-107
129-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:19-104
130        <receiver
130-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-23:20
131            android:name="com.capacitorjs.plugins.localnotifications.LocalNotificationRestoreReceiver"
131-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-103
132            android:directBootAware="true"
132-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-43
133            android:exported="false" >
133-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
134            <intent-filter>
134-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-22:29
135                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
135-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-86
135-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:25-83
136                <action android:name="android.intent.action.BOOT_COMPLETED" />
136-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-79
136-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:25-76
137                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
137-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:17-82
137-->[:capacitor-local-notifications] C:\Users\<USER>\CAPSTONE\mobile_ionic\node_modules\@capacitor\local-notifications\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:25-79
138            </intent-filter>
139        </receiver>
140
141        <service android:name="de.appplant.cordova.plugin.background.ForegroundService" />
141-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-91
141-->[:capacitor-cordova-android-plugins] C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:18-88
142
143        <receiver
143-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:29:9-40:20
144            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
144-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:30:13-78
145            android:exported="true"
145-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:31:13-36
146            android:permission="com.google.android.c2dm.permission.SEND" >
146-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:32:13-73
147            <intent-filter>
147-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:33:13-35:29
148                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
148-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:34:17-81
148-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:34:25-78
149            </intent-filter>
150
151            <meta-data
151-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:37:13-39:40
152                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
152-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:38:17-92
153                android:value="true" />
153-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:39:17-37
154        </receiver>
155        <!--
156             FirebaseMessagingService performs security checks at runtime,
157             but set to not exported to explicitly avoid allowing another app to call it.
158        -->
159        <service
159-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:46:9-53:19
160            android:name="com.google.firebase.messaging.FirebaseMessagingService"
160-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:47:13-82
161            android:directBootAware="true"
161-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:48:13-43
162            android:exported="false" >
162-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:49:13-37
163            <intent-filter android:priority="-500" >
163-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:62:13-64:29
164                <action android:name="com.google.firebase.MESSAGING_EVENT" />
164-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:63:17-78
164-->C:\Users\<USER>\CAPSTONE\mobile_ionic\android\app\src\main\AndroidManifest.xml:63:25-75
165            </intent-filter>
166        </service>
167        <service
167-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:54:9-63:19
168            android:name="com.google.firebase.components.ComponentDiscoveryService"
168-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:55:13-84
169            android:directBootAware="true"
169-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
170            android:exported="false" >
170-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:56:13-37
171            <meta-data
171-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:57:13-59:85
172                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
172-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:58:17-122
173                android:value="com.google.firebase.components.ComponentRegistrar" />
173-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:59:17-82
174            <meta-data
174-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:60:13-62:85
175                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
175-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:61:17-119
176                android:value="com.google.firebase.components.ComponentRegistrar" />
176-->[com.google.firebase:firebase-messaging:24.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e91d555d82bac48f4d55cfa5dcc85271\transformed\jetified-firebase-messaging-24.1.0\AndroidManifest.xml:62:17-82
177            <meta-data
177-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\024b6659e57055e6c4017e4c21ea75d4\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
178                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
178-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\024b6659e57055e6c4017e4c21ea75d4\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
179                android:value="com.google.firebase.components.ComponentRegistrar" />
179-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\024b6659e57055e6c4017e4c21ea75d4\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
180            <meta-data
180-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
181                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
181-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
182                android:value="com.google.firebase.components.ComponentRegistrar" />
182-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
183            <meta-data
183-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
184                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
184-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
185                android:value="com.google.firebase.components.ComponentRegistrar" />
185-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
186            <meta-data
186-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
187                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
187-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
188                android:value="com.google.firebase.components.ComponentRegistrar" />
188-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84df4938116bcd0a881358934b0b4ea4\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
189            <meta-data
189-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f93b0ea020f03c70d0995ea03b814315\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
190                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
190-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f93b0ea020f03c70d0995ea03b814315\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
191                android:value="com.google.firebase.components.ComponentRegistrar" />
191-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f93b0ea020f03c70d0995ea03b814315\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
192            <meta-data
192-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
193                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
193-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
194                android:value="com.google.firebase.components.ComponentRegistrar" />
194-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
195        </service>
196        <service
196-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
197            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
197-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
198            android:exported="false" >
198-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
199            <meta-data
199-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
200                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
200-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
201                android:value="cct" />
201-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d181ca5187d5b517c5388633f851a632\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
202        </service>
203        <service
203-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
204            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
204-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
205            android:exported="false"
205-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
206            android:permission="android.permission.BIND_JOB_SERVICE" >
206-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
207        </service>
208
209        <receiver
209-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
210            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
210-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
211            android:exported="false" />
211-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a1e028cef67bba92cebc0cee233aa0d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
212
213        <property
213-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
214            android:name="android.adservices.AD_SERVICES_CONFIG"
214-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
215            android:resource="@xml/ga_ad_services_config" />
215-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9447c3e5711a66e5ac1cefea9f074d61\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
216
217        <provider
217-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
218            android:name="com.google.firebase.provider.FirebaseInitProvider"
218-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
219            android:authorities="io.ionic.starter.firebaseinitprovider"
219-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
220            android:directBootAware="true"
220-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
221            android:exported="false"
221-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
222            android:initOrder="100" />
222-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc260dd97d40da75efd691977693d785\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
223
224        <activity
224-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f27da281d74b85eb78118b239a6eaaa\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
225            android:name="com.google.android.gms.common.api.GoogleApiActivity"
225-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f27da281d74b85eb78118b239a6eaaa\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
226            android:exported="false"
226-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f27da281d74b85eb78118b239a6eaaa\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
227            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
227-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f27da281d74b85eb78118b239a6eaaa\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
228
229        <receiver
229-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
230            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
230-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
231            android:enabled="true"
231-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
232            android:exported="false" >
232-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
233        </receiver>
234
235        <service
235-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
236            android:name="com.google.android.gms.measurement.AppMeasurementService"
236-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
237            android:enabled="true"
237-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
238            android:exported="false" />
238-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
239        <service
239-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
240            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
240-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
241            android:enabled="true"
241-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
242            android:exported="false"
242-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
243            android:permission="android.permission.BIND_JOB_SERVICE" />
243-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9c86de4aad0ce3a169cdd09760b9ed5\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
244
245        <provider
245-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
246            android:name="androidx.startup.InitializationProvider"
246-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
247            android:authorities="io.ionic.starter.androidx-startup"
247-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
248            android:exported="false" >
248-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
249            <meta-data
249-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
250                android:name="androidx.emoji2.text.EmojiCompatInitializer"
250-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
251                android:value="androidx.startup" />
251-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\94e8738a1e8769e3cb99a1932c9cb80b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
252            <meta-data
252-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea44852a4ef297c84b0de9976e6fb61\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
253                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
253-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea44852a4ef297c84b0de9976e6fb61\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
254                android:value="androidx.startup" />
254-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ea44852a4ef297c84b0de9976e6fb61\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
255            <meta-data
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
256                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
257                android:value="androidx.startup" />
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
258        </provider>
259
260        <uses-library
260-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdbd558caa2532388ee1b3ef8049c5ea\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
261            android:name="android.ext.adservices"
261-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdbd558caa2532388ee1b3ef8049c5ea\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
262            android:required="false" />
262-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdbd558caa2532388ee1b3ef8049c5ea\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
263
264        <meta-data
264-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff98a927589a446211f91dda472d317\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
265            android:name="com.google.android.gms.version"
265-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff98a927589a446211f91dda472d317\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
266            android:value="@integer/google_play_services_version" />
266-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ff98a927589a446211f91dda472d317\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
267
268        <receiver
268-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
269            android:name="androidx.profileinstaller.ProfileInstallReceiver"
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
270            android:directBootAware="false"
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
271            android:enabled="true"
271-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
272            android:exported="true"
272-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
273            android:permission="android.permission.DUMP" >
273-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
274            <intent-filter>
274-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
275                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
275-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
275-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
276            </intent-filter>
277            <intent-filter>
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
278                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
279            </intent-filter>
280            <intent-filter>
280-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
281                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
281-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
281-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
282            </intent-filter>
283            <intent-filter>
283-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
284                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
284-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
284-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a871c71eb45187477260e4e80c2f87\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
285            </intent-filter>
286        </receiver>
287    </application>
288
289</manifest>
