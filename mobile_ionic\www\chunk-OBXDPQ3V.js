var d=class{constructor(){this.gestureId=0,this.requestedStart=new Map,this.disabledGestures=new Map,this.disabledScroll=new Set}createGesture(t){var e;return new a(this,this.newID(),t.name,(e=t.priority)!==null&&e!==void 0?e:0,!!t.disableScroll)}createBlocker(t={}){return new c(this,this.newID(),t.disable,!!t.disableScroll)}start(t,e,s){return this.canStart(t)?(this.requestedStart.set(e,s),!0):(this.requestedStart.delete(e),!1)}capture(t,e,s){if(!this.start(t,e,s))return!1;let i=this.requestedStart,r=-1e4;if(i.forEach(l=>{r=Math.max(r,l)}),r===s){this.capturedId=e,i.clear();let l=new CustomEvent("ionGestureCaptured",{detail:{gestureName:t}});return document.dispatchEvent(l),!0}return i.delete(e),!1}release(t){this.requestedStart.delete(t),this.capturedId===t&&(this.capturedId=void 0)}disableGesture(t,e){let s=this.disabledGestures.get(t);s===void 0&&(s=new Set,this.disabledGestures.set(t,s)),s.add(e)}enableGesture(t,e){let s=this.disabledGestures.get(t);s!==void 0&&s.delete(e)}disableScroll(t){this.disabledScroll.add(t),this.disabledScroll.size===1&&document.body.classList.add(n)}enableScroll(t){this.disabledScroll.delete(t),this.disabledScroll.size===0&&document.body.classList.remove(n)}canStart(t){return!(this.capturedId!==void 0||this.isDisabled(t))}isCaptured(){return this.capturedId!==void 0}isScrollDisabled(){return this.disabledScroll.size>0}isDisabled(t){let e=this.disabledGestures.get(t);return!!(e&&e.size>0)}newID(){return this.gestureId++,this.gestureId}},a=class{constructor(t,e,s,i,r){this.id=e,this.name=s,this.disableScroll=r,this.priority=i*1e6+e,this.ctrl=t}canStart(){return this.ctrl?this.ctrl.canStart(this.name):!1}start(){return this.ctrl?this.ctrl.start(this.name,this.id,this.priority):!1}capture(){if(!this.ctrl)return!1;let t=this.ctrl.capture(this.name,this.id,this.priority);return t&&this.disableScroll&&this.ctrl.disableScroll(this.id),t}release(){this.ctrl&&(this.ctrl.release(this.id),this.disableScroll&&this.ctrl.enableScroll(this.id))}destroy(){this.release(),this.ctrl=void 0}},c=class{constructor(t,e,s,i){this.id=e,this.disable=s,this.disableScroll=i,this.ctrl=t}block(){if(this.ctrl){if(this.disable)for(let t of this.disable)this.ctrl.disableGesture(t,this.id);this.disableScroll&&this.ctrl.disableScroll(this.id)}}unblock(){if(this.ctrl){if(this.disable)for(let t of this.disable)this.ctrl.enableGesture(t,this.id);this.disableScroll&&this.ctrl.enableScroll(this.id)}}destroy(){this.unblock(),this.ctrl=void 0}},n="backdrop-no-scroll",u=new d;export{n as a,u as b};
