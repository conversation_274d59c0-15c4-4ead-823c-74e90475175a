import{$ as d,c as l,m as u,o as h}from"./chunk-YFIZFQXH.js";import{a as c,b as a,h as i}from"./chunk-LNJ3S2LQ.js";var w=(()=>{let n=class n{constructor(t){this.http=t,this.currentApiUrlSubject=new l(""),this.currentApiUrl$=this.currentApiUrlSubject.asObservable(),this.apiEndpoints=[{name:"ngrok (Recommended)",url:"https://*************************************************.ngrok-free.app/api",type:"ngrok",description:"Secure tunnel, works from anywhere"},{name:"Home IP (Current)",url:"http://***************:8000/api",type:"local-ip",description:"\u{1F3E0} Home network IP - current location"},{name:"School IP",url:"http://*************:8000/api",type:"local-ip",description:"\u{1F3EB} School network IP - when at school"},{name:"Localhost (Web Only)",url:"http://localhost:8000/api",type:"localhost",description:"Only works in web browser"}],this.setApiUrl(this.apiEndpoints[1].url)}getApiEndpoints(){return this.apiEndpoints.map(t=>a(c({},t),{isActive:t.url===this.getCurrentApiUrl()}))}setApiUrl(t){this.currentApiUrlSubject.next(t),localStorage.setItem("selectedApiUrl",t),console.log("\u{1F504} API URL switched to:",t)}getCurrentApiUrl(){let t=localStorage.getItem("selectedApiUrl");return t?(this.currentApiUrlSubject.next(t),t):this.currentApiUrlSubject.value||this.apiEndpoints[1].url}testEndpoint(t){return i(this,null,function*(){let r=Date.now();try{let e=t.replace("/api","/up");yield this.http.get(e,{responseType:"text"}).toPromise();let s=Date.now()-r;return{success:!0,message:`\u2705 Connected successfully (${s}ms)`,responseTime:s}}catch(e){let s=Date.now()-r,o="\u274C Connection failed";return e.status===0?o="\u274C Network error - Cannot reach server":e.status===404?o="\u274C Server found but endpoint not available":e.status>=500?o="\u274C Server error":o=`\u274C Error ${e.status}: ${e.statusText}`,{success:!1,message:`${o} (${s}ms)`,responseTime:s}}})}testAllEndpoints(){return i(this,null,function*(){let t=[];for(let r of this.apiEndpoints){let e=yield this.testEndpoint(r.url);t.push(a(c({},r),{testResult:e}))}return t})}autoDetectBestEndpoint(){return i(this,null,function*(){console.log("\u{1F50D} Auto-detecting best API endpoint...");let t=["ngrok","local-ip","localhost"];for(let r of t){let e=this.apiEndpoints.filter(s=>s.type===r);for(let s of e)if((yield this.testEndpoint(s.url)).success)return console.log("\u2705 Found working endpoint:",s.name),this.setApiUrl(s.url),s}return console.log("\u274C No working endpoints found"),null})}getConnectionDiagnostics(){let t=[],r=this.getCurrentApiUrl(),e=this.apiEndpoints.find(s=>s.url===r);return t.push("\u{1F517} CONNECTION DIAGNOSTICS"),t.push("========================"),t.push(""),t.push(`\u{1F4E1} Current API: ${r}`),t.push(`\u{1F3F7}\uFE0F Type: ${(e==null?void 0:e.type)||"unknown"}`),t.push(""),(e==null?void 0:e.type)==="ngrok"?(t.push("\u{1F527} NGROK TROUBLESHOOTING:"),t.push("1. Check if ngrok is running: http://127.0.0.1:4040"),t.push("2. Verify Laravel backend: php artisan serve --host=0.0.0.0 --port=8000"),t.push("3. Update ngrok URL if expired")):(e==null?void 0:e.type)==="local-ip"&&(t.push("\u{1F527} LOCAL IP TROUBLESHOOTING:"),t.push("1. Both devices must be on same WiFi"),t.push("2. Check Windows Firewall settings"),t.push("3. Verify backend server is running"),t.push("4. Try switching to ngrok for easier setup")),t.push(""),t.push("\u{1F4A1} QUICK ACTIONS:"),t.push("- Use Environment Switcher to test different URLs"),t.push("- Run auto-detection to find working endpoint"),t.push("- Check network diagnostics page"),t}};n.\u0275fac=function(r){return new(r||n)(h(d))},n.\u0275prov=u({token:n,factory:n.\u0275fac,providedIn:"root"});let p=n;return p})();export{w as a};
