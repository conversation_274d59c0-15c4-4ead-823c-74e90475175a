window.__Zone_disable_customElements=!0;var ce=globalThis;function te(t){return(ce.__Zone_symbol_prefix||"__zone_symbol__")+t}function dt(){let t=ce.performance;function r(Z){t&&t.mark&&t.mark(Z)}function i(Z,_){t&&t.measure&&t.measure(Z,_)}r("Zone");let n=(()=>{let _=class _{static assertZonePatched(){if(ce.Promise!==I.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let e=_.current;for(;e.parent;)e=e.parent;return e}static get current(){return b.zone}static get currentTask(){return S}static __load_patch(e,d,O=!1){if(I.hasOwnProperty(e)){let N=ce[te("forceDuplicateZoneCheck")]===!0;if(!O&&N)throw Error("Already loaded patch: "+e)}else if(!ce["__Zone_disable_"+e]){let N="Zone:"+e;r(N),I[e]=d(ce,_,R),i(N,N)}}get parent(){return this._parent}get name(){return this._name}constructor(e,d){this._parent=e,this._name=d?d.name||"unnamed":"<root>",this._properties=d&&d.properties||{},this._zoneDelegate=new f(this,this._parent&&this._parent._zoneDelegate,d)}get(e){let d=this.getZoneWith(e);if(d)return d._properties[e]}getZoneWith(e){let d=this;for(;d;){if(d._properties.hasOwnProperty(e))return d;d=d._parent}return null}fork(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)}wrap(e,d){if(typeof e!="function")throw new Error("Expecting function got: "+e);let O=this._zoneDelegate.intercept(this,e,d),N=this;return function(){return N.runGuarded(O,this,arguments,d)}}run(e,d,O,N){b={parent:b,zone:this};try{return this._zoneDelegate.invoke(this,e,d,O,N)}finally{b=b.parent}}runGuarded(e,d=null,O,N){b={parent:b,zone:this};try{try{return this._zoneDelegate.invoke(this,e,d,O,N)}catch(D){if(this._zoneDelegate.handleError(this,D))throw D}}finally{b=b.parent}}runTask(e,d,O){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||Q).name+"; Execution: "+this.name+")");let N=e,{type:D,data:{isPeriodic:Ee=!1,isRefreshable:ae=!1}={}}=e;if(e.state===G&&(D===q||D===y))return;let re=e.state!=V;re&&N._transitionTo(V,h);let Te=S;S=N,b={parent:b,zone:this};try{D==y&&e.data&&!Ee&&!ae&&(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,N,d,O)}catch(l){if(this._zoneDelegate.handleError(this,l))throw l}}finally{let l=e.state;if(l!==G&&l!==W)if(D==q||Ee||ae&&l===k)re&&N._transitionTo(h,V,k);else{let a=N._zoneDelegates;this._updateTaskCount(N,-1),re&&N._transitionTo(G,V,G),ae&&(N._zoneDelegates=a)}b=b.parent,S=Te}}scheduleTask(e){if(e.zone&&e.zone!==this){let O=this;for(;O;){if(O===e.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${e.zone.name}`);O=O.parent}}e._transitionTo(k,G);let d=[];e._zoneDelegates=d,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(O){throw e._transitionTo(W,k,G),this._zoneDelegate.handleError(this,O),O}return e._zoneDelegates===d&&this._updateTaskCount(e,1),e.state==k&&e._transitionTo(h,k),e}scheduleMicroTask(e,d,O,N){return this.scheduleTask(new g(B,e,d,O,N,void 0))}scheduleMacroTask(e,d,O,N,D){return this.scheduleTask(new g(y,e,d,O,N,D))}scheduleEventTask(e,d,O,N,D){return this.scheduleTask(new g(q,e,d,O,N,D))}cancelTask(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||Q).name+"; Execution: "+this.name+")");if(!(e.state!==h&&e.state!==V)){e._transitionTo(F,h,V);try{this._zoneDelegate.cancelTask(this,e)}catch(d){throw e._transitionTo(W,F),this._zoneDelegate.handleError(this,d),d}return this._updateTaskCount(e,-1),e._transitionTo(G,F),e.runCount=-1,e}}_updateTaskCount(e,d){let O=e._zoneDelegates;d==-1&&(e._zoneDelegates=null);for(let N=0;N<O.length;N++)O[N]._updateTaskCount(e.type,d)}};_.__symbol__=te;let Z=_;return Z})(),s={name:"",onHasTask:(Z,_,c,e)=>Z.hasTask(c,e),onScheduleTask:(Z,_,c,e)=>Z.scheduleTask(c,e),onInvokeTask:(Z,_,c,e,d,O)=>Z.invokeTask(c,e,d,O),onCancelTask:(Z,_,c,e)=>Z.cancelTask(c,e)};class f{get zone(){return this._zone}constructor(_,c,e){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this._zone=_,this._parentDelegate=c,this._forkZS=e&&(e&&e.onFork?e:c._forkZS),this._forkDlgt=e&&(e.onFork?c:c._forkDlgt),this._forkCurrZone=e&&(e.onFork?this._zone:c._forkCurrZone),this._interceptZS=e&&(e.onIntercept?e:c._interceptZS),this._interceptDlgt=e&&(e.onIntercept?c:c._interceptDlgt),this._interceptCurrZone=e&&(e.onIntercept?this._zone:c._interceptCurrZone),this._invokeZS=e&&(e.onInvoke?e:c._invokeZS),this._invokeDlgt=e&&(e.onInvoke?c:c._invokeDlgt),this._invokeCurrZone=e&&(e.onInvoke?this._zone:c._invokeCurrZone),this._handleErrorZS=e&&(e.onHandleError?e:c._handleErrorZS),this._handleErrorDlgt=e&&(e.onHandleError?c:c._handleErrorDlgt),this._handleErrorCurrZone=e&&(e.onHandleError?this._zone:c._handleErrorCurrZone),this._scheduleTaskZS=e&&(e.onScheduleTask?e:c._scheduleTaskZS),this._scheduleTaskDlgt=e&&(e.onScheduleTask?c:c._scheduleTaskDlgt),this._scheduleTaskCurrZone=e&&(e.onScheduleTask?this._zone:c._scheduleTaskCurrZone),this._invokeTaskZS=e&&(e.onInvokeTask?e:c._invokeTaskZS),this._invokeTaskDlgt=e&&(e.onInvokeTask?c:c._invokeTaskDlgt),this._invokeTaskCurrZone=e&&(e.onInvokeTask?this._zone:c._invokeTaskCurrZone),this._cancelTaskZS=e&&(e.onCancelTask?e:c._cancelTaskZS),this._cancelTaskDlgt=e&&(e.onCancelTask?c:c._cancelTaskDlgt),this._cancelTaskCurrZone=e&&(e.onCancelTask?this._zone:c._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;let d=e&&e.onHasTask,O=c&&c._hasTaskZS;(d||O)&&(this._hasTaskZS=d?e:s,this._hasTaskDlgt=c,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,e.onScheduleTask||(this._scheduleTaskZS=s,this._scheduleTaskDlgt=c,this._scheduleTaskCurrZone=this._zone),e.onInvokeTask||(this._invokeTaskZS=s,this._invokeTaskDlgt=c,this._invokeTaskCurrZone=this._zone),e.onCancelTask||(this._cancelTaskZS=s,this._cancelTaskDlgt=c,this._cancelTaskCurrZone=this._zone))}fork(_,c){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,_,c):new n(_,c)}intercept(_,c,e){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,_,c,e):c}invoke(_,c,e,d,O){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,_,c,e,d,O):c.apply(e,d)}handleError(_,c){return this._handleErrorZS?this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,_,c):!0}scheduleTask(_,c){let e=c;if(this._scheduleTaskZS)this._hasTaskZS&&e._zoneDelegates.push(this._hasTaskDlgtOwner),e=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,_,c),e||(e=c);else if(c.scheduleFn)c.scheduleFn(c);else if(c.type==B)J(c);else throw new Error("Task is missing scheduleFn.");return e}invokeTask(_,c,e,d){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,_,c,e,d):c.callback.apply(e,d)}cancelTask(_,c){let e;if(this._cancelTaskZS)e=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,_,c);else{if(!c.cancelFn)throw Error("Task is not cancelable");e=c.cancelFn(c)}return e}hasTask(_,c){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,_,c)}catch(e){this.handleError(_,e)}}_updateTaskCount(_,c){let e=this._taskCounts,d=e[_],O=e[_]=d+c;if(O<0)throw new Error("More tasks executed then were scheduled.");if(d==0||O==0){let N={microTask:e.microTask>0,macroTask:e.macroTask>0,eventTask:e.eventTask>0,change:_};this.hasTask(this._zone,N)}}}class g{constructor(_,c,e,d,O,N){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=_,this.source=c,this.data=d,this.scheduleFn=O,this.cancelFn=N,!e)throw new Error("callback is not defined");this.callback=e;let D=this;_===q&&d&&d.useG?this.invoke=g.invokeTask:this.invoke=function(){return g.invokeTask.call(ce,D,this,arguments)}}static invokeTask(_,c,e){_||(_=this),K++;try{return _.runCount++,_.zone.runTask(_,c,e)}finally{K==1&&X(),K--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(G,k)}_transitionTo(_,c,e){if(this._state===c||this._state===e)this._state=_,_==G&&(this._zoneDelegates=null);else throw new Error(`${this.type} '${this.source}': can not transition to '${_}', expecting state '${c}'${e?" or '"+e+"'":""}, was '${this._state}'.`)}toString(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}let m=te("setTimeout"),p=te("Promise"),C=te("then"),E=[],w=!1,L;function H(Z){if(L||ce[p]&&(L=ce[p].resolve(0)),L){let _=L[C];_||(_=L.then),_.call(L,Z)}else ce[m](Z,0)}function J(Z){K===0&&E.length===0&&H(X),Z&&E.push(Z)}function X(){if(!w){for(w=!0;E.length;){let Z=E;E=[];for(let _=0;_<Z.length;_++){let c=Z[_];try{c.zone.runTask(c,null,null)}catch(e){R.onUnhandledError(e)}}}R.microtaskDrainDone(),w=!1}}let Q={name:"NO ZONE"},G="notScheduled",k="scheduling",h="scheduled",V="running",F="canceling",W="unknown",B="microTask",y="macroTask",q="eventTask",I={},R={symbol:te,currentZoneFrame:()=>b,onUnhandledError:Y,microtaskDrainDone:Y,scheduleMicroTask:J,showUncaughtError:()=>!n[te("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:Y,patchMethod:()=>Y,bindArguments:()=>[],patchThen:()=>Y,patchMacroTask:()=>Y,patchEventPrototype:()=>Y,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>Y,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>Y,wrapWithCurrentZone:()=>Y,filterProperties:()=>[],attachOriginToPatched:()=>Y,_redefineProperty:()=>Y,patchCallbacks:()=>Y,nativeScheduleMicroTask:H},b={parent:null,zone:new n(null,null)},S=null,K=0;function Y(){}return i("Zone","Zone"),n}function _t(){var i;let t=globalThis,r=t[te("forceDuplicateZoneCheck")]===!0;if(t.Zone&&(r||typeof t.Zone.__symbol__!="function"))throw new Error("Zone already loaded.");return(i=t.Zone)!=null||(t.Zone=dt()),t.Zone}var be=Object.getOwnPropertyDescriptor,Ae=Object.defineProperty,je=Object.getPrototypeOf,Et=Object.create,Tt=Array.prototype.slice,He="addEventListener",xe="removeEventListener",Le=te(He),Ie=te(xe),le="true",ue="false",Pe=te("");function Ve(t,r){return Zone.current.wrap(t,r)}function Ge(t,r,i,n,s){return Zone.current.scheduleMacroTask(t,r,i,n,s)}var x=te,De=typeof window<"u",pe=De?window:void 0,$=De&&pe||globalThis,gt="removeAttribute";function Fe(t,r){for(let i=t.length-1;i>=0;i--)typeof t[i]=="function"&&(t[i]=Ve(t[i],r+"_"+i));return t}function mt(t,r){let i=t.constructor.name;for(let n=0;n<r.length;n++){let s=r[n],f=t[s];if(f){let g=be(t,s);if(!tt(g))continue;t[s]=(m=>{let p=function(){return m.apply(this,Fe(arguments,i+"."+s))};return he(p,m),p})(f)}}}function tt(t){return t?t.writable===!1?!1:!(typeof t.get=="function"&&typeof t.set>"u"):!0}var nt=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,Se=!("nw"in $)&&typeof $.process<"u"&&$.process.toString()==="[object process]",Be=!Se&&!nt&&!!(De&&pe.HTMLElement),rt=typeof $.process<"u"&&$.process.toString()==="[object process]"&&!nt&&!!(De&&pe.HTMLElement),Ce={},yt=x("enable_beforeunload"),Ye=function(t){if(t=t||$.event,!t)return;let r=Ce[t.type];r||(r=Ce[t.type]=x("ON_PROPERTY"+t.type));let i=this||t.target||$,n=i[r],s;if(Be&&i===pe&&t.type==="error"){let f=t;s=n&&n.call(this,f.message,f.filename,f.lineno,f.colno,f.error),s===!0&&t.preventDefault()}else s=n&&n.apply(this,arguments),t.type==="beforeunload"&&$[yt]&&typeof s=="string"?t.returnValue=s:s!=null&&!s&&t.preventDefault();return s};function $e(t,r,i){let n=be(t,r);if(!n&&i&&be(i,r)&&(n={enumerable:!0,configurable:!0}),!n||!n.configurable)return;let s=x("on"+r+"patched");if(t.hasOwnProperty(s)&&t[s])return;delete n.writable,delete n.value;let f=n.get,g=n.set,m=r.slice(2),p=Ce[m];p||(p=Ce[m]=x("ON_PROPERTY"+m)),n.set=function(C){let E=this;if(!E&&t===$&&(E=$),!E)return;typeof E[p]=="function"&&E.removeEventListener(m,Ye),g&&g.call(E,null),E[p]=C,typeof C=="function"&&E.addEventListener(m,Ye,!1)},n.get=function(){let C=this;if(!C&&t===$&&(C=$),!C)return null;let E=C[p];if(E)return E;if(f){let w=f.call(this);if(w)return n.set.call(this,w),typeof C[gt]=="function"&&C.removeAttribute(r),w}return null},Ae(t,r,n),t[s]=!0}function ot(t,r,i){if(r)for(let n=0;n<r.length;n++)$e(t,"on"+r[n],i);else{let n=[];for(let s in t)s.slice(0,2)=="on"&&n.push(s);for(let s=0;s<n.length;s++)$e(t,n[s],i)}}var se=x("originalInstance");function ve(t){let r=$[t];if(!r)return;$[x(t)]=r,$[t]=function(){let s=Fe(arguments,t);switch(s.length){case 0:this[se]=new r;break;case 1:this[se]=new r(s[0]);break;case 2:this[se]=new r(s[0],s[1]);break;case 3:this[se]=new r(s[0],s[1],s[2]);break;case 4:this[se]=new r(s[0],s[1],s[2],s[3]);break;default:throw new Error("Arg list too long.")}},he($[t],r);let i=new r(function(){}),n;for(n in i)t==="XMLHttpRequest"&&n==="responseBlob"||function(s){typeof i[s]=="function"?$[t].prototype[s]=function(){return this[se][s].apply(this[se],arguments)}:Ae($[t].prototype,s,{set:function(f){typeof f=="function"?(this[se][s]=Ve(f,t+"."+s),he(this[se][s],f)):this[se][s]=f},get:function(){return this[se][s]}})}(n);for(n in r)n!=="prototype"&&r.hasOwnProperty(n)&&($[t][n]=r[n])}function fe(t,r,i){let n=t;for(;n&&!n.hasOwnProperty(r);)n=je(n);!n&&t[r]&&(n=t);let s=x(r),f=null;if(n&&(!(f=n[s])||!n.hasOwnProperty(s))){f=n[s]=n[r];let g=n&&be(n,r);if(tt(g)){let m=i(f,s,r);n[r]=function(){return m(this,arguments)},he(n[r],f)}}return f}function pt(t,r,i){let n=null;function s(f){let g=f.data;return g.args[g.cbIdx]=function(){f.invoke.apply(this,arguments)},n.apply(g.target,g.args),f}n=fe(t,r,f=>function(g,m){let p=i(g,m);return p.cbIdx>=0&&typeof m[p.cbIdx]=="function"?Ge(p.name,m[p.cbIdx],p,s):f.apply(g,m)})}function he(t,r){t[x("OriginalDelegate")]=r}var Je=!1,Me=!1;function kt(){try{let t=pe.navigator.userAgent;if(t.indexOf("MSIE ")!==-1||t.indexOf("Trident/")!==-1)return!0}catch{}return!1}function vt(){if(Je)return Me;Je=!0;try{let t=pe.navigator.userAgent;(t.indexOf("MSIE ")!==-1||t.indexOf("Trident/")!==-1||t.indexOf("Edge/")!==-1)&&(Me=!0)}catch{}return Me}function Ke(t){return typeof t=="function"}function Qe(t){return typeof t=="number"}var ye=!1;if(typeof window<"u")try{let t=Object.defineProperty({},"passive",{get:function(){ye=!0}});window.addEventListener("test",t,t),window.removeEventListener("test",t,t)}catch{ye=!1}var bt={useG:!0},ne={},st={},it=new RegExp("^"+Pe+"(\\w+)(true|false)$"),ct=x("propagationStopped");function at(t,r){let i=(r?r(t):t)+ue,n=(r?r(t):t)+le,s=Pe+i,f=Pe+n;ne[t]={},ne[t][ue]=s,ne[t][le]=f}function Pt(t,r,i,n){let s=n&&n.add||He,f=n&&n.rm||xe,g=n&&n.listeners||"eventListeners",m=n&&n.rmAll||"removeAllListeners",p=x(s),C="."+s+":",E="prependListener",w="."+E+":",L=function(k,h,V){if(k.isRemoved)return;let F=k.callback;typeof F=="object"&&F.handleEvent&&(k.callback=y=>F.handleEvent(y),k.originalDelegate=F);let W;try{k.invoke(k,h,[V])}catch(y){W=y}let B=k.options;if(B&&typeof B=="object"&&B.once){let y=k.originalDelegate?k.originalDelegate:k.callback;h[f].call(h,V.type,y,B)}return W};function H(k,h,V){if(h=h||t.event,!h)return;let F=k||h.target||t,W=F[ne[h.type][V?le:ue]];if(W){let B=[];if(W.length===1){let y=L(W[0],F,h);y&&B.push(y)}else{let y=W.slice();for(let q=0;q<y.length&&!(h&&h[ct]===!0);q++){let I=L(y[q],F,h);I&&B.push(I)}}if(B.length===1)throw B[0];for(let y=0;y<B.length;y++){let q=B[y];r.nativeScheduleMicroTask(()=>{throw q})}}}let J=function(k){return H(this,k,!1)},X=function(k){return H(this,k,!0)};function Q(k,h){if(!k)return!1;let V=!0;h&&h.useG!==void 0&&(V=h.useG);let F=h&&h.vh,W=!0;h&&h.chkDup!==void 0&&(W=h.chkDup);let B=!1;h&&h.rt!==void 0&&(B=h.rt);let y=k;for(;y&&!y.hasOwnProperty(s);)y=je(y);if(!y&&k[s]&&(y=k),!y||y[p])return!1;let q=h&&h.eventNameToString,I={},R=y[p]=y[s],b=y[x(f)]=y[f],S=y[x(g)]=y[g],K=y[x(m)]=y[m],Y;h&&h.prepend&&(Y=y[x(h.prepend)]=y[h.prepend]);function Z(o,u){return!ye&&typeof o=="object"&&o?!!o.capture:!ye||!u?o:typeof o=="boolean"?{capture:o,passive:!0}:o?typeof o=="object"&&o.passive!==!1?{...o,passive:!0}:o:{passive:!0}}let _=function(o){if(!I.isExisting)return R.call(I.target,I.eventName,I.capture?X:J,I.options)},c=function(o){if(!o.isRemoved){let u=ne[o.eventName],v;u&&(v=u[o.capture?le:ue]);let P=v&&o.target[v];if(P){for(let T=0;T<P.length;T++)if(P[T]===o){P.splice(T,1),o.isRemoved=!0,o.removeAbortListener&&(o.removeAbortListener(),o.removeAbortListener=null),P.length===0&&(o.allRemoved=!0,o.target[v]=null);break}}}if(o.allRemoved)return b.call(o.target,o.eventName,o.capture?X:J,o.options)},e=function(o){return R.call(I.target,I.eventName,o.invoke,I.options)},d=function(o){return Y.call(I.target,I.eventName,o.invoke,I.options)},O=function(o){return b.call(o.target,o.eventName,o.invoke,o.options)},N=V?_:e,D=V?c:O,Ee=function(o,u){let v=typeof u;return v==="function"&&o.callback===u||v==="object"&&o.originalDelegate===u},ae=h&&h.diff?h.diff:Ee,re=Zone[x("UNPATCHED_EVENTS")],Te=t[x("PASSIVE_EVENTS")];function l(o){if(typeof o=="object"&&o!==null){let u={...o};return o.signal&&(u.signal=o.signal),u}return o}let a=function(o,u,v,P,T=!1,M=!1){return function(){let A=this||t,j=arguments[0];h&&h.transferEventName&&(j=h.transferEventName(j));let U=arguments[1];if(!U)return o.apply(this,arguments);if(Se&&j==="uncaughtException")return o.apply(this,arguments);let z=!1;if(typeof U!="function"){if(!U.handleEvent)return o.apply(this,arguments);z=!0}if(F&&!F(o,U,A,arguments))return;let de=ye&&!!Te&&Te.indexOf(j)!==-1,ee=l(Z(arguments[2],de)),_e=ee==null?void 0:ee.signal;if(_e!=null&&_e.aborted)return;if(re){for(let ie=0;ie<re.length;ie++)if(j===re[ie])return de?o.call(A,j,U,ee):o.apply(this,arguments)}let Oe=ee?typeof ee=="boolean"?!0:ee.capture:!1,Ue=ee&&typeof ee=="object"?ee.once:!1,ht=Zone.current,Ne=ne[j];Ne||(at(j,q),Ne=ne[j]);let ze=Ne[Oe?le:ue],ge=A[ze],We=!1;if(ge){if(We=!0,W){for(let ie=0;ie<ge.length;ie++)if(ae(ge[ie],U))return}}else ge=A[ze]=[];let we,qe=A.constructor.name,Xe=st[qe];Xe&&(we=Xe[j]),we||(we=qe+u+(q?q(j):j)),I.options=ee,Ue&&(I.options.once=!1),I.target=A,I.capture=Oe,I.eventName=j,I.isExisting=We;let ke=V?bt:void 0;ke&&(ke.taskData=I),_e&&(I.options.signal=void 0);let oe=ht.scheduleEventTask(we,U,ke,v,P);if(_e){I.options.signal=_e;let ie=()=>oe.zone.cancelTask(oe);o.call(_e,"abort",ie,{once:!0}),oe.removeAbortListener=()=>_e.removeEventListener("abort",ie)}if(I.target=null,ke&&(ke.taskData=null),Ue&&(I.options.once=!0),!ye&&typeof oe.options=="boolean"||(oe.options=ee),oe.target=A,oe.capture=Oe,oe.eventName=j,z&&(oe.originalDelegate=U),M?ge.unshift(oe):ge.push(oe),T)return A}};return y[s]=a(R,C,N,D,B),Y&&(y[E]=a(Y,w,d,D,B,!0)),y[f]=function(){let o=this||t,u=arguments[0];h&&h.transferEventName&&(u=h.transferEventName(u));let v=arguments[2],P=v?typeof v=="boolean"?!0:v.capture:!1,T=arguments[1];if(!T)return b.apply(this,arguments);if(F&&!F(b,T,o,arguments))return;let M=ne[u],A;M&&(A=M[P?le:ue]);let j=A&&o[A];if(j)for(let U=0;U<j.length;U++){let z=j[U];if(ae(z,T)){if(j.splice(U,1),z.isRemoved=!0,j.length===0&&(z.allRemoved=!0,o[A]=null,!P&&typeof u=="string")){let de=Pe+"ON_PROPERTY"+u;o[de]=null}return z.zone.cancelTask(z),B?o:void 0}}return b.apply(this,arguments)},y[g]=function(){let o=this||t,u=arguments[0];h&&h.transferEventName&&(u=h.transferEventName(u));let v=[],P=lt(o,q?q(u):u);for(let T=0;T<P.length;T++){let M=P[T],A=M.originalDelegate?M.originalDelegate:M.callback;v.push(A)}return v},y[m]=function(){let o=this||t,u=arguments[0];if(u){h&&h.transferEventName&&(u=h.transferEventName(u));let v=ne[u];if(v){let P=v[ue],T=v[le],M=o[P],A=o[T];if(M){let j=M.slice();for(let U=0;U<j.length;U++){let z=j[U],de=z.originalDelegate?z.originalDelegate:z.callback;this[f].call(this,u,de,z.options)}}if(A){let j=A.slice();for(let U=0;U<j.length;U++){let z=j[U],de=z.originalDelegate?z.originalDelegate:z.callback;this[f].call(this,u,de,z.options)}}}}else{let v=Object.keys(o);for(let P=0;P<v.length;P++){let T=v[P],M=it.exec(T),A=M&&M[1];A&&A!=="removeListener"&&this[m].call(this,A)}this[m].call(this,"removeListener")}if(B)return this},he(y[s],R),he(y[f],b),K&&he(y[m],K),S&&he(y[g],S),!0}let G=[];for(let k=0;k<i.length;k++)G[k]=Q(i[k],n);return G}function lt(t,r){if(!r){let f=[];for(let g in t){let m=it.exec(g),p=m&&m[1];if(p&&(!r||p===r)){let C=t[g];if(C)for(let E=0;E<C.length;E++)f.push(C[E])}}return f}let i=ne[r];i||(at(r),i=ne[r]);let n=t[i[ue]],s=t[i[le]];return n?s?n.concat(s):n.slice():s?s.slice():[]}function wt(t,r){let i=t.Event;i&&i.prototype&&r.patchMethod(i.prototype,"stopImmediatePropagation",n=>function(s,f){s[ct]=!0,n&&n.apply(s,f)})}function Rt(t,r){r.patchMethod(t,"queueMicrotask",i=>function(n,s){Zone.current.scheduleMicroTask("queueMicrotask",s[0])})}var Re=x("zoneTask");function me(t,r,i,n){let s=null,f=null;r+=n,i+=n;let g={};function m(C){let E=C.data;E.args[0]=function(){return C.invoke.apply(this,arguments)};let w=s.apply(t,E.args);return Qe(w)?E.handleId=w:(E.handle=w,E.isRefreshable=Ke(w.refresh)),C}function p(C){let{handle:E,handleId:w}=C.data;return f.call(t,E!=null?E:w)}s=fe(t,r,C=>function(E,w){var L;if(Ke(w[0])){let H={isRefreshable:!1,isPeriodic:n==="Interval",delay:n==="Timeout"||n==="Interval"?w[1]||0:void 0,args:w},J=w[0];w[0]=function(){try{return J.apply(this,arguments)}finally{let{handle:F,handleId:W,isPeriodic:B,isRefreshable:y}=H;!B&&!y&&(W?delete g[W]:F&&(F[Re]=null))}};let X=Ge(r,w[0],H,m,p);if(!X)return X;let{handleId:Q,handle:G,isRefreshable:k,isPeriodic:h}=X.data;if(Q)g[Q]=X;else if(G&&(G[Re]=X,k&&!h)){let V=G.refresh;G.refresh=function(){let{zone:F,state:W}=X;return W==="notScheduled"?(X._state="scheduled",F._updateTaskCount(X,1)):W==="running"&&(X._state="scheduling"),V.call(this)}}return(L=G!=null?G:Q)!=null?L:X}else return C.apply(t,w)}),f=fe(t,i,C=>function(E,w){let L=w[0],H;Qe(L)?(H=g[L],delete g[L]):(H=L==null?void 0:L[Re],H?L[Re]=null:H=L),H!=null&&H.type?H.cancelFn&&H.zone.cancelTask(H):C.apply(t,w)})}function Ct(t,r){let{isBrowser:i,isMix:n}=r.getGlobalObjects();if(!i&&!n||!t.customElements||!("customElements"in t))return;let s=["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback","formAssociatedCallback","formDisabledCallback","formResetCallback","formStateRestoreCallback"];r.patchCallbacks(r,t.customElements,"customElements","define",s)}function Dt(t,r){if(Zone[r.symbol("patchEventTarget")])return;let{eventNames:i,zoneSymbolEventNames:n,TRUE_STR:s,FALSE_STR:f,ZONE_SYMBOL_PREFIX:g}=r.getGlobalObjects();for(let p=0;p<i.length;p++){let C=i[p],E=C+f,w=C+s,L=g+E,H=g+w;n[C]={},n[C][f]=L,n[C][s]=H}let m=t.EventTarget;if(!(!m||!m.prototype))return r.patchEventTarget(t,r,[m&&m.prototype]),!0}function St(t,r){r.patchEventPrototype(t,r)}function ut(t,r,i){if(!i||i.length===0)return r;let n=i.filter(f=>f.target===t);if(!n||n.length===0)return r;let s=n[0].ignoreProperties;return r.filter(f=>s.indexOf(f)===-1)}function et(t,r,i,n){if(!t)return;let s=ut(t,r,i);ot(t,s,n)}function Ze(t){return Object.getOwnPropertyNames(t).filter(r=>r.startsWith("on")&&r.length>2).map(r=>r.substring(2))}function Ot(t,r){if(Se&&!rt||Zone[t.symbol("patchEvents")])return;let i=r.__Zone_ignore_on_properties,n=[];if(Be){let s=window;n=n.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);let f=kt()?[{target:s,ignoreProperties:["error"]}]:[];et(s,Ze(s),i&&i.concat(f),je(s))}n=n.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let s=0;s<n.length;s++){let f=r[n[s]];f&&f.prototype&&et(f.prototype,Ze(f.prototype),i)}}function Nt(t){t.__load_patch("legacy",r=>{let i=r[t.__symbol__("legacyPatch")];i&&i()}),t.__load_patch("timers",r=>{let i="set",n="clear";me(r,i,n,"Timeout"),me(r,i,n,"Interval"),me(r,i,n,"Immediate")}),t.__load_patch("requestAnimationFrame",r=>{me(r,"request","cancel","AnimationFrame"),me(r,"mozRequest","mozCancel","AnimationFrame"),me(r,"webkitRequest","webkitCancel","AnimationFrame")}),t.__load_patch("blocking",(r,i)=>{let n=["alert","prompt","confirm"];for(let s=0;s<n.length;s++){let f=n[s];fe(r,f,(g,m,p)=>function(C,E){return i.current.run(g,r,E,p)})}}),t.__load_patch("EventTarget",(r,i,n)=>{St(r,n),Dt(r,n);let s=r.XMLHttpRequestEventTarget;s&&s.prototype&&n.patchEventTarget(r,n,[s.prototype])}),t.__load_patch("MutationObserver",(r,i,n)=>{ve("MutationObserver"),ve("WebKitMutationObserver")}),t.__load_patch("IntersectionObserver",(r,i,n)=>{ve("IntersectionObserver")}),t.__load_patch("FileReader",(r,i,n)=>{ve("FileReader")}),t.__load_patch("on_property",(r,i,n)=>{Ot(n,r)}),t.__load_patch("customElements",(r,i,n)=>{Ct(r,n)}),t.__load_patch("XHR",(r,i)=>{C(r);let n=x("xhrTask"),s=x("xhrSync"),f=x("xhrListener"),g=x("xhrScheduled"),m=x("xhrURL"),p=x("xhrErrorBeforeScheduled");function C(E){let w=E.XMLHttpRequest;if(!w)return;let L=w.prototype;function H(R){return R[n]}let J=L[Le],X=L[Ie];if(!J){let R=E.XMLHttpRequestEventTarget;if(R){let b=R.prototype;J=b[Le],X=b[Ie]}}let Q="readystatechange",G="scheduled";function k(R){let b=R.data,S=b.target;S[g]=!1,S[p]=!1;let K=S[f];J||(J=S[Le],X=S[Ie]),K&&X.call(S,Q,K);let Y=S[f]=()=>{if(S.readyState===S.DONE)if(!b.aborted&&S[g]&&R.state===G){let _=S[i.__symbol__("loadfalse")];if(S.status!==0&&_&&_.length>0){let c=R.invoke;R.invoke=function(){let e=S[i.__symbol__("loadfalse")];for(let d=0;d<e.length;d++)e[d]===R&&e.splice(d,1);!b.aborted&&R.state===G&&c.call(R)},_.push(R)}else R.invoke()}else!b.aborted&&S[g]===!1&&(S[p]=!0)};return J.call(S,Q,Y),S[n]||(S[n]=R),q.apply(S,b.args),S[g]=!0,R}function h(){}function V(R){let b=R.data;return b.aborted=!0,I.apply(b.target,b.args)}let F=fe(L,"open",()=>function(R,b){return R[s]=b[2]==!1,R[m]=b[1],F.apply(R,b)}),W="XMLHttpRequest.send",B=x("fetchTaskAborting"),y=x("fetchTaskScheduling"),q=fe(L,"send",()=>function(R,b){if(i.current[y]===!0||R[s])return q.apply(R,b);{let S={target:R,url:R[m],isPeriodic:!1,args:b,aborted:!1},K=Ge(W,h,S,k,V);R&&R[p]===!0&&!S.aborted&&K.state===G&&K.invoke()}}),I=fe(L,"abort",()=>function(R,b){let S=H(R);if(S&&typeof S.type=="string"){if(S.cancelFn==null||S.data&&S.data.aborted)return;S.zone.cancelTask(S)}else if(i.current[B]===!0)return I.apply(R,b)})}}),t.__load_patch("geolocation",r=>{r.navigator&&r.navigator.geolocation&&mt(r.navigator.geolocation,["getCurrentPosition","watchPosition"])}),t.__load_patch("PromiseRejectionEvent",(r,i)=>{function n(s){return function(f){lt(r,s).forEach(m=>{let p=r.PromiseRejectionEvent;if(p){let C=new p(s,{promise:f.promise,reason:f.rejection});m.invoke(C)}})}}r.PromiseRejectionEvent&&(i[x("unhandledPromiseRejectionHandler")]=n("unhandledrejection"),i[x("rejectionHandledHandler")]=n("rejectionhandled"))}),t.__load_patch("queueMicrotask",(r,i,n)=>{Rt(r,n)})}function Lt(t){t.__load_patch("ZoneAwarePromise",(r,i,n)=>{let s=Object.getOwnPropertyDescriptor,f=Object.defineProperty;function g(l){if(l&&l.toString===Object.prototype.toString){let a=l.constructor&&l.constructor.name;return(a||"")+": "+JSON.stringify(l)}return l?l.toString():Object.prototype.toString.call(l)}let m=n.symbol,p=[],C=r[m("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")]!==!1,E=m("Promise"),w=m("then"),L="__creationTrace__";n.onUnhandledError=l=>{if(n.showUncaughtError()){let a=l&&l.rejection;a?console.error("Unhandled Promise rejection:",a instanceof Error?a.message:a,"; Zone:",l.zone.name,"; Task:",l.task&&l.task.source,"; Value:",a,a instanceof Error?a.stack:void 0):console.error(l)}},n.microtaskDrainDone=()=>{for(;p.length;){let l=p.shift();try{l.zone.runGuarded(()=>{throw l.throwOriginal?l.rejection:l})}catch(a){J(a)}}};let H=m("unhandledPromiseRejectionHandler");function J(l){n.onUnhandledError(l);try{let a=i[H];typeof a=="function"&&a.call(this,l)}catch{}}function X(l){return l&&l.then}function Q(l){return l}function G(l){return D.reject(l)}let k=m("state"),h=m("value"),V=m("finally"),F=m("parentPromiseValue"),W=m("parentPromiseState"),B="Promise.then",y=null,q=!0,I=!1,R=0;function b(l,a){return o=>{try{Z(l,a,o)}catch(u){Z(l,!1,u)}}}let S=function(){let l=!1;return function(o){return function(){l||(l=!0,o.apply(null,arguments))}}},K="Promise resolved with itself",Y=m("currentTaskTrace");function Z(l,a,o){let u=S();if(l===o)throw new TypeError(K);if(l[k]===y){let v=null;try{(typeof o=="object"||typeof o=="function")&&(v=o&&o.then)}catch(P){return u(()=>{Z(l,!1,P)})(),l}if(a!==I&&o instanceof D&&o.hasOwnProperty(k)&&o.hasOwnProperty(h)&&o[k]!==y)c(o),Z(l,o[k],o[h]);else if(a!==I&&typeof v=="function")try{v.call(o,u(b(l,a)),u(b(l,!1)))}catch(P){u(()=>{Z(l,!1,P)})()}else{l[k]=a;let P=l[h];if(l[h]=o,l[V]===V&&a===q&&(l[k]=l[W],l[h]=l[F]),a===I&&o instanceof Error){let T=i.currentTask&&i.currentTask.data&&i.currentTask.data[L];T&&f(o,Y,{configurable:!0,enumerable:!1,writable:!0,value:T})}for(let T=0;T<P.length;)e(l,P[T++],P[T++],P[T++],P[T++]);if(P.length==0&&a==I){l[k]=R;let T=o;try{throw new Error("Uncaught (in promise): "+g(o)+(o&&o.stack?`
`+o.stack:""))}catch(M){T=M}C&&(T.throwOriginal=!0),T.rejection=o,T.promise=l,T.zone=i.current,T.task=i.currentTask,p.push(T),n.scheduleMicroTask()}}}return l}let _=m("rejectionHandledHandler");function c(l){if(l[k]===R){try{let a=i[_];a&&typeof a=="function"&&a.call(this,{rejection:l[h],promise:l})}catch{}l[k]=I;for(let a=0;a<p.length;a++)l===p[a].promise&&p.splice(a,1)}}function e(l,a,o,u,v){c(l);let P=l[k],T=P?typeof u=="function"?u:Q:typeof v=="function"?v:G;a.scheduleMicroTask(B,()=>{try{let M=l[h],A=!!o&&V===o[V];A&&(o[F]=M,o[W]=P);let j=a.run(T,void 0,A&&T!==G&&T!==Q?[]:[M]);Z(o,!0,j)}catch(M){Z(o,!1,M)}},o)}let d="function ZoneAwarePromise() { [native code] }",O=function(){},N=r.AggregateError;class D{static toString(){return d}static resolve(a){return a instanceof D?a:Z(new this(null),q,a)}static reject(a){return Z(new this(null),I,a)}static withResolvers(){let a={};return a.promise=new D((o,u)=>{a.resolve=o,a.reject=u}),a}static any(a){if(!a||typeof a[Symbol.iterator]!="function")return Promise.reject(new N([],"All promises were rejected"));let o=[],u=0;try{for(let T of a)u++,o.push(D.resolve(T))}catch{return Promise.reject(new N([],"All promises were rejected"))}if(u===0)return Promise.reject(new N([],"All promises were rejected"));let v=!1,P=[];return new D((T,M)=>{for(let A=0;A<o.length;A++)o[A].then(j=>{v||(v=!0,T(j))},j=>{P.push(j),u--,u===0&&(v=!0,M(new N(P,"All promises were rejected")))})})}static race(a){let o,u,v=new this((M,A)=>{o=M,u=A});function P(M){o(M)}function T(M){u(M)}for(let M of a)X(M)||(M=this.resolve(M)),M.then(P,T);return v}static all(a){return D.allWithCallback(a)}static allSettled(a){return(this&&this.prototype instanceof D?this:D).allWithCallback(a,{thenCallback:u=>({status:"fulfilled",value:u}),errorCallback:u=>({status:"rejected",reason:u})})}static allWithCallback(a,o){let u,v,P=new this((j,U)=>{u=j,v=U}),T=2,M=0,A=[];for(let j of a){X(j)||(j=this.resolve(j));let U=M;try{j.then(z=>{A[U]=o?o.thenCallback(z):z,T--,T===0&&u(A)},z=>{o?(A[U]=o.errorCallback(z),T--,T===0&&u(A)):v(z)})}catch(z){v(z)}T++,M++}return T-=2,T===0&&u(A),P}constructor(a){let o=this;if(!(o instanceof D))throw new Error("Must be an instanceof Promise.");o[k]=y,o[h]=[];try{let u=S();a&&a(u(b(o,q)),u(b(o,I)))}catch(u){Z(o,!1,u)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return D}then(a,o){var T;let u=(T=this.constructor)==null?void 0:T[Symbol.species];(!u||typeof u!="function")&&(u=this.constructor||D);let v=new u(O),P=i.current;return this[k]==y?this[h].push(P,v,a,o):e(this,P,v,a,o),v}catch(a){return this.then(null,a)}finally(a){var P;let o=(P=this.constructor)==null?void 0:P[Symbol.species];(!o||typeof o!="function")&&(o=D);let u=new o(O);u[V]=V;let v=i.current;return this[k]==y?this[h].push(v,u,a,a):e(this,v,u,a,a),u}}D.resolve=D.resolve,D.reject=D.reject,D.race=D.race,D.all=D.all;let Ee=r[E]=r.Promise;r.Promise=D;let ae=m("thenPatched");function re(l){let a=l.prototype,o=s(a,"then");if(o&&(o.writable===!1||!o.configurable))return;let u=a.then;a[w]=u,l.prototype.then=function(v,P){return new D((M,A)=>{u.call(this,M,A)}).then(v,P)},l[ae]=!0}n.patchThen=re;function Te(l){return function(a,o){let u=l.apply(a,o);if(u instanceof D)return u;let v=u.constructor;return v[ae]||re(v),u}}return Ee&&(re(Ee),fe(r,"fetch",l=>Te(l))),Promise[i.__symbol__("uncaughtPromiseErrors")]=p,D})}function It(t){t.__load_patch("toString",r=>{let i=Function.prototype.toString,n=x("OriginalDelegate"),s=x("Promise"),f=x("Error"),g=function(){if(typeof this=="function"){let E=this[n];if(E)return typeof E=="function"?i.call(E):Object.prototype.toString.call(E);if(this===Promise){let w=r[s];if(w)return i.call(w)}if(this===Error){let w=r[f];if(w)return i.call(w)}}return i.call(this)};g[n]=i,Function.prototype.toString=g;let m=Object.prototype.toString,p="[object Promise]";Object.prototype.toString=function(){return typeof Promise=="function"&&this instanceof Promise?p:m.call(this)}})}function Mt(t,r,i,n,s){let f=Zone.__symbol__(n);if(r[f])return;let g=r[f]=r[n];r[n]=function(m,p,C){return p&&p.prototype&&s.forEach(function(E){let w=`${i}.${n}::`+E,L=p.prototype;try{if(L.hasOwnProperty(E)){let H=t.ObjectGetOwnPropertyDescriptor(L,E);H&&H.value?(H.value=t.wrapWithCurrentZone(H.value,w),t._redefineProperty(p.prototype,E,H)):L[E]&&(L[E]=t.wrapWithCurrentZone(L[E],w))}else L[E]&&(L[E]=t.wrapWithCurrentZone(L[E],w))}catch{}}),g.call(r,m,p,C)},t.attachOriginToPatched(r[n],g)}function Zt(t){t.__load_patch("util",(r,i,n)=>{let s=Ze(r);n.patchOnProperties=ot,n.patchMethod=fe,n.bindArguments=Fe,n.patchMacroTask=pt;let f=i.__symbol__("BLACK_LISTED_EVENTS"),g=i.__symbol__("UNPATCHED_EVENTS");r[g]&&(r[f]=r[g]),r[f]&&(i[f]=i[g]=r[f]),n.patchEventPrototype=wt,n.patchEventTarget=Pt,n.isIEOrEdge=vt,n.ObjectDefineProperty=Ae,n.ObjectGetOwnPropertyDescriptor=be,n.ObjectCreate=Et,n.ArraySlice=Tt,n.patchClass=ve,n.wrapWithCurrentZone=Ve,n.filterProperties=ut,n.attachOriginToPatched=he,n._redefineProperty=Object.defineProperty,n.patchCallbacks=Mt,n.getGlobalObjects=()=>({globalSources:st,zoneSymbolEventNames:ne,eventNames:s,isBrowser:Be,isMix:rt,isNode:Se,TRUE_STR:le,FALSE_STR:ue,ZONE_SYMBOL_PREFIX:Pe,ADD_EVENT_LISTENER_STR:He,REMOVE_EVENT_LISTENER_STR:xe})})}function At(t){Lt(t),It(t),Zt(t)}var ft=_t();At(ft);Nt(ft);
