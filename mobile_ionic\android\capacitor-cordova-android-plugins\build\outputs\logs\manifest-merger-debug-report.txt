-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:2:1-8:12
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:2:1-8:12
	package
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml
	xmlns:amazon
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:3:1-57
	xmlns:android
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:4:1-6:15
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:4:15-50
service#de.appplant.cordova.plugin.background.ForegroundService
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:5:1-82
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:5:10-80
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:7:1-63
	android:name
		ADDED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml:7:18-61
uses-sdk
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\CAPSTONE\mobile_ionic\android\capacitor-cordova-android-plugins\src\main\AndroidManifest.xml
